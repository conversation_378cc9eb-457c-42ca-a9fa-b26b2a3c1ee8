"use server";

import { verifyAdminAccess } from "@/lib/services/admin-service";

/**
 * Type for user activity
 */
export type UserActivity = {
  id: string;
  user_id: string;
  action: string;
  details?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
};

/**
 * Fetch user activities
 */
export async function fetchUserActivities(userId: string) {
  try {
    // Verify admin access
    const adminCheck = await verifyAdminAccess();
    if (!adminCheck.success) {
      return { success: false, error: "Admin access required" };
    }

    // This is a placeholder function
    // In a real implementation, you would fetch from a user_activities table

    // Generate mock data for now
    const mockActivities: UserActivity[] = [
      {
        id: "1",
        user_id: userId,
        action: "login",
        details: "Successful login",
        ip_address: "***********",
        user_agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
        created_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
      },
      {
        id: "2",
        user_id: userId,
        action: "profile_update",
        details: "Updated profile information",
        ip_address: "***********",
        user_agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
        created_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
      },
      {
        id: "3",
        user_id: userId,
        action: "password_reset",
        details: "Requested password reset",
        ip_address: "***********",
        user_agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
        created_at: new Date(
          Date.now() - 1000 * 60 * 60 * 24 * 3,
        ).toISOString(), // 3 days ago
      },
      {
        id: "4",
        user_id: userId,
        action: "login",
        details: "Successful login",
        ip_address: "***********",
        user_agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
        created_at: new Date(
          Date.now() - 1000 * 60 * 60 * 24 * 7,
        ).toISOString(), // 7 days ago
      },
      {
        id: "5",
        user_id: userId,
        action: "subscription_change",
        details: "Changed subscription to Pro plan",
        ip_address: "***********",
        user_agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
        created_at: new Date(
          Date.now() - 1000 * 60 * 60 * 24 * 14,
        ).toISOString(), // 14 days ago
      },
    ];

    return {
      success: true,
      data: mockActivities,
    };
  } catch (error) {
    console.error("Error fetching user activities:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Fetch activity statistics
 */
export async function fetchActivityStats(timeRange: "7" | "30" | "90" = "30") {
  try {
    // Verify admin access
    const adminCheck = await verifyAdminAccess();
    if (!adminCheck.success) {
      return { success: false, error: "Admin access required" };
    }

    // This is a placeholder function
    // In a real implementation, you would fetch and aggregate data from a user_activities table

    // Generate mock data based on time range
    const days = parseInt(timeRange);
    const loginCount = Math.floor(Math.random() * days * 5) + days * 2;
    const signupCount = Math.floor((Math.random() * days) / 2) + days / 4;
    const profileUpdateCount = Math.floor(Math.random() * days) + days / 2;
    const subscriptionChangeCount =
      Math.floor((Math.random() * days) / 4) + days / 8;

    // Generate daily data for charts
    const dailyData = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dateString = date.toISOString().split("T")[0];

      dailyData.push({
        date: dateString,
        logins: Math.floor(Math.random() * 10) + 1,
        signups: Math.floor(Math.random() * 3),
        profileUpdates: Math.floor(Math.random() * 5),
        subscriptionChanges: Math.floor(Math.random() * 2),
      });
    }

    return {
      success: true,
      data: {
        summary: {
          loginCount,
          signupCount,
          profileUpdateCount,
          subscriptionChangeCount,
          totalActivities:
            loginCount +
            signupCount +
            profileUpdateCount +
            subscriptionChangeCount,
        },
        dailyData,
      },
    };
  } catch (error) {
    console.error("Error fetching activity stats:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}
