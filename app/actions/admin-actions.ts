"use server";

import { revalidatePath } from "next/cache";
import { supabase } from "@/lib/supabase";
import { verifyAdminAccess } from "@/lib/services/admin-service";

/**
 * Type for user form data
 */
export type UserFormData = {
  email: string;
  display_name?: string;
  first_name?: string;
  last_name?: string;
  status?: string;
  selected_plan?: string;
  referral_source?: string;
  roles?: {
    admin?: boolean;
    parent?: boolean;
    teacher?: boolean;
    student?: boolean;
    performer?: boolean;
  };
};

/**
 * Type for beta signup form data
 * This is used by the batch actions modal
 */
export type BetaSignupFormData = {
  email: string;
  name?: string;
  status?: string;
  source?: string;
  notes?: string;
  created_at?: string;
  active?: boolean;
  waitlist?: boolean;
  selected_plan?: string;
};

/**
 * Batch update users
 */
export async function batchUpdateUsers(
  ids: string[],
  updates: Partial<UserFormData>,
) {
  try {
    // Verify admin access
    const adminCheck = await verifyAdminAccess();
    if (!adminCheck.success) {
      return { success: false, error: "Admin access required" };
    }

    // Update profiles
    const profileUpdates: Record<string, any> = {};

    if (updates.email) profileUpdates.email = updates.email;
    if (updates.display_name)
      profileUpdates.display_name = updates.display_name;
    if (updates.first_name) profileUpdates.first_name = updates.first_name;
    if (updates.last_name) profileUpdates.last_name = updates.last_name;
    if (updates.status) profileUpdates.status = updates.status;
    if (updates.selected_plan)
      profileUpdates.selected_plan = updates.selected_plan;
    if (updates.referral_source)
      profileUpdates.referral_source = updates.referral_source;

    if (Object.keys(profileUpdates).length > 0) {
      const { error: profileError } = await supabase
        .from("profiles")
        .update(profileUpdates)
        .in("id", ids);

      if (profileError) {
        throw new Error(`Error updating profiles: ${profileError.message}`);
      }
    }

    // Update roles if provided
    if (updates.roles) {
      for (const id of ids) {
        const { error: roleError } = await supabase
          .from("user_roles")
          .update(updates.roles)
          .eq("id", id);

        if (roleError) {
          console.error(
            `Error updating roles for user ${id}: ${roleError.message}`,
          );
        }
      }
    }

    revalidatePath("/admin/dashboard");

    return { success: true };
  } catch (error) {
    console.error("Error in batch update:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Batch update beta signups
 * This is used by the batch actions modal
 */
export async function batchUpdateSignups(
  ids: string[],
  updates: Partial<BetaSignupFormData>,
) {
  try {
    // Verify admin access
    const adminCheck = await verifyAdminAccess();
    if (!adminCheck.success) {
      return { success: false, error: "Admin access required" };
    }

    // For now, we'll just return success since we don't have the beta_signups table
    // In a real implementation, you would update the beta_signups table

    revalidatePath("/admin/dashboard");

    return { success: true, message: "Batch update completed successfully" };
  } catch (error) {
    console.error("Error in batch update signups:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Import users from CSV
 */
export async function importUsersFromCSV(userData: UserFormData[]) {
  try {
    // Verify admin access
    const adminCheck = await verifyAdminAccess();
    if (!adminCheck.success) {
      return { success: false, error: "Admin access required" };
    }

    let successCount = 0;
    let errorCount = 0;

    for (const user of userData) {
      try {
        // Create auth user (in a real app, you would use admin APIs)
        // For now, we'll just create the profile

        // Generate a random ID for demo purposes
        const userId = Math.random().toString(36).substring(2, 15);

        // Create profile
        const { error: profileError } = await supabase.from("profiles").insert({
          id: userId,
          email: user.email,
          display_name: user.display_name,
          first_name: user.first_name,
          last_name: user.last_name,
          status: user.status || "Active",
          selected_plan: user.selected_plan || "free",
          referral_source: user.referral_source,
        });

        if (profileError) {
          throw new Error(`Error creating profile: ${profileError.message}`);
        }

        // Create roles
        const { error: roleError } = await supabase.from("user_roles").insert({
          id: userId,
          admin: user.roles?.admin || false,
          parent: user.roles?.parent || false,
          teacher: user.roles?.teacher || false,
          student: user.roles?.student || false,
          performer: user.roles?.performer || false,
        });

        if (roleError) {
          throw new Error(`Error creating roles: ${roleError.message}`);
        }

        successCount++;
      } catch (userError) {
        console.error(`Error importing user ${user.email}:`, userError);
        errorCount++;
      }
    }

    revalidatePath("/admin/dashboard");

    return {
      success: true,
      message: `Imported ${successCount} users successfully. ${errorCount} failed.`,
    };
  } catch (error) {
    console.error("Error in import users:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Import beta signups from CSV
 */
export async function importSignupsFromCSV(signupData: BetaSignupFormData[]) {
  try {
    // Verify admin access
    const adminCheck = await verifyAdminAccess();
    if (!adminCheck.success) {
      return { success: false, error: "Admin access required" };
    }

    // For now, we'll just return success since we don't have the beta_signups table
    // In a real implementation, you would insert the signups into the beta_signups table

    return {
      success: true,
      message: `Successfully imported ${signupData.length} signups`,
    };
  } catch (error) {
    console.error("Error importing signups:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Add a new beta signup
 */
export async function addBetaSignup(formData: any) {
  try {
    // Verify admin access
    const adminCheck = await verifyAdminAccess();
    if (!adminCheck.success) {
      return { success: false, error: "Admin access required" };
    }

    // For now, we'll just return success since we don't have the beta_signups table
    // In a real implementation, you would insert the signup into the beta_signups table

    return {
      success: true,
      message: "Beta signup added successfully",
    };
  } catch (error) {
    console.error("Error adding beta signup:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Edit an existing beta signup
 */
export async function editBetaSignup(id: string, formData: any) {
  try {
    // Verify admin access
    const adminCheck = await verifyAdminAccess();
    if (!adminCheck.success) {
      return { success: false, error: "Admin access required" };
    }

    // For now, we'll just return success since we don't have the beta_signups table
    // In a real implementation, you would update the signup in the beta_signups table

    return {
      success: true,
      message: "Beta signup updated successfully",
    };
  } catch (error) {
    console.error("Error editing beta signup:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}
