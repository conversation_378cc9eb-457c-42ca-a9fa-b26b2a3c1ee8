"use server";

import { createServerActionClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { revalidatePath } from "next/cache";
import {
  createSong as executeCreateSong,
  <PERSON>,
  SongCreationData,
} from "@/lib/queries/songQueries"; // Assuming SongCreationData is Omit<Song, ...>

export async function addSongAction(songData: SongCreationData): Promise<{
  error?: string;
  data?: Song;
}> {
  try {
    const cookieStore = cookies();
    const supabase = createServerActionClient({ cookies: () => cookieStore });

    // Double check session directly in action, though createSong will also check
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();
    if (sessionError || !session) {
      console.error(
        "Server Action: No active session or error getting session",
        sessionError,
      );
      return { error: "Authentication required: No active session." };
    }

    // Ensure user_id in songData matches the authenticated user
    if (songData.user_id !== session.user.id) {
      console.error(
        `Server Action: Critical Auth Mismatch - songData.user_id (${songData.user_id}) vs session user_id (${session.user.id}).`,
      );
      return { error: "User authentication mismatch. Cannot create song." };
    }

    const newSong = await executeCreateSong(supabase, songData);

    // Revalidate paths if needed, e.g., the songs list page
    revalidatePath("/songs");
    // Potentially revalidate other paths where this new song might appear

    return { data: newSong };
  } catch (error: any) {
    console.error("Error in addSongAction:", error.message);
    return { error: `Failed to create song: ${error.message}` };
  }
}
