"use server";

import { redirect } from "next/navigation";
import { stripe } from "@/lib/stripe";
import { createAdminClient } from "@/lib/supabase";

export async function redirectToCheckout(formData: FormData) {
  const userId = formData.get("userId") as string;
  const priceId = formData.get("priceId") as string;
  const planType = formData.get("planType") as string;
  const customerEmail = formData.get("customerEmail") as string;
  const customerName = formData.get("customerName") as string;

  let sessionUrl: string | null = null;

  try {
    // Always create a new Stripe customer, since stripe_customer_id is not stored in subscriptions
    const customer = await stripe.customers.create({
      email: customerEmail,
      name: customerName,
      metadata: {
        user_id: userId,
      },
    });
    const customerId = customer.id;

    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ["card"],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: "subscription",
      success_url: `${process.env.NEXT_PUBLIC_BASE_URL}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/subscription/canceled`,
      metadata: {
        user_id: userId,
        plan_type: planType,
      },
    });

    sessionUrl = session.url;
  } catch (error) {
    console.error(
      "Error during Stripe operations (customer or session creation):",
      error,
    );
    throw new Error("Failed to process Stripe checkout.");
  }

  if (sessionUrl) {
    redirect(sessionUrl); // This will throw NEXT_REDIRECT and be handled by Next.js
  } else {
    console.error(
      "Stripe session URL was not available after successful operation.",
    );
    throw new Error("Failed to get Stripe session URL.");
  }
}

export async function redirectToBillingPortal(formData: FormData) {
  const customerId = formData.get("customerId") as string;

  try {
    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: `${process.env.NEXT_PUBLIC_BASE_URL}/settings#subscriptions`,
    });

    if (session.url) {
      redirect(session.url);
    }
  } catch (error) {
    console.error("Error creating billing portal session:", error);
    throw new Error("Failed to create billing portal session");
  }
}
