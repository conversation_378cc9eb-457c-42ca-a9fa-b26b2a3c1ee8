"use server";

import <PERSON><PERSON> from "stripe";
import { redirect } from "next/navigation";
import { createAdminClient } from "@/lib/supabase";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2025-05-28.basil",
});

interface EnterpriseSubscriptionData {
  customerId: string;
  basePriceId: string;
  planType: string;
  additionalUsers?: number;
  supportStaff?: number;
  spaces?: number;
  representedBands?: number;
  billingInterval: "monthly" | "yearly";
  metadata?: Record<string, string>;
}

export async function createEnterpriseSubscription(
  data: EnterpriseSubscriptionData,
) {
  const {
    customerId,
    basePriceId,
    planType,
    additionalUsers = 0,
    supportStaff = 0,
    spaces = 0,
    representedBands = 0,
    billingInterval,
    metadata = {},
  } = data;

  try {
    // Build subscription items array
    const items: Stripe.SubscriptionCreateParams.Item[] = [
      {
        price: basePriceId, // Base enterprise plan
        quantity: 1,
      },
    ];

    // Add additional users if specified
    if (additionalUsers > 0) {
      items.push({
        price: "price_1R6m2LGIvMj5WXQrJav2nbmp", // $5/month per additional user
        quantity: additionalUsers,
      });
    }

    // Add support staff if specified
    if (supportStaff > 0) {
      items.push({
        price: "price_1R6mKZGIvMj5WXQr0eUx1g4T", // $4/month per support staff
        quantity: supportStaff,
      });
    }

    // Add spaces if specified
    if (spaces > 0) {
      items.push({
        price: "price_1R6mMRGIvMj5WXQr7n8bDjpH", // $10/month per space
        quantity: spaces,
      });
    }

    // Add represented bands if specified (for agents/promoters)
    if (representedBands > 0) {
      items.push({
        price: "price_1R6mNxGIvMj5WXQrRzEwURTM", // $3/month per represented band
        quantity: representedBands,
      });
    }

    // Remove check for promotional eligibility
    let couponId: string | undefined = undefined;

    // Create subscription with multiple items
    const subscriptionParams: Stripe.SubscriptionCreateParams = {
      customer: customerId,
      items,
      payment_behavior: "default_incomplete",
      payment_settings: {
        save_default_payment_method: "on_subscription",
      },
      expand: ["latest_invoice.payment_intent"],
      metadata: {
        plan_type: planType,
        billing_interval: billingInterval,
        additional_users: additionalUsers.toString(),
        support_staff: supportStaff.toString(),
        spaces: spaces.toString(),
        represented_bands: representedBands.toString(),
        promotional_offer: "false",
        ...metadata,
      },
    };

    // Add coupon if present (future-proofing)
    if (couponId) {
      subscriptionParams.discounts = [{ coupon: couponId }];
    }

    const subscription = await stripe.subscriptions.create(subscriptionParams);

    // Store subscription info in database
    await storeSubscriptionInfo(subscription, data);

    // Handle payment flow
    const invoice = subscription.latest_invoice as any;
    const paymentIntent = invoice.payment_intent;

    if (paymentIntent && paymentIntent.status === "requires_payment_method") {
      redirect(
        `/subscription/payment?subscription_id=${subscription.id}&client_secret=${paymentIntent.client_secret}`,
      );
    } else {
      redirect(`/subscription/success?subscription_id=${subscription.id}`);
    }
  } catch (error: any) {
    console.error("Error creating enterprise subscription:", error);
    throw new Error("Failed to create subscription");
  }
}

export async function updateEnterpriseSubscription(
  subscriptionId: string,
  newAddOns: Array<{ priceId: string; quantity: number }>,
) {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    // Prepare items for update
    const items: Stripe.SubscriptionUpdateParams.Item[] = [];

    // Keep base plan item (always quantity 1)
    const baseItem = subscription.items.data.find(
      (item) => item.price.unit_amount === 5900, // $59 base plans
    );
    if (baseItem) {
      items.push({ id: baseItem.id, quantity: 1 });
    }

    // Update or add new add-on items
    newAddOns.forEach((addOn) => {
      const existingItem = subscription.items.data.find(
        (item) => item.price.id === addOn.priceId,
      );

      if (existingItem) {
        // Update existing item
        if (addOn.quantity > 0) {
          items.push({ id: existingItem.id, quantity: addOn.quantity });
        } else {
          // Delete item by setting deleted: true
          items.push({ id: existingItem.id, deleted: true });
        }
      } else if (addOn.quantity > 0) {
        // Add new item
        items.push({ price: addOn.priceId, quantity: addOn.quantity });
      }
    });

    // Update the subscription
    const updatedSubscription = await stripe.subscriptions.update(
      subscriptionId,
      {
        items: items,
        proration_behavior: "create_prorations", // Pro-rate the changes
      },
    );

    return updatedSubscription;
  } catch (error: any) {
    console.error("Error updating enterprise subscription:", error);
    throw new Error("Failed to update subscription");
  }
}

// Helper function to get promotional coupon ID
function getPromotionalCoupon(planType: string): string | undefined {
  const promotionalCoupons: Record<string, string> = {
    "individual-pro": "q1FXC3qq", // 90-day trial ✅
    "enterprise-band": "2cYMoMxK", // Unlimited users first year ✅
    "enterprise-studio": "KDlmpuKT", // Unlimited rooms 3 months ✅
    "enterprise-school": "pnDveKev", // Free staff 3 months ✅
  };

  return promotionalCoupons[planType];
}

// Helper function to store subscription info in database
async function storeSubscriptionInfo(
  subscription: Stripe.Subscription,
  data: EnterpriseSubscriptionData,
) {
  const supabase = createAdminClient();

  try {
    const { error } = await supabase.from("subscriptions").insert({
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer as string,
      plan_type: data.planType,
      billing_interval: data.billingInterval,
      status: subscription.status,
      current_period_start: subscription.items.data[0]?.current_period_start
        ? new Date(subscription.items.data[0].current_period_start * 1000)
        : null,
      current_period_end: subscription.items.data[0]?.current_period_end
        ? new Date(subscription.items.data[0].current_period_end * 1000)
        : null,
      metadata: {
        additional_users: data.additionalUsers,
        support_staff: data.supportStaff,
        spaces: data.spaces,
        represented_bands: data.representedBands,
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });

    if (error) {
      console.error("Error storing subscription info:", error);
    }
  } catch (error: any) {
    console.error("Error storing subscription info:", error);
  }
}

// Helper function to calculate total subscription cost
export function calculateEnterpriseSubscriptionTotal(
  basePriceAmount: number,
  additionalUsers: number = 0,
  supportStaff: number = 0,
  spaces: number = 0,
  representedBands: number = 0,
): number {
  let total = basePriceAmount; // Base plan cost

  // Add-on costs (in cents)
  total += additionalUsers * 500; // $5 per additional user
  total += supportStaff * 400; // $4 per support staff
  total += spaces * 1000; // $10 per space
  total += representedBands * 300; // $3 per represented band

  return total;
}

// Helper function to format subscription breakdown for display
export function formatSubscriptionBreakdown(
  basePlanName: string,
  basePriceAmount: number,
  additionalUsers: number = 0,
  supportStaff: number = 0,
  spaces: number = 0,
  representedBands: number = 0,
) {
  const items = [
    {
      name: basePlanName,
      quantity: 1,
      unitPrice: basePriceAmount,
      total: basePriceAmount,
    },
  ];

  if (additionalUsers > 0) {
    items.push({
      name: "Additional Users",
      quantity: additionalUsers,
      unitPrice: 500,
      total: additionalUsers * 500,
    });
  }

  if (supportStaff > 0) {
    items.push({
      name: "Support Staff",
      quantity: supportStaff,
      unitPrice: 400,
      total: supportStaff * 400,
    });
  }

  if (spaces > 0) {
    items.push({
      name: "Spaces/Rooms",
      quantity: spaces,
      unitPrice: 1000,
      total: spaces * 1000,
    });
  }

  if (representedBands > 0) {
    items.push({
      name: "Represented Bands",
      quantity: representedBands,
      unitPrice: 300,
      total: representedBands * 300,
    });
  }

  const grandTotal = items.reduce((sum, item) => sum + item.total, 0);

  return { items, grandTotal };
}
