"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { QRActivation } from "@/components/account/qr-activation";
import { useAppStore } from "@/lib/store";
import { Loader2 } from "lucide-react";

export default function ActivatePage() {
  const router = useRouter();
  const currentUser = useAppStore((state) => state.authUser);
  const [isLoading, setIsLoading] = useState(true);
  const [isActivated, setIsActivated] = useState(false);

  // Check if the device is already activated
  useEffect(() => {
    const checkActivation = async () => {
      if (!currentUser) {
        setIsLoading(false);
        return;
      }

      try {
        // Check if there's an activation record for this user
        const response = await fetch(`/api/child/check-activation`, {
          method: "GET",
        });

        const data = await response.json();
        setIsActivated(data.activated);
      } catch (error) {
        console.error("Error checking activation status:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkActivation();
  }, [currentUser]);

  const handleActivationSuccess = () => {
    setIsActivated(true);
  };

  const handleContinue = () => {
    router.push("/dashboard");
  };

  if (isLoading) {
    return (
      <div className="flex h-[400px] items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!currentUser) {
    return (
      <div className="flex h-[400px] items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <h2 className="text-lg font-semibold">Please log in</h2>
              <p className="text-sm text-muted-foreground mt-2">
                You need to be logged in to activate your device.
              </p>
              <Button className="mt-4" onClick={() => router.push("/login")}>
                Log In
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl py-6">
      <h1 className="text-3xl font-bold tracking-tight mb-6">
        Device Activation
      </h1>

      {isActivated ? (
        <Card>
          <CardHeader>
            <CardTitle>Device Activated</CardTitle>
            <CardDescription>
              Your device has been successfully activated.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center py-6">
              <p className="mb-4 text-center">
                You can now start using all features of the Crescender app.
              </p>
              <Button onClick={handleContinue}>Continue to Dashboard</Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          <p className="text-muted-foreground mb-6">
            To activate your device, have your parent scan the QR code below.
          </p>
          <QRActivation onSuccess={handleActivationSuccess} />
        </>
      )}
    </div>
  );
}
