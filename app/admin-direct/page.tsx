"use client";

import React, { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { useAppStore } from "@/lib/store";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Shield, ArrowRight, RefreshCw } from "lucide-react";
import Link from "next/link";
import {
  setupDirectAdminAccess,
  getAdminBypass,
  isAdminBypassValid,
} from "@/lib/services/admin-service";

export default function AdminDirectPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [userId, setUserId] = useState<string>("");
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [directAuthCheck, setDirectAuth<PERSON>heck] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Get store actions
  const setCurrentAuthUser = useAppStore((state) => state.setCurrentAuthUser);
  const setCurrentProfile = useAppStore((state) => state.setCurrentProfile);
  const setCoreUserData = useAppStore((state) => state.setCoreUserData);

  // Check current session
  useEffect(() => {
    const checkSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        if (data.session) {
          setSessionInfo(data.session);
          setUserId(data.session.user.id);
        }
      } catch (err) {
        console.error("Error checking session:", err);
      }
    };

    checkSession();
  }, []);

  // Direct login with user ID
  const handleDirectAccess = async () => {
    if (!userId) {
      setError("Please enter a user ID");
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Use our centralized admin service to set up direct admin access
      const result = await setupDirectAdminAccess(userId);

      if (!result.success) {
        throw new Error(result.error || "Failed to set up admin access");
      }

      // Set the direct auth check state for UI feedback
      setDirectAuthCheck(result.data);

      // Set success message
      setSuccess("Admin access granted! You can now access the admin panel.");
    } catch (err) {
      console.error("Direct access error:", err);
      setError(err instanceof Error ? err.message : String(err));
      setDirectAuthCheck(null);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="container max-w-md">
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="text-center">Direct Admin Access</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {error && (
                <div className="bg-red-50 p-4 rounded-md text-red-800">
                  <p>{error}</p>
                </div>
              )}

              {success && (
                <div className="bg-green-50 p-4 rounded-md text-green-800">
                  <p>{success}</p>
                  <div className="mt-4 flex justify-center">
                    <Link href="/admin">
                      <Button>
                        Go to Admin Panel
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="userId">Admin User ID</Label>
                <Input
                  id="userId"
                  placeholder="Enter your admin user ID"
                  value={userId}
                  onChange={(e) => setUserId(e.target.value)}
                />
                <p className="text-sm text-gray-500">
                  Enter your admin user ID to directly access the admin panel.
                </p>
              </div>

              <Button
                onClick={handleDirectAccess}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Checking...
                  </>
                ) : (
                  <>
                    <Shield className="mr-2 h-4 w-4" />
                    Access Admin Panel
                  </>
                )}
              </Button>

              {sessionInfo && (
                <div className="mt-4 p-3 bg-gray-100 rounded-md">
                  <p className="text-sm font-medium">Current Session:</p>
                  <p className="text-xs text-gray-600">
                    User ID: {sessionInfo.user.id}
                  </p>
                  <p className="text-xs text-gray-600">
                    Email: {sessionInfo.user.email}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2 w-full"
                    onClick={() => (window.location.href = "/admin")}
                  >
                    <RefreshCw className="mr-2 h-3 w-3" />
                    Try Normal Admin Access
                  </Button>
                </div>
              )}

              {directAuthCheck && (
                <div className="mt-4 p-3 bg-gray-100 rounded-md">
                  <p className="text-sm font-medium">Direct Auth Check:</p>
                  <p className="text-xs text-gray-600">
                    User: {directAuthCheck.user?.id}
                  </p>
                  <p className="text-xs text-gray-600">
                    Profile: {directAuthCheck.profile?.display_name}
                  </p>
                  <p className="text-xs text-gray-600">
                    Admin: {directAuthCheck.roles?.admin ? "Yes" : "No"}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
