"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAppStore } from "@/lib/store";
import { GearBrand } from "@/lib/types/gear";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Loader2, ArrowLeft, Check, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";

export default function AdminBrandsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { authUser } = useAppStore();
  const [isLoading, setIsLoading] = useState(true);
  const [brands, setBrands] = useState<GearBrand[]>([]);
  const [customOnly, setCustomOnly] = useState(true);
  const [minUsage, setMinUsage] = useState(1);
  const [isUpdating, setIsUpdating] = useState<number | null>(null);

  useEffect(() => {
    const checkAdminAccess = async () => {
      if (!authUser) return;

      try {
        const response = await fetch(
          "/api/admin/brands?custom_only=" +
            customOnly +
            "&min_usage=" +
            minUsage,
        );

        if (response.status === 403) {
          toast({
            title: "Access Denied",
            description: "You don't have permission to access this page.",
            variant: "destructive",
          });
          router.push("/");
          return;
        }

        if (!response.ok) {
          throw new Error("Failed to fetch brands");
        }

        const data = await response.json();
        setBrands(data);
      } catch (error) {
        console.error("Error checking admin access:", error);
        toast({
          title: "Error",
          description: "Failed to load brand data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminAccess();
  }, [authUser, router, toast, customOnly, minUsage]);

  const handlePromoteToBrand = async (brandId: number) => {
    if (!authUser) return;

    setIsUpdating(brandId);

    try {
      const response = await fetch("/api/admin/brands", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: brandId,
          is_standard: true,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update brand");
      }

      // Update the local state
      setBrands((prev) =>
        prev.map((brand) =>
          brand.id === brandId ? { ...brand, is_standard: true } : brand,
        ),
      );

      toast({
        title: "Success",
        description: "Brand has been promoted to standard brand.",
      });
    } catch (error) {
      console.error("Error updating brand:", error);
      toast({
        title: "Error",
        description: "Failed to update brand. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(null);
    }
  };

  if (!authUser) {
    return (
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold">Admin: Brand Management</h1>
        <p className="mt-4">Please log in to access this page.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push("/admin")}
          className="mr-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Admin
        </Button>
        <h1 className="text-3xl font-bold">Brand Management</h1>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Brand Filters</CardTitle>
          <CardDescription>
            Filter the list of brands to find candidates for promotion to
            standard brands.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="space-y-2 flex-1">
              <div className="flex items-center space-x-2">
                <Switch
                  id="custom-only"
                  checked={customOnly}
                  onCheckedChange={setCustomOnly}
                />
                <Label htmlFor="custom-only">Show custom brands only</Label>
              </div>
            </div>

            <div className="space-y-2 w-full md:w-48">
              <Label htmlFor="min-usage">Minimum usage count</Label>
              <Select
                value={minUsage.toString()}
                onValueChange={(value) => setMinUsage(parseInt(value))}
              >
                <SelectTrigger id="min-usage">
                  <SelectValue placeholder="Select minimum usage" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 or more</SelectItem>
                  <SelectItem value="2">2 or more</SelectItem>
                  <SelectItem value="5">5 or more</SelectItem>
                  <SelectItem value="10">10 or more</SelectItem>
                  <SelectItem value="20">20 or more</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Brand List</CardTitle>
          <CardDescription>
            {customOnly
              ? "User-defined custom brands that may be candidates for promotion to standard brands."
              : "All brands in the system, including standard and custom brands."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : brands.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground">
                No brands found matching the current filters.
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Brand Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead className="text-right">Usage Count</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {brands.map((brand) => (
                    <TableRow key={brand.id}>
                      <TableCell className="font-medium">
                        {brand.name}
                      </TableCell>
                      <TableCell>
                        {brand.is_standard ? (
                          <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-100">
                            <Check className="mr-1 h-3 w-3" />
                            Standard
                          </span>
                        ) : (
                          <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-100">
                            Custom
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        {brand.usage_count}
                      </TableCell>
                      <TableCell>
                        {format(new Date(brand.created_at), "MMM d, yyyy")}
                      </TableCell>
                      <TableCell className="text-right">
                        {!brand.is_standard && (
                          <Button
                            size="sm"
                            onClick={() => handlePromoteToBrand(brand.id)}
                            disabled={isUpdating === brand.id}
                          >
                            {isUpdating === brand.id ? (
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                              <Check className="mr-2 h-4 w-4" />
                            )}
                            Promote to Standard
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
