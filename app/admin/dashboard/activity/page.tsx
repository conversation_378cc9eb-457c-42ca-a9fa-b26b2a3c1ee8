"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ActivityMetrics } from "@/components/admin/dashboard/activity-metrics";
import { ActivityCharts } from "@/components/admin/dashboard/activity-charts";
import { SignupStats } from "@/components/admin/dashboard/signup-stats";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { verifyAdminAccess } from "@/lib/services/admin-service";

export default function ActivityDashboard() {
  const router = useRouter();
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [timeRange, setTimeRange] = useState<"7" | "30" | "90" | "all">("30");
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Check admin access
  useEffect(() => {
    const checkAdminAccess = async () => {
      setIsLoading(true);
      const adminResult = await verifyAdminAccess();
      setIsAdmin(adminResult.success);
      setIsLoading(false);
    };

    checkAdminAccess();
  }, []);

  const handleRefresh = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto py-8 px-4">
          <div className="bg-white rounded-lg shadow-lg p-6 text-center">
            <h2 className="text-xl font-semibold mb-4">
              Admin Access Required
            </h2>
            <p className="text-gray-600 mb-4">
              You need admin privileges to access this dashboard.
            </p>
            <Button onClick={() => router.push("/admin")}>
              Return to Admin Panel
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-2xl font-bold">Activity Dashboard</h1>
          <div className="flex items-center gap-2">
            <Tabs
              value={timeRange}
              onValueChange={(v) =>
                setTimeRange(v as "7" | "30" | "90" | "all")
              }
            >
              <TabsList>
                <TabsTrigger value="7">7 Days</TabsTrigger>
                <TabsTrigger value="30">30 Days</TabsTrigger>
                <TabsTrigger value="90">90 Days</TabsTrigger>
                <TabsTrigger value="all">All Time</TabsTrigger>
              </TabsList>
            </Tabs>
            <Button variant="outline" onClick={handleRefresh}>
              Refresh Data
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push("/admin/dashboard")}
            >
              Back to Dashboard
            </Button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          {/* Signup Statistics */}
          <div className="mb-8">
            <SignupStats
              refreshTrigger={refreshTrigger}
              timeRange={
                timeRange === "7"
                  ? "last7Days"
                  : timeRange === "30"
                    ? "last30Days"
                    : timeRange === "90"
                      ? "last90Days"
                      : "allTime"
              }
            />
          </div>

          <hr className="my-8" />

          {/* User Activity */}
          <ActivityMetrics
            refreshTrigger={refreshTrigger}
            timeRange={timeRange === "all" ? "90" : timeRange}
          />

          <div className="mt-8">
            <ActivityCharts
              refreshTrigger={refreshTrigger}
              timeRange={timeRange === "all" ? "90" : timeRange}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
