"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  CreditCard,
  DollarSign,
  TrendingUp,
  Users,
  AlertCircle,
  Download,
  RefreshCw,
  Eye,
} from "lucide-react";
import {
  getSubscriptionStats,
  getAllActiveSubscriptions,
} from "@/lib/services/subscription-service";
import { formatCurrency } from "@/lib/config/stripe-config";
import { verifyAdminAccess } from "@/lib/services/admin-service";
import type { SubscriptionWithUser } from "@/lib/services/subscription-service";

export default function AdminBillingPage() {
  const router = useRouter();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithUser[]>(
    [],
  );
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    trialing: 0,
    canceled: 0,
    revenue: {
      monthly: 0,
      yearly: 0,
    },
    planBreakdown: {} as Record<string, number>,
  });

  const checkAdminAccess = useCallback(async () => {
    const adminResult = await verifyAdminAccess();
    setIsAdmin(adminResult.success);

    if (adminResult.success) {
      await Promise.all([fetchSubscriptionStats(), fetchActiveSubscriptions()]);
    }
    setIsLoading(false);
  }, []);

  useEffect(() => {
    checkAdminAccess();
  }, [checkAdminAccess]);

  const fetchSubscriptionStats = async () => {
    try {
      const statsData = await getSubscriptionStats();
      // Transform the incoming data to match the existing state structure
      const transformedData = {
        total: statsData.totalSubscriptions || 0,
        active: statsData.activeSubscriptions || 0,
        trialing: statsData.trialSubscriptions || 0,
        canceled: statsData.canceledSubscriptions || 0,
        revenue: {
          monthly: statsData.revenueThisMonth || 0,
          yearly: 0, // Assuming yearly is not directly provided by this endpoint or needs separate calculation
        },
        planBreakdown: statsData.planDistribution || {},
      };
      setStats(transformedData);
    } catch (error) {
      console.error("Error fetching subscription stats:", error);
    }
  };

  const fetchActiveSubscriptions = async () => {
    try {
      const subscriptionsData = await getAllActiveSubscriptions();
      setSubscriptions(subscriptionsData || []); // Assuming it returns an array or null/undefined
    } catch (error) {
      console.error("Error fetching active subscriptions:", error);
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    await Promise.all([fetchSubscriptionStats(), fetchActiveSubscriptions()]);
    setIsLoading(false);
  };

  const exportSubscriptions = () => {
    if (subscriptions.length === 0) return;

    const headers = [
      "User Name",
      "Email",
      "Plan",
      "Status",
      "Billing Interval",
      "Current Period End",
      "Created At",
    ];
    const csvRows = [
      headers.join(","),
      ...subscriptions.map((sub) =>
        [
          sub.user_name || "",
          sub.user_email || "",
          sub.plan_type || "",
          sub.status || "",
          sub.billing_interval || "",
          sub.current_period_end
            ? new Date(sub.current_period_end).toLocaleDateString()
            : "",
          sub.created_at
            ? new Date(sub.created_at).toLocaleDateString()
            : "",
        ]
          .map((field) => `"${field}"`)
          .join(","),
      ),
    ].join("\n");

    const blob = new Blob([csvRows], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `subscriptions-${new Date().toISOString().split("T")[0]}.csv`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<
      string,
      "default" | "secondary" | "destructive" | "outline"
    > = {
      active: "default",
      trialing: "secondary",
      past_due: "destructive",
      canceled: "outline",
      incomplete: "destructive",
    };
    return variants[status] || "outline";
  };

  const formatPlanName = (planType: string) => {
    return planType
      .replace("enterprise-", "Enterprise - ")
      .replace("individual-pro", "Individual Pro")
      .replace(/\b\w/g, (l) => l.toUpperCase());
  };

  if (!isAdmin) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Access Denied
            </CardTitle>
            <CardDescription>
              You need admin privileges to access the billing dashboard.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => router.push("/admin")}>
              Return to Admin Panel
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Billing Dashboard</h1>
          <p className="text-muted-foreground">
            Manage subscriptions and billing analytics
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push("/admin/dashboard")}
          >
            Back to Dashboard
          </Button>
        </div>
      </div>

      {/* Revenue Overview */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Subscriptions
            </CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.active} active, {stats.trialing} trialing
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Monthly Revenue
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(stats.revenue.monthly)}
            </div>
            <p className="text-xs text-muted-foreground">
              Estimated monthly recurring revenue
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Conversion Rate
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.total > 0
                ? Math.round(
                    ((stats.active + stats.trialing) / stats.total) * 100,
                  )
                : 0}
              %
            </div>
            <p className="text-xs text-muted-foreground">
              Users with active subscriptions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.active}</div>
            <p className="text-xs text-muted-foreground">Paying customers</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="subscriptions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="subscriptions">Active Subscriptions</TabsTrigger>
          <TabsTrigger value="analytics">Plan Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="subscriptions" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Active Subscriptions</CardTitle>
                  <CardDescription>
                    Manage all active and trialing subscriptions
                  </CardDescription>
                </div>
                <Button variant="outline" onClick={exportSubscriptions}>
                  <Download className="h-4 w-4 mr-2" />
                  Export CSV
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {subscriptions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No active subscriptions found.
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>User</TableHead>
                        <TableHead>Plan</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Billing</TableHead>
                        <TableHead>Current Period</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {subscriptions.map((subscription) => (
                        <TableRow key={subscription.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {subscription.user_name || "Unknown"}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {subscription.user_email}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {formatPlanName(subscription.plan_type)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={getStatusBadge(subscription.status || "")}
                            >
                              {subscription.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {subscription.billing_interval || "monthly"}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {subscription.current_period_end
                                ? new Date(
                                    subscription.current_period_end,
                                  ).toLocaleDateString()
                                : "N/A"}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Plan Distribution</CardTitle>
              <CardDescription>
                Breakdown of subscribers by plan type
              </CardDescription>
            </CardHeader>
            <CardContent>
              {Object.keys(stats.planBreakdown).length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No subscription data available.
                </div>
              ) : (
                <div className="space-y-4">
                  {Object.entries(stats.planBreakdown).map(([plan, count]) => (
                    <div
                      key={plan}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div>
                        <div className="font-medium">
                          {formatPlanName(plan)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {Math.round((count / stats.total) * 100)}% of total
                          subscriptions
                        </div>
                      </div>
                      <div className="text-2xl font-bold">{count}</div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
