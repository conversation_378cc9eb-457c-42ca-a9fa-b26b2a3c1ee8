"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDistanceToNow } from "date-fns";
import {
  AlertTriangle,
  BarChart3,
  Clock,
  Download,
  Edit,
  Plus,
  RefreshCw,
  Trash2,
  Upload,
  Users,
  Activity,
} from "lucide-react";
import { AnalyticsPanels } from "@/components/admin/dashboard/analytics-panels";
import { PlanBoxes } from "@/components/admin/dashboard/plan-boxes";
import { SubscriptionAnalytics } from "@/components/admin/dashboard/subscription-analytics";
import { UserManagementModal } from "@/components/admin/dashboard/user-management-modal";
import { ImportUsersModal } from "@/components/admin/dashboard/import-users-modal";
import { BatchActionsModal } from "@/components/admin/dashboard/batch-actions-modal";
import { UserActivityModal } from "@/components/admin/dashboard/user-activity-modal";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { fetchUsers, removeUser } from "@/lib/services/admin-service";
// Define formatLastLogin function inline since there's an issue with the import
const formatLastLogin = (
  timestamp: string,
): { text: string; isOld: boolean } => {
  const date = new Date(timestamp);
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  const isOld = date < thirtyDaysAgo;
  const text = formatDistanceToNow(date, { addSuffix: true });

  return { text, isOld };
};
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { verifyAdminAccess } from "@/lib/services/admin-service";

// Define a user type based on our application's data structure
type UserData = {
  id: string;
  email: string;
  display_name?: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  created_at: string;
  last_login?: string;
  status?: string;
  selected_plan?: string;
  referral_source?: string;
  user_roles?: {
    admin?: boolean;
    parent?: boolean;
    teacher?: boolean;
    student?: boolean;
    performer?: boolean;
  };
};

export default function Dashboard() {
  const router = useRouter();
  const [users, setUsers] = useState<UserData[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  // User management state
  const [userModalOpen, setUserModalOpen] = useState(false);
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [batchModalOpen, setBatchModalOpen] = useState(false);
  const [activityModalOpen, setActivityModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<UserData | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Check admin access
  useEffect(() => {
    const checkAdminAccess = async () => {
      const adminResult = await verifyAdminAccess();
      setIsAdmin(adminResult.success);

      if (adminResult.success) {
        fetchUserData();
      }
    };

    checkAdminAccess();
  }, []);

  useEffect(() => {
    // Handle select all checkbox
    if (selectAll) {
      setSelectedIds(users.map((user) => user.id));
    } else if (selectedIds.length === users.length) {
      // If all were selected and selectAll is toggled off
      setSelectedIds([]);
    }
  }, [selectAll, users, selectedIds.length]);

  const fetchUserData = async () => {
    setIsLoadingUsers(true);

    try {
      const result = await fetchUsers();

      if (result.success && result.data) {
        setUsers(result.data);
      } else {
        console.error("Error fetching users:", result.error);
      }

      // Increment the refresh trigger to update analytics
      setRefreshTrigger((prev) => prev + 1);
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setIsLoadingUsers(false);
    }
  };

  const exportToCSV = async () => {
    if (users.length === 0) return;

    setIsExporting(true);

    try {
      // Define CSV headers
      const headers = [
        "Name",
        "Email",
        "Plan",
        "Referral Source",
        "Signup Date",
        "Status",
        "Last Login",
      ];

      // Format the data for CSV
      const csvRows = [
        headers.join(","), // Header row
        ...users.map((user) => {
          // Format the date
          const date = new Date(user.created_at).toLocaleString();

          // Format the plan name
          let planName = "Free";
          if (user.selected_plan === "individual-pro")
            planName = "Individual Pro";
          if (user.selected_plan === "enterprise") planName = "Enterprise";

          // Format the last login
          const lastLogin = user.last_login
            ? new Date(user.last_login).toLocaleString()
            : "Never";

          // Escape fields that might contain commas
          const escapeCsvField = (field: string) => {
            if (
              field &&
              (field.includes(",") ||
                field.includes('"') ||
                field.includes("\n"))
            ) {
              return `"${field.replace(/"/g, '""')}"`;
            }
            return field || "";
          };

          const name =
            user.display_name ||
            `${user.first_name || ""} ${user.last_name || ""}`.trim();

          return [
            escapeCsvField(name),
            escapeCsvField(user.email),
            escapeCsvField(planName),
            escapeCsvField(user.referral_source || ""),
            escapeCsvField(date),
            escapeCsvField(user.status || "Active"),
            escapeCsvField(lastLogin),
          ].join(",");
        }),
      ].join("\n");

      // Create a blob and download link
      const blob = new Blob([csvRows], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");

      // Set up the download
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `crescender-users-${new Date().toISOString().split("T")[0]}.csv`,
      );
      link.style.visibility = "hidden";

      // Append to the document, click, and clean up
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url); // Clean up the URL object
    } catch (error) {
      console.error("Error exporting to CSV:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleAddUser = () => {
    setSelectedUser(null);
    setUserModalOpen(true);
  };

  const handleEditUser = (user: UserData) => {
    setSelectedUser(user);
    setUserModalOpen(true);
  };

  const handleViewActivity = (user: UserData) => {
    setSelectedUser(user);
    setActivityModalOpen(true);
  };

  const handleDeleteUser = (user: UserData) => {
    setUserToDelete(user);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    setIsDeleting(true);

    try {
      const response = await removeUser(userToDelete.id);

      if (response.success) {
        // Remove from selected IDs if it was selected
        if (selectedIds.includes(userToDelete.id)) {
          setSelectedIds((prev) => prev.filter((id) => id !== userToDelete.id));
        }

        // Refresh the list after deletion
        fetchUserData();
      } else {
        console.error("Error deleting user:", response.error);
      }
    } catch (error) {
      console.error("Error deleting user:", error);
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setUserToDelete(null);
    }
  };

  const handleSelectUser = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds((prev) => [...prev, id]);
    } else {
      setSelectedIds((prev) => prev.filter((userId) => userId !== id));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
  };

  const handleBatchAction = () => {
    if (selectedIds.length > 0) {
      setBatchModalOpen(true);
    }
  };

  const handleImportUsers = () => {
    setImportModalOpen(true);
  };

  const handleViewActivityDashboard = () => {
    router.push("/admin/dashboard/activity");
  };

  // Helper function to get status badge color
  const getStatusBadgeClass = (status: string = "Active") => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Pending":
        return "bg-amber-100 text-amber-800";
      case "Inactive":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Helper function to get initials from name
  const getInitials = (name: string = "") => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto py-8 px-4">
          <div className="bg-white rounded-lg shadow-lg p-6 text-center">
            <h2 className="text-xl font-semibold mb-4">
              Admin Access Required
            </h2>
            <p className="text-gray-600 mb-4">
              You need admin privileges to access this dashboard.
            </p>
            <Button onClick={() => router.push("/admin")}>
              Return to Admin Panel
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-2xl font-bold">Admin Dashboard</h1>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleViewActivityDashboard}
              className="flex items-center gap-2"
            >
              <BarChart3 className="h-4 w-4" />
              Activity Dashboard
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push("/admin")}
              className="flex items-center gap-2"
            >
              Return to Admin Panel
            </Button>
          </div>
        </div>

        <div className="mb-8">
          <AnalyticsPanels refreshTrigger={refreshTrigger} users={users} />
        </div>

        <div className="mb-8">
          <SubscriptionAnalytics refreshTrigger={refreshTrigger} />
        </div>

        <div className="mb-8">
          <PlanBoxes users={users} />
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">
              All Users ({users.length})
            </h2>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={exportToCSV}
                disabled={isExporting || users.length === 0}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {isExporting ? "Exporting..." : "Export to CSV"}
              </Button>
              <Button
                variant="outline"
                onClick={handleImportUsers}
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                Import Users
              </Button>
              <Button
                variant="outline"
                onClick={fetchUserData}
                disabled={isLoadingUsers}
                className="flex items-center gap-2"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isLoadingUsers ? "animate-spin" : ""}`}
                />
                {isLoadingUsers ? "Refreshing..." : "Refresh"}
              </Button>
              <Button
                onClick={handleAddUser}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add User
              </Button>
            </div>
          </div>

          {selectedIds.length > 0 && (
            <div className="mb-4 p-2 bg-muted rounded-md flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span>{selectedIds.length} users selected</span>
              </div>
              <Button size="sm" onClick={handleBatchAction}>
                Batch Actions
              </Button>
            </div>
          )}

          {isLoadingUsers ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4">Loading users...</p>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No users found.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableCaption>List of all users</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">
                      <Checkbox
                        checked={selectAll}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all users"
                      />
                    </TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Roles</TableHead>
                    <TableHead>Signed Up</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Login</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedIds.includes(user.id)}
                          onCheckedChange={(checked) =>
                            handleSelectUser(user.id, checked === true)
                          }
                          aria-label={`Select ${user.display_name || user.email}`}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar>
                            {user.avatar_url ? (
                              <AvatarImage
                                src={user.avatar_url}
                                alt={user.display_name || user.email}
                              />
                            ) : null}
                            <AvatarFallback>
                              {getInitials(
                                user.display_name ||
                                  `${user.first_name || ""} ${user.last_name || ""}`.trim(),
                              )}
                            </AvatarFallback>
                          </Avatar>
                          <span className="font-medium">
                            {user.display_name ||
                              `${user.first_name || ""} ${user.last_name || ""}`.trim() ||
                              user.email}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {user.user_roles?.admin && (
                            <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
                              Admin
                            </span>
                          )}
                          {user.user_roles?.parent && (
                            <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                              Parent
                            </span>
                          )}
                          {user.user_roles?.teacher && (
                            <span className="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded">
                              Teacher
                            </span>
                          )}
                          {user.user_roles?.student && (
                            <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">
                              Student
                            </span>
                          )}
                          {user.user_roles?.performer && (
                            <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                              Performer
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {formatDistanceToNow(new Date(user.created_at), {
                          addSuffix: true,
                        })}
                      </TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeClass(user.status)}`}
                        >
                          {user.status || "Active"}
                        </span>
                      </TableCell>
                      <TableCell>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center">
                                {user.last_login ? (
                                  <>
                                    <Clock className="h-3 w-3 mr-1" />
                                    <span className="text-sm">
                                      {formatLastLogin(user.last_login).text}
                                    </span>
                                    {formatLastLogin(user.last_login).isOld && (
                                      <AlertTriangle className="h-3 w-3 ml-1 text-amber-500" />
                                    )}
                                  </>
                                ) : (
                                  <span className="text-sm text-muted-foreground">
                                    Never
                                  </span>
                                )}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              {user.last_login ? (
                                <>
                                  {formatLastLogin(user.last_login).isOld ? (
                                    <p>
                                      User hasn't logged in for over 30 days
                                    </p>
                                  ) : (
                                    <p>
                                      Last login:{" "}
                                      {new Date(
                                        user.last_login,
                                      ).toLocaleString()}
                                    </p>
                                  )}
                                </>
                              ) : (
                                <p>User has never logged in</p>
                              )}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewActivity(user)}
                            className="h-8 w-8 p-0"
                            title="View Activity"
                          >
                            <span className="sr-only">View Activity</span>
                            <Activity className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditUser(user)}
                            className="h-8 w-8 p-0"
                            title="Edit User"
                          >
                            <span className="sr-only">Edit</span>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteUser(user)}
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50"
                          >
                            <span className="sr-only">Delete</span>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </div>

      {/* User Management Modal */}
      <UserManagementModal
        open={userModalOpen}
        onOpenChange={setUserModalOpen}
        user={selectedUser}
        onSuccess={fetchUserData}
      />

      {/* Import Users Modal */}
      <ImportUsersModal
        open={importModalOpen}
        onOpenChange={setImportModalOpen}
        onSuccess={fetchUserData}
      />

      {/* Batch Actions Modal */}
      <BatchActionsModal
        open={batchModalOpen}
        onOpenChange={setBatchModalOpen}
        selectedIds={selectedIds}
        onSuccess={() => {
          fetchUserData();
          setSelectedIds([]);
          setSelectAll(false);
        }}
      />

      {/* User Activity Modal */}
      <UserActivityModal
        open={activityModalOpen}
        onOpenChange={setActivityModalOpen}
        user={selectedUser}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the user{" "}
              {userToDelete?.display_name || userToDelete?.email}. This action
              cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteUser}
              disabled={isDeleting}
              className="bg-red-500 hover:bg-red-600"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
