"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Loader2,
  Shield,
  Users,
  Database,
  UserCog,
  Settings,
  RefreshCw,
  BarChart3,
} from "lucide-react";
import Link from "next/link";
import { AdminPanel } from "@/components/admin/admin-panel";
import { AdminRolePanel } from "@/components/admin/admin-role-panel";
import { EnhancedTableTester } from "@/components/debug/EnhancedTableTester";
import { PermissionDebugger } from "@/components/debug/PermissionDebugger";
import { AdminDebugger } from "@/components/admin/AdminDebugger";
import { useAppStore } from "@/lib/store";

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState("users");

  // Use Next.js 15 best practice auth state from store
  const authUser = useAppStore((state) => state.authUser);
  const currentProfile = useAppStore((state) => state.currentProfile);
  const roles = useAppStore((state) => state.roles);
  const isLoading = useAppStore((state) => state.isLoading);

  // Add debugging
  console.log("[AdminPage] Auth state:", {
    isAuthenticated: !!authUser,
    isLoading,
    userId: authUser?.id,
    authUser: authUser ? "exists" : "null",
    roles: roles,
  });

  // Check if user has admin role
  const isAdmin = roles?.admin === true;

  console.log("[AdminPage] Admin check:", {
    isAdmin,
    rolesAdmin: roles?.admin,
    userId: authUser?.id,
    hasRoles: !!roles,
  });

  if (isLoading) {
    return (
      <div className="container mx-auto p-4 flex items-center justify-center h-screen">
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin text-purple-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold">Loading...</h2>
          <p className="text-gray-500">Checking your permissions</p>
        </div>
      </div>
    );
  }

  // Standard not authenticated check
  if (!isLoading && !authUser) {
    return (
      <div className="container mx-auto p-4">
        <div className="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md text-center">
          <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-4">Authentication Required</h2>
          <p className="text-gray-600 mb-6">
            You need to be logged in to access the admin panel.
          </p>
          <div className="flex justify-center">
            <Link href="/auth/signin">
              <Button>Log In</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Standard admin check
  if (!isAdmin) {
    return (
      <div className="container mx-auto p-4">
        <div className="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md text-center">
          <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-6">
            You don't have permission to access the admin panel.
          </p>
          <AdminDebugger />
          <div className="flex justify-center mt-4">
            <Link href="/">
              <Button>Return to Home</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Admin Dashboard</h1>
          <div className="flex items-center gap-2">
            <Link
              href="/admin/dashboard"
              className="px-4 py-2 bg-purple-600 text-white rounded flex items-center hover:bg-purple-700"
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              Advanced Dashboard
            </Link>
            <Link
              href="/"
              className="px-4 py-2 bg-gray-600 text-white rounded flex items-center hover:bg-gray-700"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Link>
          </div>
        </div>

        <AdminDebugger />

        <div className="bg-white rounded-lg shadow-lg p-6 mt-6">
          <Tabs
            defaultValue={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="mb-6 grid grid-cols-4 gap-4">
              <TabsTrigger value="users" className="flex items-center">
                <Users className="w-4 h-4 mr-2" />
                Users
              </TabsTrigger>
              <TabsTrigger value="roles" className="flex items-center">
                <UserCog className="w-4 h-4 mr-2" />
                Roles
              </TabsTrigger>
              <TabsTrigger value="database" className="flex items-center">
                <Database className="w-4 h-4 mr-2" />
                Database
              </TabsTrigger>
              <TabsTrigger value="system" className="flex items-center">
                <Settings className="w-4 h-4 mr-2" />
                System
              </TabsTrigger>
            </TabsList>

            <TabsContent value="users" className="space-y-4">
              <AdminPanel />
            </TabsContent>

            <TabsContent value="roles" className="space-y-4">
              <h2 className="text-xl font-semibold mb-4">Role Management</h2>
              <p className="text-gray-600 mb-4">
                Manage user roles and permissions.
              </p>
              <AdminRolePanel />
            </TabsContent>

            <TabsContent value="database" className="space-y-4">
              <h2 className="text-xl font-semibold mb-4">Database Explorer</h2>
              <p className="text-gray-600 mb-4">
                Use this tool to explore database tables and test permissions.
              </p>
              <EnhancedTableTester />
            </TabsContent>

            <TabsContent value="system" className="space-y-4">
              <h2 className="text-xl font-semibold mb-4">System Diagnostics</h2>
              <p className="text-gray-600 mb-4">
                Use these tools to diagnose system issues and test permissions.
              </p>
              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-2">
                  Permission Debugger
                </h3>
                <PermissionDebugger />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
