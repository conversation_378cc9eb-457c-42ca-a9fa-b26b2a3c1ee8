"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Play,
  CheckCircle,
  XCircle,
  Clock,
  Wifi,
  Download,
  Copy,
} from "lucide-react";
import { SpotifyIcon } from "@/components/ui/spotify-button";
import { useToast } from "@/hooks/use-toast";
import type {
  SpotifyTestSuite,
  SpotifyTestResult,
} from "@/lib/tests/spotify-api-test";

export default function SpotifyTestPage() {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<SpotifyTestSuite | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const exportToJSON = () => {
    if (!testResults) return;

    const jsonData = JSON.stringify(testResults, null, 2);
    const blob = new Blob([jsonData], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `spotify-api-test-${new Date().toISOString().split("T")[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "JSON Exported",
      description: "Test results have been downloaded as JSON file.",
    });
  };

  const copyToClipboard = async () => {
    if (!testResults) return;

    try {
      const jsonData = JSON.stringify(testResults, null, 2);
      await navigator.clipboard.writeText(jsonData);
      toast({
        title: "Copied to Clipboard",
        description: "Test results JSON has been copied to your clipboard.",
      });
    } catch (err) {
      toast({
        title: "Copy Failed",
        description:
          "Failed to copy to clipboard. Please try the download option.",
        variant: "destructive",
      });
    }
  };

  const runTests = async () => {
    setIsRunning(true);
    setError(null);
    setTestResults(null);

    try {
      const response = await fetch("/api/spotify/test", {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to run tests");
      }

      const results = await response.json();
      setTestResults(results);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  const getStatusBadge = (success: boolean) => {
    return (
      <Badge variant={success ? "default" : "destructive"}>
        {success ? "PASS" : "FAIL"}
      </Badge>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <SpotifyIcon className="w-8 h-8 text-[#1DB954]" />
            Spotify API Test Suite
          </h1>
          <p className="text-muted-foreground mt-2">
            Comprehensive testing of Spotify API connectivity and functionality
          </p>
        </div>
        <div className="flex items-center gap-2">
          {testResults && (
            <>
              <Button
                onClick={copyToClipboard}
                variant="outline"
                size="sm"
                className="gap-2"
              >
                <Copy className="w-4 h-4" />
                Copy JSON
              </Button>
              <Button
                onClick={exportToJSON}
                variant="outline"
                size="sm"
                className="gap-2"
              >
                <Download className="w-4 h-4" />
                Export JSON
              </Button>
            </>
          )}
          <Button onClick={runTests} disabled={isRunning} className="gap-2">
            {isRunning ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            {isRunning ? "Running Tests..." : "Run Tests"}
          </Button>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <XCircle className="w-5 h-5" />
              <span className="font-medium">Test Error:</span>
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {testResults && (
        <div className="space-y-6">
          {/* Summary Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wifi className="w-5 h-5" />
                Test Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {testResults.summary.passed}
                  </div>
                  <div className="text-sm text-muted-foreground">Passed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {testResults.summary.failed}
                  </div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {testResults.summary.total}
                  </div>
                  <div className="text-sm text-muted-foreground">Total</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {testResults.summary.totalDuration}ms
                  </div>
                  <div className="text-sm text-muted-foreground">Duration</div>
                </div>
              </div>

              {/* Network Diagnostics Summary */}
              {testResults.summary.networkDiagnostics && (
                <div className="border-t pt-4 mt-4">
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Wifi className="w-4 h-4" />
                    Network Diagnostics
                  </h4>
                  <div className="grid grid-cols-3 gap-4 mb-3">
                    <div className="flex items-center gap-2">
                      {testResults.summary.networkDiagnostics
                        .internetConnectivity ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-500" />
                      )}
                      <span className="text-sm">Internet</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {testResults.summary.networkDiagnostics.dnsResolution ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-500" />
                      )}
                      <span className="text-sm">DNS</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {testResults.summary.networkDiagnostics
                        .spotifyDomainAccess ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-500" />
                      )}
                      <span className="text-sm">Spotify Access</span>
                    </div>
                  </div>
                  {testResults.summary.networkDiagnostics.recommendedAction && (
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                      <p className="text-sm text-blue-800">
                        <strong>💡 Recommendation:</strong>{" "}
                        {
                          testResults.summary.networkDiagnostics
                            .recommendedAction
                        }
                      </p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Environment Info */}
          <Card>
            <CardHeader>
              <CardTitle>Environment</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium">Credentials:</span>{" "}
                  <Badge
                    variant={
                      testResults.environment.hasCredentials
                        ? "default"
                        : "destructive"
                    }
                  >
                    {testResults.environment.hasCredentials
                      ? "Configured"
                      : "Missing"}
                  </Badge>
                </div>
                <div>
                  <span className="font-medium">Node Version:</span>{" "}
                  {testResults.environment.nodeVersion}
                </div>
                <div>
                  <span className="font-medium">Platform:</span>{" "}
                  {testResults.environment.platform}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Test Results */}
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testResults.results.map(
                  (result: SpotifyTestResult, index: number) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        {getStatusIcon(result.success)}
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{result.test}</span>
                            {result.category && (
                              <Badge variant="outline" className="text-xs">
                                {result.category}
                              </Badge>
                            )}
                          </div>
                          {result.error && (
                            <div className="text-sm text-red-600 mt-1">
                              {result.error}
                            </div>
                          )}
                          {result.networkDiagnostics?.errorType && (
                            <div className="text-sm text-orange-600 mt-1">
                              Network Error Type:{" "}
                              {result.networkDiagnostics.errorType}
                            </div>
                          )}
                          {result.data && (
                            <div className="text-sm text-muted-foreground mt-1">
                              {typeof result.data === "object"
                                ? JSON.stringify(result.data, null, 2)
                                : result.data}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Clock className="w-3 h-3" />
                          {result.duration}ms
                        </div>
                        {getStatusBadge(result.success)}
                      </div>
                    </div>
                  ),
                )}
              </div>
            </CardContent>
          </Card>

          {/* Timestamp */}
          <div className="text-center text-sm text-muted-foreground">
            Test run completed at{" "}
            {new Date(testResults.timestamp).toLocaleString()}
          </div>
        </div>
      )}

      {!testResults && !isRunning && (
        <Card className="border-dashed">
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              <SpotifyIcon className="w-12 h-12 mx-auto mb-4 text-[#1DB954]" />
              <p>Click "Run Tests" to start the Spotify API test suite</p>
              <p className="text-sm mt-2">
                This will test connectivity, authentication, and all major API
                endpoints
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
