import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: GET /api/admin/brands
 * Retrieves all brands with usage statistics
 * Only accessible to admin users
 *
 * Query Parameters:
 * - custom_only: If "true", only returns custom brands
 * - min_usage: Minimum usage count to include
 *
 * Response:
 * Array of brands with usage statistics or error
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Check if the user is an admin
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("is_admin")
      .eq("id", user.id)
      .single();

    if (profileError || !profile?.is_admin) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 403 },
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const customOnly = searchParams.get("custom_only") === "true";
    const minUsage = parseInt(searchParams.get("min_usage") || "0");

    // Build the query
    let query = supabase
      .from("gear_brands")
      .select("*")
      .order("usage_count", { ascending: false });

    if (customOnly) {
      query = query.eq("is_standard", false);
    }

    if (minUsage > 0) {
      query = query.gte("usage_count", minUsage);
    }

    const { data, error } = await query;

    if (error) throw error;

    return NextResponse.json(data);
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    console.error("Error fetching brands:", error);
    return NextResponse.json(
      { error: errorMessage || "Failed to fetch brands" },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: PATCH /api/admin/brands/:id
 * Updates a brand (e.g., to convert a custom brand to a standard one)
 * Only accessible to admin users
 *
 * Request Body:
 * - is_standard: Boolean indicating if the brand is standard
 *
 * Response:
 * Updated brand or error
 */
export async function PATCH(request: NextRequest) {
  try {
    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Check if the user is an admin
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("is_admin")
      .eq("id", user.id)
      .single();

    if (profileError || !profile?.is_admin) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 403 },
      );
    }

    // Parse the request body and URL parameters
    const { id, is_standard } = await request.json();

    if (!id || typeof is_standard !== "boolean") {
      return NextResponse.json(
        {
          error: "Invalid request. Brand ID and is_standard flag are required.",
        },
        { status: 400 },
      );
    }

    // Update the brand
    const { data, error } = await supabase
      .from("gear_brands")
      .update({ is_standard })
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json(data);
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    console.error("Error updating brand:", error);
    return NextResponse.json(
      { error: errorMessage || "Failed to update brand" },
      { status: 500 },
    );
  }
}
