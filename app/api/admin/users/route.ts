/**
 * API Endpoint: /api/admin/users
 *
 * This endpoint demonstrates the use of the centralized API auth middleware
 * to handle authentication and admin role verification.
 */

import { NextRequest, NextResponse } from "next/server";
import {
  createApiHandlers,
  withAdminAuth,
} from "@/lib/middleware/api-auth-middleware";
import { supabase } from "@/lib/supabase";

/**
 * GET /api/admin/users
 * Retrieves all users (admin only)
 */
async function getUsers(req: NextRequest) {
  try {
    // The auth middleware has already verified that the user is an admin
    // and added the userId to req.auth

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");
    const search = searchParams.get("search") || "";

    // Build the query
    let query = supabase
      .from("profiles")
      .select("*, user_roles(*)")
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    // Add search filter if provided
    if (search) {
      query = query.or(
        `display_name.ilike.%${search}%,email.ilike.%${search}%,username.ilike.%${search}%`,
      );
    }

    const { data, error, count } = await query;

    if (error) throw error;

    return NextResponse.json({
      users: data,
      pagination: {
        total: count || 0,
        limit,
        offset,
      },
    });
  } catch (error: any) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch users" },
      { status: 500 },
    );
  }
}

/**
 * POST /api/admin/users
 * Creates a new user (admin only)
 */
async function createUser(req: NextRequest) {
  try {
    // The auth middleware has already verified that the user is an admin

    // Parse the request body
    const userData = await req.json();

    // Validate required fields
    if (!userData.email || !userData.display_name) {
      return NextResponse.json(
        { error: "Email and display name are required" },
        { status: 400 },
      );
    }

    // Create the user
    const { data, error } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password || Math.random().toString(36).slice(-8),
      email_confirm: true,
      user_metadata: {
        display_name: userData.display_name,
      },
    });

    if (error) throw error;

    // Create the profile
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .insert({
        id: data.user.id,
        email: userData.email,
        display_name: userData.display_name,
        username: userData.username || userData.email.split("@")[0],
        first_name: userData.first_name || "",
        last_name: userData.last_name || "",
      })
      .select()
      .single();

    if (profileError) throw profileError;

    // Create user roles
    const { data: roles, error: rolesError } = await supabase
      .from("user_roles")
      .insert({
        id: data.user.id,
        admin: userData.is_admin === true,
        parent: userData.is_parent === true,
        teacher: userData.is_teacher === true,
        student: userData.is_student === true,
        performer: userData.is_performer === true,
      })
      .select()
      .single();

    if (rolesError) throw rolesError;

    return NextResponse.json(
      {
        user: data.user,
        profile,
        roles,
      },
      { status: 201 },
    );
  } catch (error: any) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create user" },
      { status: 500 },
    );
  }
}

// Export the handlers with admin authentication
export const { GET, POST } = createApiHandlers({
  GET: withAdminAuth(getUsers),
  POST: withAdminAuth(createUser),
});
