export const dynamic = "force-dynamic";

import { NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * Simple API endpoint to check authentication status
 * This endpoint doesn't use any middleware and directly checks the session
 */
export async function GET() {
  try {
    // Get the cookies
    const cookieStore = cookies();

    // Create a Supabase client
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the session
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error("[AuthCheck] Session error:", error);
      return NextResponse.json(
        {
          authenticated: false,
          error: error.message,
        },
        { status: 500 },
      );
    }

    // Check if we have a session
    const hasSession = !!data.session;
    const userId = data.session?.user?.id;

    console.log(
      `[AuthCheck] User ${userId || "unknown"} is ${hasSession ? "authenticated" : "not authenticated"}`,
    );

    // If authenticated, get user profile
    let profile = null;
    if (hasSession && userId) {
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("*")
        .eq("user_id", userId)
        .single();

      if (profileError) {
        console.error("[AuthCheck] Profile error:", profileError);
      } else {
        profile = profileData;
      }
    }

    return NextResponse.json({
      authenticated: hasSession,
      userId: userId || null,
      profile,
    });
  } catch (error) {
    console.error("[AuthCheck] Unexpected error:", error);
    return NextResponse.json(
      {
        authenticated: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
