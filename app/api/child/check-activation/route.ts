export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: GET /api/child/check-activation
 * Checks if the current user's device is activated
 *
 * Response:
 * {
 *   activated: boolean
 * }
 */
export async function GET(request: NextRequest) {
  const cookieStore = await cookies();
  try {
    // Get the authorization header
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Invalid authorization header" },
        { status: 401 },
      );
    }

    // Extract the token
    const token = authHeader.split(" ")[1];

    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Verify the token
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token);

    if (authError) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Check if there's an activation record for this user
    const { data, error } = await supabase
      .from("device_activations")
      .select("*")
      .eq("child_id", user.id)
      .limit(1);

    if (error) {
      console.error("Error checking device activation:", error);
      return NextResponse.json(
        { error: "Failed to check device activation" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      activated: data && data.length > 0,
    });
  } catch (error) {
    console.error("Error in check-activation API route:", error);
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}
