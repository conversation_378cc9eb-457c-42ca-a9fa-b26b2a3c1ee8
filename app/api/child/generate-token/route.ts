export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { generateActivationToken } from "@/lib/api/accountLinking";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: POST /api/child/generate-token
 * Generates a QR activation token for a child's device
 *
 * Request Body: Empty (uses the authenticated user's ID)
 *
 * Response:
 * {
 *   success: boolean,
 *   token?: string,
 *   expiresAt?: number,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, error: "Invalid authorization header" },
        { status: 401 },
      );
    }

    // Extract the token
    const token = authHeader.split(" ")[1];

    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies });

    // Verify the token
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token);

    if (authError) {
      return NextResponse.json(
        { success: false, error: "Invalid token" },
        { status: 401 },
      );
    }

    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    // Generate token for the current user (child)
    const tokenData = await generateActivationToken(user.id);

    if (!tokenData) {
      return NextResponse.json(
        { success: false, error: "Failed to generate token" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      token: tokenData.token,
      expiresAt: tokenData.expiresAt,
    });
  } catch (error) {
    console.error("Error in generate-token API route:", error);
    return NextResponse.json(
      { success: false, error: "Server error" },
      { status: 500 },
    );
  }
}
