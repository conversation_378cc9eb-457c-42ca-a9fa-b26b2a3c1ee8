export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { mockCurricula } from "@/lib/data/mock-curricula";

interface CurriculumAssignment {
  id?: string;
  child_id: string;
  curriculum_id: string;
  progress?: number;
}

/**
 * API Endpoint: GET /api/children/[username]/curriculum
 * Get curriculum assignments for a child
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  try {
    const cookieStore = await cookies();
    const { username } = await params;
    let childId: string;

    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("profiles")
      .select("id")
      .eq("username", username)
      .eq("profile_type", "child")
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    childId = childData.id;
    // Supabase client already initialized above

    // Verify the user has access to this child
    const { data: child, error: childError } = await supabase
      .from("children")
      .select("user_id")
      .eq("id", childId)
      .single();

    if (childError) {
      console.error("Error fetching child:", childError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    // Get curriculum assignments
    const { data: assignments, error } = await supabase
      .from("child_curriculum_assignments")
      .select("*")
      .eq("child_id", childId);

    if (error) {
      console.error("Error fetching curriculum assignments:", error);
      return NextResponse.json(
        { success: false, error: "Failed to fetch curriculum assignments" },
        { status: 500 },
      );
    }

    // Enrich with curriculum details from mock data
    const enrichedAssignments = assignments.map((assignment) => {
      const curriculum = mockCurricula.find(
        (c) => c.id === assignment.curriculum_id,
      );
      return {
        ...assignment,
        curriculum: curriculum
          ? {
              title: curriculum.title,
              instrument: curriculum.instrument,
              level: curriculum.level,
              publisher: curriculum.publisher,
              description: curriculum.description,
              modules_count: curriculum.modules.length,
              lessons_count: curriculum.modules.reduce(
                (count, module) => count + module.lessons.length,
                0,
              ),
            }
          : null,
      };
    });

    return NextResponse.json({
      success: true,
      data: enrichedAssignments,
    });
  } catch (error: any) {
    console.error("Error in curriculum API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: POST /api/children/[username]/curriculum
 * Assign a curriculum to a child
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  try {
    const cookieStore = await cookies();
    const { username } = await params;
    let childId: string;

    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("profiles")
      .select("id")
      .eq("username", username)
      .eq("profile_type", "child")
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    childId = childData.id;

    // Verify the user has access to this child
    const { data: child, error: childError } = await supabase
      .from("children")
      .select("user_id")
      .eq("id", childId)
      .single();

    if (childError) {
      console.error("Error fetching child:", childError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    // Validate required fields
    const body = await request.json();
    if (!body.curriculum_id) {
      return NextResponse.json(
        { success: false, error: "Curriculum ID is required" },
        { status: 400 },
      );
    }

    // Verify the curriculum exists in our mock data
    const curriculum = mockCurricula.find((c) => c.id === body.curriculum_id);
    if (!curriculum) {
      return NextResponse.json(
        { success: false, error: "Curriculum not found" },
        { status: 404 },
      );
    }

    // Check if the curriculum is already assigned
    const { data: existingAssignment, error: checkError } = await supabase
      .from("child_curriculum_assignments")
      .select("id")
      .eq("child_id", childId)
      .eq("curriculum_id", body.curriculum_id)
      .maybeSingle();

    if (checkError) {
      console.error("Error checking existing assignment:", checkError);
      return NextResponse.json(
        { success: false, error: "Failed to check existing assignments" },
        { status: 500 },
      );
    }

    if (existingAssignment) {
      return NextResponse.json(
        {
          success: false,
          error: "Curriculum is already assigned to this child",
        },
        { status: 400 },
      );
    }

    // Create the assignment
    const newAssignment: CurriculumAssignment = {
      child_id: childId,
      curriculum_id: body.curriculum_id,
      progress: 0,
    };

    const { data, error } = await supabase
      .from("child_curriculum_assignments")
      .insert(newAssignment)
      .select()
      .single();

    if (error) {
      console.error("Error assigning curriculum:", error);
      return NextResponse.json(
        { success: false, error: "Failed to assign curriculum" },
        { status: 500 },
      );
    }

    // Initialize lesson progress for all lessons in the curriculum
    const lessonProgressEntries = curriculum.modules.flatMap((module) =>
      module.lessons.map((lesson) => ({
        child_id: childId,
        curriculum_id: body.curriculum_id,
        module_id: module.id,
        lesson_id: lesson.id,
        completed: false,
        progress: 0,
      })),
    );

    if (lessonProgressEntries.length > 0) {
      const { error: lessonError } = await supabase
        .from("child_lesson_progress")
        .insert(lessonProgressEntries);

      if (lessonError) {
        console.error("Error initializing lesson progress:", lessonError);
        // Continue anyway, as the main assignment was successful
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        ...data,
        curriculum: {
          title: curriculum.title,
          instrument: curriculum.instrument,
          level: curriculum.level,
          publisher: curriculum.publisher,
          description: curriculum.description,
          modules_count: curriculum.modules.length,
          lessons_count: lessonProgressEntries.length,
        },
      },
    });
  } catch (error: any) {
    console.error("Error in curriculum API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}
