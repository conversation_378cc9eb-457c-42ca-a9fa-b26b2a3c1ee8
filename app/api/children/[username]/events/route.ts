export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

interface ChildEvent {
  id?: string;
  child_id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  location?: string;
  type?: "lesson" | "practice" | "performance" | "other";
  notes?: string;
}

/**
 * API Endpoint: GET /api/children/[username]/events
 * Get events for a child
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  try {
    const { username } = await params;
    let childId: string;

    // Fix cookie handling
    const supabase = createRouteHandlerClient({ cookies });

    // Add authentication check
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    const user = session.user;
    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("profiles")
      .select("id, user_id")
      .eq("username", username)
      .eq("profile_type", "child")
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    childId = childData.id;

    // Verify the user has access to this child (parent-child relationship)
    if (childData.user_id !== user.id) {
      // Check if user is a parent of this child
      const { data: parentChild, error: parentError } = await supabase
        .from("children")
        .select("user_id")
        .eq("id", childId)
        .eq("user_id", user.id)
        .single();

      if (parentError || !parentChild) {
        console.error("User does not have access to this child:", parentError);
        return NextResponse.json(
          { success: false, error: "Access denied" },
          { status: 403 },
        );
      }
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get("start_date");
    const endDate = searchParams.get("end_date");

    // Build the query
    let query = supabase
      .from("child_events")
      .select("*")
      .eq("child_id", childId)
      .order("start_time", { ascending: true });

    // Add date filters if provided
    if (startDate) {
      query = query.gte("start_time", startDate);
    }

    if (endDate) {
      query = query.lte("start_time", endDate);
    }

    // Execute the query
    const { data, error } = await query;

    if (error) {
      console.error("Error fetching events:", error);
      return NextResponse.json(
        { success: false, error: "Failed to fetch events" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error: any) {
    console.error("Error in events API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: POST /api/children/[username]/events
 * Create an event for a child
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  try {
    const { username } = await params;
    let childId: string;

    // Fix cookie handling
    const supabase = createRouteHandlerClient({ cookies });

    // Add authentication check
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    const user = session.user;
    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("profiles")
      .select("id, user_id")
      .eq("username", username)
      .eq("profile_type", "child")
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    childId = childData.id;

    // Verify the user has access to this child (parent-child relationship)
    if (childData.user_id !== user.id) {
      // Check if user is a parent of this child
      const { data: parentChild, error: parentError } = await supabase
        .from("children")
        .select("user_id")
        .eq("id", childId)
        .eq("user_id", user.id)
        .single();

      if (parentError || !parentChild) {
        console.error("User does not have access to this child:", parentError);
        return NextResponse.json(
          { success: false, error: "Access denied" },
          { status: 403 },
        );
      }
    }

    const body = await request.json();

    // Validate required fields
    if (!body.title || !body.start_time || !body.end_time) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 },
      );
    }

    // Create the event
    const newEvent: ChildEvent = {
      child_id: childId,
      title: body.title,
      description: body.description || undefined,
      start_time: body.start_time,
      end_time: body.end_time,
      location: body.location || undefined,
      type: body.type || "other",
      notes: body.notes || undefined,
    };

    const { data, error } = await supabase
      .from("child_events")
      .insert(newEvent)
      .select()
      .single();

    if (error) {
      console.error("Error creating event:", error);
      return NextResponse.json(
        { success: false, error: "Failed to create event" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error: any) {
    console.error("Error in events API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: PATCH /api/children/[username]/events/[eventId]
 * Update an event for a child
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ username: string; eventId: string }> },
) {
  try {
    const { username, eventId } = await params;
    let childId: string;

    // Fix cookie handling
    const supabase = createRouteHandlerClient({ cookies });

    // Add authentication check
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    const user = session.user;
    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("profiles")
      .select("id, user_id")
      .eq("username", username)
      .eq("profile_type", "child")
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    childId = childData.id;

    // Verify the user has access to this child (parent-child relationship)
    if (childData.user_id !== user.id) {
      // Check if user is a parent of this child
      const { data: parentChild, error: parentError } = await supabase
        .from("children")
        .select("user_id")
        .eq("id", childId)
        .eq("user_id", user.id)
        .single();

      if (parentError || !parentChild) {
        console.error("User does not have access to this child:", parentError);
        return NextResponse.json(
          { success: false, error: "Access denied" },
          { status: 403 },
        );
      }
    }

    const body = await request.json();

    // Verify the event exists and belongs to the child
    const { data: event, error: eventError } = await supabase
      .from("child_events")
      .select("*")
      .eq("id", eventId)
      .eq("child_id", childId)
      .single();

    if (eventError) {
      console.error("Error fetching event:", eventError);
      return NextResponse.json(
        { success: false, error: "Event not found" },
        { status: 404 },
      );
    }

    // Update the event
    const { data, error } = await supabase
      .from("child_events")
      .update({
        title: body.title || event.title,
        description:
          body.description !== undefined ? body.description : event.description,
        start_time: body.start_time || event.start_time,
        end_time: body.end_time || event.end_time,
        location: body.location !== undefined ? body.location : event.location,
        type: body.type || event.type,
        notes: body.notes !== undefined ? body.notes : event.notes,
        updated_at: new Date().toISOString(),
      })
      .eq("id", eventId)
      .select()
      .single();

    if (error) {
      console.error("Error updating event:", error);
      return NextResponse.json(
        { success: false, error: "Failed to update event" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error: any) {
    console.error("Error in events API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}
