export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import {
  updatePracticeAssignment,
  deletePracticeAssignment,
  markPracticeAssignmentCompleted,
} from "@/lib/queries/practiceAssignmentQueries";

/**
 * API Endpoint: GET /api/children/[username]/practice/assignments/[id]
 * Get a specific practice assignment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string; id: string }> },
  const cookieStore = await cookies();
) {
  try {
    const { username, id: assignmentId } = await params;

    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("children")
      .select("id, user_id")
      .eq("username", username)
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    // Verify the user has access to this child
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    if (childData.user_id !== user.id) {
      return NextResponse.json(
        {
          success: false,
          error: "You don't have permission to access this child's data",
        },
        { status: 403 },
      );
    }

    // Get the assignment
    const { data, error } = await supabase
      .from("practice_assignments")
      .select(
        `
        *,
        song:song_id (
          id,
          title,
          primary_artist_name,
          album_cover_url
        )
      `,
      )
      .eq("id", assignmentId)
      .eq("child_id", childData.id)
      .single();

    if (error) {
      console.error("Error fetching practice assignment:", error);
      return NextResponse.json(
        { success: false, error: "Assignment not found" },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error: any) {
    console.error("Error in practice assignment API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: PUT /api/children/[username]/practice/assignments/[id]
 * Update a practice assignment
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ username: string; id: string }> },
  const cookieStore = await cookies();
) {
  try {
    const { username, id: assignmentId } = await params;

    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("children")
      .select("id, user_id")
      .eq("username", username)
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    // Verify the user has access to this child
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    if (childData.user_id !== user.id) {
      return NextResponse.json(
        {
          success: false,
          error: "You don't have permission to access this child's data",
        },
        { status: 403 },
      );
    }

    // Parse request body
    const body = await request.json();

    // Check if we're just marking as completed
    if (body.action === "complete") {
      const assignment = await markPracticeAssignmentCompleted(
        assignmentId,
        user.id,
        true,
      );

      return NextResponse.json({
        success: true,
        data: assignment,
      });
    }

    if (body.action === "incomplete") {
      const assignment = await markPracticeAssignmentCompleted(
        assignmentId,
        user.id,
        false,
      );

      return NextResponse.json({
        success: true,
        data: assignment,
      });
    }

    // Update the assignment
    const assignment = await updatePracticeAssignment(
      assignmentId,
      body,
      user.id,
    );

    return NextResponse.json({
      success: true,
      data: assignment,
    });
  } catch (error: any) {
    console.error("Error in practice assignment API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: DELETE /api/children/[username]/practice/assignments/[id]
 * Delete a practice assignment
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ username: string; id: string }> },
  const cookieStore = await cookies();
) {
  try {
    const { username, id: assignmentId } = await params;

    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("children")
      .select("id, user_id")
      .eq("username", username)
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    // Verify the user has access to this child
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    if (childData.user_id !== user.id) {
      return NextResponse.json(
        {
          success: false,
          error: "You don't have permission to access this child's data",
        },
        { status: 403 },
      );
    }

    // Delete the assignment
    const success = await deletePracticeAssignment(assignmentId, user.id);

    return NextResponse.json({
      success,
    });
  } catch (error: any) {
    console.error("Error in practice assignment API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}
