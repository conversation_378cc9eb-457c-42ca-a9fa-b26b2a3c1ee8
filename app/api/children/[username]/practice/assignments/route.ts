export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import {
  createPracticeAssignment,
  fetchPracticeAssignments,
  getPracticeAssignmentStats,
} from "@/lib/queries/practiceAssignmentQueries";

/**
 * API Endpoint: GET /api/children/[username]/practice/assignments
 * Get practice assignments for a child
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  try {
    const cookieStore = await cookies();
    const { username } = await params;
    let childId: string;

    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Find the child by username - first try profiles table
    let childData;
    let usernameError;

    // Try to find in profiles table first
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .select("id, user_id, username")
      .eq("username", username)
      .eq("profile_type", "child")
      .eq("is_active", true)
      .single();

    if (profileData) {
      childData = profileData;
    } else {
      // Fallback to children table
      console.log("Profile not found, trying children table...");
      const { data: childrenData, error: childrenError } = await supabase
        .from("children")
        .select("id, user_id")
        .eq("username", username)
        .eq("is_active", true)
        .single();

      if (childrenError) {
        console.error("Error finding child by username:", childrenError);
        return NextResponse.json(
          { success: false, error: "Child not found" },
          { status: 404 },
        );
      }

      childData = childrenData;
    }

    childId = childData.id;

    // Verify the user has access to this child
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    if (childData.user_id !== user.id) {
      return NextResponse.json(
        {
          success: false,
          error: "You don't have permission to access this child's data",
        },
        { status: 403 },
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const completed = searchParams.get("completed");
    const limit = searchParams.get("limit");
    const offset = searchParams.get("offset");
    const startDate = searchParams.get("start_date");
    const endDate = searchParams.get("end_date");
    const assignedByType = searchParams.get("assigned_by_type");
    const stats = searchParams.get("stats");

    // If stats is requested, return statistics
    if (stats === "true") {
      const statsData = await getPracticeAssignmentStats(childId);
      return NextResponse.json({
        success: true,
        data: statsData,
      });
    }

    // Fetch assignments
    const assignments = await fetchPracticeAssignments(childId, {
      completed: completed ? completed === "true" : undefined,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
      startDate: startDate || undefined,
      endDate: endDate || undefined,
      assignedByType: (assignedByType as any) || undefined,
    });

    return NextResponse.json({
      success: true,
      data: assignments,
    });
  } catch (error: any) {
    console.error("Error in practice assignments API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: POST /api/children/[username]/practice/assignments
 * Create a new practice assignment for a child
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  try {
    const cookieStore = await cookies();
    const { username } = await params;
    let childId: string;

    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Find the child by username - first try profiles table
    let childData;
    let usernameError;

    // Try to find in profiles table first
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .select("id, user_id, username")
      .eq("username", username)
      .eq("profile_type", "child")
      .eq("is_active", true)
      .single();

    if (profileData) {
      childData = profileData;
    } else {
      // Fallback to children table
      console.log("Profile not found, trying children table...");
      const { data: childrenData, error: childrenError } = await supabase
        .from("children")
        .select("id, user_id")
        .eq("username", username)
        .eq("is_active", true)
        .single();

      if (childrenError) {
        console.error("Error finding child by username:", childrenError);
        return NextResponse.json(
          { success: false, error: "Child not found" },
          { status: 404 },
        );
      }

      childData = childrenData;
    }

    childId = childData.id;

    // Verify the user has access to this child
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    if (childData.user_id !== user.id) {
      return NextResponse.json(
        {
          success: false,
          error: "You don't have permission to access this child's data",
        },
        { status: 403 },
      );
    }

    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      console.error("Error parsing request body:", error);
      return NextResponse.json(
        { success: false, error: "Invalid JSON in request body" },
        { status: 400 },
      );
    }

    try {
      // Validate required fields
      if (!body.title) {
        return NextResponse.json(
          { success: false, error: "Title is required" },
          { status: 400 },
        );
      }

      if (!body.type) {
        return NextResponse.json(
          { success: false, error: "Assignment type is required" },
          { status: 400 },
        );
      }

      // Create the assignment
      const assignment = await createPracticeAssignment(
        {
          ...body,
          child_id: childId,
        },
        user.id,
      );

      return NextResponse.json({
        success: true,
        data: assignment,
      });
    } catch (createError: any) {
      console.error("Error creating practice assignment:", createError);
      return NextResponse.json(
        {
          success: false,
          error: createError.message || "Failed to create practice assignment",
          details: createError.details || createError.toString(),
        },
        { status: 500 },
      );
    }
  } catch (error: any) {
    console.error("Error in practice assignments API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}
