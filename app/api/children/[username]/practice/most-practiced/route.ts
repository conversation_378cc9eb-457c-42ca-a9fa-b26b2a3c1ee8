export const dynamic = "force-dynamic";

import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { fetchProfileByUsername } from "@/lib/queries/profileBasedQueries";
import { format, subDays } from "date-fns";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
  const cookieStore = await cookies();
) {
  try {
    const { username } = await params;
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the child profile
    const profile = await fetchProfileByUsername(username);

    if (!profile) {
      return NextResponse.json(
        { error: "Child profile not found" },
        { status: 404 },
      );
    }

    // Check if the current user has permission to access this child's data
    const isOwner = profile.user_id === session.user.id;
    const isParent = profile.parent_id === session.user.id;

    if (!isOwner && !isParent) {
      return NextResponse.json(
        {
          error:
            "You don't have permission to access this child's practice data",
        },
        { status: 403 },
      );
    }

    // Get the start of the current month
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const startOfMonthStr = format(startOfMonth, "yyyy-MM-dd");

    // Query practice sessions for this child in the current month
    const { data: sessions, error } = await supabase
      .from("practice_sessions")
      .select("*")
      .eq("child_id", profile.id)
      .gte("practiced_at", startOfMonthStr)
      .order("practiced_at", { ascending: false });

    if (error) {
      console.error("Error fetching practice sessions:", error);
      return NextResponse.json(
        { error: "Failed to fetch practice data" },
        { status: 500 },
      );
    }

    // If no sessions, return empty data
    if (!sessions || sessions.length === 0) {
      return NextResponse.json({
        data: null,
      });
    }

    // Analyze practice types
    const practiceTypes: Record<string, number> = {};
    const instruments: Record<string, number> = {};

    // Count occurrences of each practice type and instrument
    sessions.forEach((session) => {
      // Handle practice types
      if (session.practice_type && Array.isArray(session.practice_type)) {
        session.practice_type.forEach((type: string) => {
          if (type) {
            practiceTypes[type] = (practiceTypes[type] || 0) + 1;
          }
        });
      }

      // Handle instruments
      if (session.metadata?.instrument) {
        const instrument = session.metadata.instrument;
        instruments[instrument] = (instruments[instrument] || 0) + 1;
      }
    });

    // Find most common practice type
    let mostPracticedType = null;
    let maxTypeCount = 0;

    Object.entries(practiceTypes).forEach(([type, count]) => {
      if (count > maxTypeCount) {
        mostPracticedType = type;
        maxTypeCount = count;
      }
    });

    // Find most common instrument
    let mostPracticedInstrument = null;
    let maxInstrumentCount = 0;

    Object.entries(instruments).forEach(([instrument, count]) => {
      if (count > maxInstrumentCount) {
        mostPracticedInstrument = instrument;
        maxInstrumentCount = count;
      }
    });

    // Calculate total minutes for the month
    const totalMinutes = sessions.reduce(
      (sum, session) => sum + (session.duration_minutes || 0),
      0,
    );

    // Determine what to return based on what data we have
    let result;

    if (mostPracticedInstrument && maxInstrumentCount > 0) {
      result = {
        name: mostPracticedInstrument,
        time: `${totalMinutes} minutes this month`,
      };
    } else if (mostPracticedType && maxTypeCount > 0) {
      result = {
        name: mostPracticedType,
        time: `${maxTypeCount} sessions this month`,
      };
    } else {
      // Fallback if no specific data
      result = {
        name: "Practice",
        time: `${totalMinutes} minutes this month`,
      };
    }

    return NextResponse.json({ data: result });
  } catch (error) {
    console.error("Error in most-practiced API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
