export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

interface PracticeSession {
  id?: string;
  child_id: string;
  start_time: string;
  end_time?: string;
  duration_minutes?: number;
  instrument?: string;
  notes?: string;
}

/**
 * API Endpoint: GET /api/children/[username]/practice
 * Get practice sessions for a child
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  try {
    const { username } = await params;
    let childId: string;

    // Fix cookie handling
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Add authentication check
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    const user = session.user;
    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("profiles")
      .select("id, user_id")
      .eq("username", username)
      .eq("profile_type", "child")
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    childId = childData.id;

    // Verify the user has access to this child (parent-child relationship)
    if (childData.user_id !== user.id) {
      // Check if user is a parent of this child
      const { data: parentChild, error: parentError } = await supabase
        .from("children")
        .select("user_id")
        .eq("id", childId)
        .eq("user_id", user.id)
        .single();

      if (parentError || !parentChild) {
        console.error("User does not have access to this child:", parentError);
        return NextResponse.json(
          { success: false, error: "Access denied" },
          { status: 403 },
        );
      }
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get("start_date");
    const endDate = searchParams.get("end_date");

    // Build the query
    let query = supabase
      .from("practice_sessions")
      .select("*")
      .eq("child_id", childId)
      .order("start_time", { ascending: false });

    // Add date filters if provided
    if (startDate) {
      query = query.gte("start_time", startDate);
    }

    if (endDate) {
      query = query.lte("start_time", endDate);
    }

    // Execute the query
    const { data, error } = await query;

    if (error) {
      console.error("Error fetching practice sessions:", error);
      return NextResponse.json(
        { success: false, error: "Failed to fetch practice sessions" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error: any) {
    console.error("Error in practice API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: POST /api/children/[username]/practice
 * Start or end a practice session for a child
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  const cookieStore = await cookies();
  try {
    const { username } = await params;
    const body = await request.json();
    const { action, session_id, instrument, notes } = body;

    // Fix cookie handling
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Add authentication check
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    const user = session.user;
    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("profiles")
      .select("id, user_id")
      .eq("username", username)
      .eq("profile_type", "child")
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    const childId = childData.id;

    // Verify the user has access to this child
    const { data: child, error: childError } = await supabase
      .from("children")
      .select("user_id")
      .eq("id", childId)
      .eq("user_id", user.id)
      .single();

    if (childError || !child) {
      console.error("Error fetching child or access denied:", childError);
      return NextResponse.json(
        { success: false, error: "Child not found or access denied" },
        { status: 403 },
      );
    }

    if (action === "start") {
      // Start a new practice session
      const newSession: PracticeSession = {
        child_id: childId,
        start_time: new Date().toISOString(),
        instrument: instrument || undefined,
        notes: notes || undefined,
      };

      const { data, error } = await supabase
        .from("practice_sessions")
        .insert(newSession)
        .select()
        .single();

      if (error) {
        console.error("Error starting practice session:", error);
        return NextResponse.json(
          { success: false, error: "Failed to start practice session" },
          { status: 500 },
        );
      }

      return NextResponse.json({
        success: true,
        data,
      });
    } else if (action === "end") {
      // End an existing practice session
      if (!session_id) {
        return NextResponse.json(
          { success: false, error: "Session ID is required" },
          { status: 400 },
        );
      }

      // Get the current session
      const { data: currentSession, error: sessionError } = await supabase
        .from("practice_sessions")
        .select("*")
        .eq("id", session_id)
        .eq("child_id", childId)
        .single();

      if (sessionError) {
        console.error("Error fetching practice session:", sessionError);
        return NextResponse.json(
          { success: false, error: "Practice session not found" },
          { status: 404 },
        );
      }

      // Calculate duration in minutes
      const startTime = new Date(currentSession.start_time);
      const endTime = new Date();
      const durationMinutes = Math.round(
        (endTime.getTime() - startTime.getTime()) / 60000,
      );

      // Update the session
      const { data, error } = await supabase
        .from("practice_sessions")
        .update({
          end_time: endTime.toISOString(),
          duration_minutes: durationMinutes,
          notes: notes || currentSession.notes,
        })
        .eq("id", session_id)
        .select()
        .single();

      if (error) {
        console.error("Error ending practice session:", error);
        return NextResponse.json(
          { success: false, error: "Failed to end practice session" },
          { status: 500 },
        );
      }

      return NextResponse.json({
        success: true,
        data,
      });
    } else {
      return NextResponse.json(
        { success: false, error: "Invalid action" },
        { status: 400 },
      );
    }
  } catch (error: any) {
    console.error("Error in practice API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}


