export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: GET /api/children/[username]/practice/stats
 * Get practice statistics for a child
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  try {
    const { username } = await params;
    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "week"; // day, week, month, year

    // Fix cookie handling
    const supabase = createRouteHandlerClient({ cookies });

    // Add authentication check
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    const user = session.user;
    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("profiles")
      .select("id, user_id")
      .eq("username", username)
      .eq("profile_type", "child")
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    const childId = childData.id;

    // Verify the user has access to this child (parent-child relationship)
    if (childData.user_id !== user.id) {
      // Check if user is a parent of this child
      const { data: parentChild, error: parentError } = await supabase
        .from("children")
        .select("user_id")
        .eq("id", childId)
        .eq("user_id", user.id)
        .single();

      if (parentError || !parentChild) {
        console.error("User does not have access to this child:", parentError);
        return NextResponse.json(
          { success: false, error: "Access denied" },
          { status: 403 },
        );
      }
    }

    if (period === "day") {
      // Get today's practice minutes
      const { data, error } = await supabase.rpc("get_daily_practice_minutes", {
        p_child_id: childId,
      });

      if (error) {
        console.error("Error fetching daily practice minutes:", error);
        return NextResponse.json(
          { success: false, error: "Failed to fetch practice statistics" },
          { status: 500 },
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          minutes: data || 0,
        },
      });
    } else if (period === "week") {
      // Get weekly practice minutes
      const { data, error } = await supabase.rpc(
        "get_weekly_practice_minutes",
        { p_child_id: childId },
      );

      if (error) {
        console.error("Error fetching weekly practice minutes:", error);
        return NextResponse.json(
          { success: false, error: "Failed to fetch practice statistics" },
          { status: 500 },
        );
      }

      return NextResponse.json({
        success: true,
        data,
      });
    } else {
      // For other periods, calculate manually
      const now = new Date();
      let startDate = new Date();

      if (period === "month") {
        startDate.setDate(1); // First day of current month
      } else if (period === "year") {
        startDate = new Date(now.getFullYear(), 0, 1); // January 1st of current year
      }

      // Get practice sessions in the period
      const { data, error } = await supabase
        .from("practice_sessions")
        .select("duration_minutes, start_time")
        .eq("child_id", childId)
        .gte("start_time", startDate.toISOString())
        .lte("start_time", now.toISOString());

      if (error) {
        console.error("Error fetching practice sessions:", error);
        return NextResponse.json(
          { success: false, error: "Failed to fetch practice statistics" },
          { status: 500 },
        );
      }

      // Calculate total minutes
      const totalMinutes = data.reduce(
        (sum, session) => sum + (session.duration_minutes || 0),
        0,
      );

      return NextResponse.json({
        success: true,
        data: {
          period,
          total_minutes: totalMinutes,
          sessions: data.length,
        },
      });
    }
  } catch (error: any) {
    console.error("Error in practice stats API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
} 