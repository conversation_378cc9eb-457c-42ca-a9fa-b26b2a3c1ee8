export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

interface ChildSong {
  id?: string;
  child_id: string;
  title: string;
  artist?: string;
  instrument?: string;
  difficulty?: string;
  progress?: number;
  notes?: string;
  sheet_music_url?: string;
  audio_url?: string;
}

/**
 * API Endpoint: GET /api/children/[username]/songs
 * Get songs for a child
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  try {
    const { username } = await params;
    let childId: string;

    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("profiles")
      .select("id")
      .eq("username", username)
      .eq("profile_type", "child")
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    childId = childData.id;
    const { searchParams } = new URL(request.url);
    const instrument = searchParams.get("instrument");

    // Supabase client already initialized above

    // Verification already done when fetching by username

    // Build the query
    let query = supabase
      .from("child_songs")
      .select("*")
      .eq("child_id", childId)
      .order("created_at", { ascending: false });

    // Add instrument filter if provided
    if (instrument) {
      query = query.eq("instrument", instrument);
    }

    // Execute the query
    const { data, error } = await query;

    if (error) {
      console.error("Error fetching songs:", error);
      return NextResponse.json(
        { success: false, error: "Failed to fetch songs" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error: any) {
    console.error("Error in songs API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: POST /api/children/[username]/songs
 * Add a song for a child
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  try {
    const { username } = await params;
    let childId: string;

    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("profiles")
      .select("id")
      .eq("username", username)
      .eq("profile_type", "child")
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    childId = childData.id;
    const body = await request.json();

    // Supabase client already initialized above

    // Verification already done when fetching by username

    // Validate required fields
    if (!body.title) {
      return NextResponse.json(
        { success: false, error: "Title is required" },
        { status: 400 },
      );
    }

    // Create the song
    const newSong: ChildSong = {
      child_id: childId,
      title: body.title,
      artist: body.artist || undefined,
      instrument: body.instrument || undefined,
      difficulty: body.difficulty || undefined,
      progress: body.progress || 0,
      notes: body.notes || undefined,
      sheet_music_url: body.sheet_music_url || undefined,
      audio_url: body.audio_url || undefined,
    };

    const { data, error } = await supabase
      .from("child_songs")
      .insert(newSong)
      .select()
      .single();

    if (error) {
      console.error("Error adding song:", error);
      return NextResponse.json(
        { success: false, error: "Failed to add song" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error: any) {
    console.error("Error in songs API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: PATCH /api/children/[username]/songs/[songId]
 * Update a song for a child
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ username: string; songId: string }> },
) {
  try {
    const { username, songId } = await params;
    let childId: string;

    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Find the child by username
    const { data: childData, error: usernameError } = await supabase
      .from("profiles")
      .select("id")
      .eq("username", username)
      .eq("profile_type", "child")
      .single();

    if (usernameError) {
      console.error("Error finding child by username:", usernameError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    childId = childData.id;
    const body = await request.json();

    // Supabase client already initialized above

    // Verification already done when fetching by username

    // Verify the song exists and belongs to the child
    const { data: song, error: songError } = await supabase
      .from("child_songs")
      .select("*")
      .eq("id", songId)
      .eq("child_id", childId)
      .single();

    if (songError) {
      console.error("Error fetching song:", songError);
      return NextResponse.json(
        { success: false, error: "Song not found" },
        { status: 404 },
      );
    }

    // Update the song
    const { data, error } = await supabase
      .from("child_songs")
      .update({
        title: body.title || song.title,
        artist: body.artist !== undefined ? body.artist : song.artist,
        instrument:
          body.instrument !== undefined ? body.instrument : song.instrument,
        difficulty:
          body.difficulty !== undefined ? body.difficulty : song.difficulty,
        progress: body.progress !== undefined ? body.progress : song.progress,
        notes: body.notes !== undefined ? body.notes : song.notes,
        sheet_music_url:
          body.sheet_music_url !== undefined
            ? body.sheet_music_url
            : song.sheet_music_url,
        audio_url:
          body.audio_url !== undefined ? body.audio_url : song.audio_url,
        updated_at: new Date().toISOString(),
      })
      .eq("id", songId)
      .select()
      .single();

    if (error) {
      console.error("Error updating song:", error);
      return NextResponse.json(
        { success: false, error: "Failed to update song" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error: any) {
    console.error("Error in songs API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}
