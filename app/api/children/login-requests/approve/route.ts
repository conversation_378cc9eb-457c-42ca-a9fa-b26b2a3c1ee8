import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: POST /api/children/login-requests/approve
 * Approve a login request for a child
 * Requires authentication
 *
 * Request Body:
 * {
 *   requestId: string,
 *   code: string
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // For development, we'll skip the authentication check
    // In production, you would want to uncomment this code
    /*
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }
    const userId = session.user.id;
    */

    // For development, we'll use a dummy user ID
    const userId = "40fc6ab1-4bf3-4813-b14a-017007c07c07"; // Replace with an actual parent ID from your database

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.requestId || !body.code) {
      return NextResponse.json(
        { success: false, error: "Request ID and code are required" },
        { status: 400 },
      );
    }

    // Format the code (remove dashes and spaces)
    const formattedCode = body.code.replace(/[\\s-]/g, "").toUpperCase();

    // Get the login request
    const { data: loginRequest, error: requestError } = await supabase
      .from("child_login_requests")
      .select("*, children(id, username, name)")
      .eq("id", body.requestId)
      .eq("status", "pending")
      .gt("expires_at", new Date().toISOString())
      .single();

    if (requestError) {
      console.error("Error fetching login request:", requestError);
      return NextResponse.json(
        { success: false, error: "Login request not found or expired" },
        { status: 404 },
      );
    }

    // Verify the parent has permission to approve this request
    const { data: parentChild, error: parentChildError } = await supabase
      .from("parent_child_links")
      .select("*")
      .eq("parent_id", userId)
      .eq("child_id", loginRequest.child_id)
      .single();

    if (parentChildError) {
      console.error(
        "Error verifying parent-child relationship:",
        parentChildError,
      );
      return NextResponse.json(
        {
          success: false,
          error: "You don't have permission to approve this request",
        },
        { status: 403 },
      );
    }

    // Verify the code matches
    if (loginRequest.login_code !== formattedCode) {
      return NextResponse.json(
        { success: false, error: "Invalid code" },
        { status: 400 },
      );
    }

    // Update the login request status
    const { error: updateError } = await supabase
      .from("child_login_requests")
      .update({
        status: "approved",
        approved_at: new Date().toISOString(),
      })
      .eq("id", body.requestId);

    if (updateError) {
      console.error("Error updating login request:", updateError);
      return NextResponse.json(
        { success: false, error: "Failed to approve login request" },
        { status: 500 },
      );
    }

    // Update the child's login code in the children table
    const { error: childUpdateError } = await supabase
      .from("children")
      .update({
        login_code: formattedCode,
        login_code_expires: new Date(Date.now() + 15 * 60 * 1000).toISOString(), // 15 minutes from now
      })
      .eq("id", loginRequest.child_id);

    if (childUpdateError) {
      console.error("Error updating child login code:", childUpdateError);
      // Continue anyway, this is not critical
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Error in approve login request API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Failed to approve login request",
      },
      { status: 500 },
    );
  }
}
