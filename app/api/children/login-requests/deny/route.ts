export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: POST /api/children/login-requests/deny
 * Deny a login request for a child
 * Requires authentication
 *
 * Request Body:
 * {
 *   requestId: string
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // For development, we'll skip the authentication check
    // In production, you would want to uncomment this code
    /*
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }
    const userId = session.user.id;
    */

    // For development, we'll use a dummy user ID
    const userId = "40fc6ab1-4bf3-4813-b14a-017007c07c07"; // Replace with an actual parent ID from your database

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.requestId) {
      return NextResponse.json(
        { success: false, error: "Request ID is required" },
        { status: 400 },
      );
    }

    // Get the login request
    const { data: loginRequest, error: requestError } = await supabase
      .from("child_login_requests")
      .select("*, children(id, username, name)")
      .eq("id", body.requestId)
      .eq("status", "pending")
      .single();

    if (requestError) {
      console.error("Error fetching login request:", requestError);
      return NextResponse.json(
        { success: false, error: "Login request not found" },
        { status: 404 },
      );
    }

    // Verify the parent has permission to deny this request
    const { data: parentChild, error: parentChildError } = await supabase
      .from("parent_child_links")
      .select("*")
      .eq("parent_id", userId)
      .eq("child_id", loginRequest.child_id)
      .single();

    if (parentChildError) {
      console.error(
        "Error verifying parent-child relationship:",
        parentChildError,
      );
      return NextResponse.json(
        {
          success: false,
          error: "You don't have permission to deny this request",
        },
        { status: 403 },
      );
    }

    // Update the login request status
    const { error: updateError } = await supabase
      .from("child_login_requests")
      .update({
        status: "denied",
        denied_at: new Date().toISOString(),
      })
      .eq("id", body.requestId);

    if (updateError) {
      console.error("Error updating login request:", updateError);
      return NextResponse.json(
        { success: false, error: "Failed to deny login request" },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Error in deny login request API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Failed to deny login request",
      },
      { status: 500 },
    );
  }
}
