export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: GET /api/children/login-requests
 * Get pending login requests for a parent's children
 * Requires authentication
 *
 * Response:
 * {
 *   success: boolean,
 *   requests?: Array<LoginRequest>,
 *   error?: string
 * }
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // For development, we'll skip the authentication check
    // In production, you would want to uncomment this code
    /*
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }
    const userId = session.user.id;
    */

    // For development, we'll use a dummy user ID
    const userId = "40fc6ab1-4bf3-4813-b14a-017007c07c07"; // Replace with an actual parent ID from your database

    // Get all children for the authenticated parent
    const { data: children, error: childrenError } = await supabase
      .from("parent_child_links")
      .select("child_id")
      .eq("parent_id", userId);

    if (childrenError) {
      console.error("Error fetching children:", childrenError);
      return NextResponse.json(
        { success: false, error: "Failed to fetch children" },
        { status: 500 },
      );
    }

    if (!children || children.length === 0) {
      return NextResponse.json({ success: true, requests: [] });
    }

    const childIds = children.map((c) => c.child_id);

    // Get pending login requests for these children
    const { data: requests, error: requestsError } = await supabase
      .from("child_login_requests")
      .select(
        `
        id,
        child_id,
        device_type,
        device_name,
        device_id,
        location,
        login_code,
        status,
        created_at,
        expires_at,
        children(id, username, name, avatar_url)
      `,
      )
      .in("child_id", childIds)
      .eq("status", "pending")
      .gt("expires_at", new Date().toISOString())
      .order("created_at", { ascending: false });

    if (requestsError) {
      console.error("Error fetching login requests:", requestsError);
      return NextResponse.json(
        { success: false, error: "Failed to fetch login requests" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      requests: requests || [],
    });
  } catch (error: any) {
    console.error("Error in login requests API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Failed to fetch login requests",
      },
      { status: 500 },
    );
  }
}
