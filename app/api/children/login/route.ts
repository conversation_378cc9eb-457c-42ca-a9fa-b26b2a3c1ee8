import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import {
  findChildByUsername,
  generateLoginCode,
} from "@/lib/queries/childQueries";
import { ChildLoginRequest, ChildLoginResponse } from "@/lib/types/child";

/**
 * API Endpoint: POST /api/children/login
 * Initiates a login request for a child
 *
 * Request Body:
 * {
 *   username: string
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   child_id?: string,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the session first to ensure we have a valid session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        {
          success: false,
          error: "Authentication failed - please log in again",
        },
        { status: 401 },
      );
    }

    // Get the user from the session
    const user = session.user;

    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        {
          success: false,
          error: "Authentication required - please log in again",
        },
        { status: 401 },
      );
    }

    // Parse the request body
    const body: ChildLoginRequest = await request.json();

    // Validate required fields
    if (!body.username) {
      return NextResponse.json(
        { success: false, error: "Username is required" },
        { status: 400 },
      );
    }

    // Find the child by username
    const child = await findChildByUsername(body.username);

    if (!child) {
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    // Check if the user is the parent of the child
    if (child.user_id !== user.id) {
      return NextResponse.json(
        {
          success: false,
          error:
            "You are not authorized to generate a login code for this child",
        },
        { status: 403 },
      );
    }

    // Generate a login code
    const loginCode = await generateLoginCode(child.id);

    // Get the expiration time
    const { data: updatedChild } = await supabase
      .from("children")
      .select("login_code_expires")
      .eq("id", child.id)
      .single();

    const response: ChildLoginResponse = {
      success: true,
      child_id: child.id,
      login_code: loginCode,
      expires_at: updatedChild?.login_code_expires,
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error("Error in child login API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Failed to process login request",
      },
      { status: 500 },
    );
  }
}
