import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import {
  fetchChildren,
  createChild,
  checkChildLimits,
} from "@/lib/queries/childQueries";

/**
 * API Endpoint: GET /api/children
 * Fetches all children for the authenticated user
 *
 * Response:
 * Array of children or error
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the session first to ensure we have a valid session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        { error: "Authentication failed - please log in again" },
        { status: 401 },
      );
    }

    // Get the user from the session
    const user = session.user;

    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        { error: "Authentication required - please log in again" },
        { status: 401 },
      );
    }

    const children = await fetchChildren(user.id);

    return NextResponse.json(children);
  } catch (error: any) {
    console.error("Error fetching children:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch children" },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: POST /api/children
 * Creates a new child for the authenticated user
 *
 * Request Body:
 * Child data
 *
 * Response:
 * Created child or error
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the session first to ensure we have a valid session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        { error: "Authentication failed - please log in again" },
        { status: 401 },
      );
    }

    // Get the user from the session
    const user = session.user;

    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        { error: "Authentication required - please log in again" },
        { status: 401 },
      );
    }

    // Check if the user has reached their child limit
    const { canAddMoreChildren, childCount, maxChildren } =
      await checkChildLimits(user.id);

    if (!canAddMoreChildren) {
      return NextResponse.json(
        {
          error: "Child limit reached",
          message: `You have reached your limit of ${maxChildren} children. Upgrade to add more.`,
          currentCount: childCount,
          maxItems: maxChildren,
        },
        { status: 403 },
      );
    }

    // Parse the request body
    const childData = await request.json();
    console.log("Received child data:", childData);

    // Validate required fields
    if (!childData.name || !childData.dob) {
      return NextResponse.json(
        { error: "Missing required fields: name and dob are required" },
        { status: 400 },
      );
    }

    if (!childData.username) {
      return NextResponse.json(
        { error: "Missing required field: username is required" },
        { status: 400 },
      );
    }

    // Check if username is unique using the authenticated client
    const { count: existingCount, error: uniqueError } = await supabase
      .from("children")
      .select("id", { count: "exact", head: true })
      .eq("username", childData.username.toLowerCase());

    if (uniqueError) {
      console.error("Error checking username uniqueness:", uniqueError);
      return NextResponse.json(
        { error: "Failed to validate username" },
        { status: 500 },
      );
    }

    if (existingCount && existingCount > 0) {
      return NextResponse.json(
        {
          error:
            "Username is already taken. Please choose a different username.",
        },
        { status: 400 },
      );
    }

    // Prepare the insert data
    const insertData = {
      user_id: user.id,
      name: childData.name.trim(),
      username: childData.username.trim().toLowerCase(),
      dob: childData.dob,
      bio: childData.bio || "",
      avatar_url: childData.avatar_url || "",
      is_active: true,
      is_student: childData.is_student ?? true,
      is_performer: childData.is_performer ?? false,
      is_teacher: childData.is_teacher ?? false,
      color_scheme: childData.color_scheme || "purple",
      login_permission: childData.login_permission || "none",
      instruments: childData.instruments || [],
    };

    console.log("Inserting child data:", insertData);

    // Create the child using the authenticated Supabase client
    const { data: newChild, error: insertError } = await supabase
      .from("children")
      .insert([insertData])
      .select()
      .single();

    if (insertError) {
      console.error("Supabase error creating child:", insertError);
      console.error("Error details:", {
        message: insertError.message,
        details: insertError.details,
        hint: insertError.hint,
        code: insertError.code,
      });
      return NextResponse.json(
        { error: insertError.message || "Failed to create child" },
        { status: 500 },
      );
    }

    console.log("Child created successfully:", newChild);
    return NextResponse.json(newChild, { status: 201 });
  } catch (error: any) {
    console.error("Error creating child:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create child" },
      { status: 500 },
    );
  }
}
