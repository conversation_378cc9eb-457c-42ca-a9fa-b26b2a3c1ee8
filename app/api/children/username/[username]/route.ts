export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { findChildByUsername } from "@/lib/queries/childQueries";

/**
 * API Endpoint: GET /api/children/username/:username
 * Fetches a specific child by username
 *
 * Response:
 * Child or error
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  try {
    // Get the current user from Supabase auth
    const supabase = createRouteHandlerClient({ cookies });

    // Get the session first to ensure we have a valid session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        { error: "Authentication failed - please log in again" },
        { status: 401 },
      );
    }

    // Get the user from the session
    const user = session.user;

    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        { error: "Authentication required - please log in again" },
        { status: 401 },
      );
    }

    // Normalize the username to lowercase
    const { username } = await params;
    const normalizedUsername = username.toLowerCase();

    // Find the child by username
    const child = await findChildByUsername(normalizedUsername);

    if (!child) {
      return NextResponse.json({ error: "Child not found" }, { status: 404 });
    }

    // Check if the child belongs to the user
    if (child.user_id !== user.id) {
      return NextResponse.json(
        { error: "You do not have permission to access this child" },
        { status: 403 },
      );
    }

    return NextResponse.json(child);
  } catch (error: any) {
    console.error("Error fetching child by username:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch child" },
      { status: 500 },
    );
  }
}
