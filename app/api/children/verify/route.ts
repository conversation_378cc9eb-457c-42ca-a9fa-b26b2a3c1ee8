export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import {
  verifyLoginCode,
  setLoginPermission,
} from "@/lib/queries/childQueries";
import { ChildLoginVerification } from "@/lib/types/child";

/**
 * API Endpoint: POST /api/children/verify
 * Verifies a login code for a child and sets permission
 *
 * Request Body:
 * {
 *   child_id: string,
 *   code: string,
 *   permission: 'none' | 'once' | 'week' | 'permanent'
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies });

    // Get the session first to ensure we have a valid session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        {
          success: false,
          error: "Authentication failed - please log in again",
        },
        { status: 401 },
      );
    }

    // Get the user from the session
    const user = session.user;

    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        {
          success: false,
          error: "Authentication required - please log in again",
        },
        { status: 401 },
      );
    }

    // Parse the request body
    const body: ChildLoginVerification = await request.json();

    // Validate required fields
    if (!body.child_id || !body.code || !body.permission) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 },
      );
    }

    // Check if the child belongs to the user
    const { data: child, error: childError } = await supabase
      .from("children")
      .select("user_id")
      .eq("id", body.child_id)
      .single();

    if (childError) {
      console.error("Error fetching child:", childError);
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    if (child.user_id !== user.id) {
      return NextResponse.json(
        {
          success: false,
          error: "You are not authorized to verify this child's login",
        },
        { status: 403 },
      );
    }

    // Verify the login code
    const isValid = await verifyLoginCode(body.child_id, body.code);

    if (!isValid) {
      return NextResponse.json(
        { success: false, error: "Invalid or expired login code" },
        { status: 400 },
      );
    }

    // Set the login permission
    await setLoginPermission(body.child_id, body.permission);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Error in verify login API:", error);
    return NextResponse.json(
      { success: false, error: error.message || "Failed to verify login code" },
      { status: 500 },
    );
  }
}
