import { NextRequest, NextResponse } from "next/server";
import { checkPracticeStreaks } from "@/lib/notifications/triggers/practice-streak";
import { checkUpcomingLessons } from "@/lib/notifications/triggers/upcoming-lessons";
import { checkBillingReminders } from "@/lib/notifications/triggers/billing-reminders";

/**
 * API Endpoint: GET /api/cron/notifications
 * Runs scheduled notification jobs
 *
 * This endpoint is meant to be called by a cron job service like Vercel Cron
 * It should be scheduled to run at appropriate intervals (e.g., hourly)
 *
 * Query Parameters:
 * - key: string - A secret key to prevent unauthorized access
 * - job: string - (Optional) Specific job to run ('practice', 'lessons', 'billing', 'all')
 *
 * Response:
 * {
 *   success: boolean,
 *   results?: object,
 *   error?: string
 * }
 */
export async function GET(request: NextRequest) {
  try {
    // Verify the secret key
    const url = new URL(request.url);
    const key = url.searchParams.get("key");

    if (key !== process.env.CRON_SECRET_KEY) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    // Get the job parameter
    const job = url.searchParams.get("job") || "all";

    // Results object
    const results: Record<string, any> = {};

    // Run the specified job(s)
    if (job === "all" || job === "practice") {
      console.log("Running practice streak notifications job");
      try {
        await checkPracticeStreaks();
        results.practice = { success: true };
      } catch (error: any) {
        console.error(
          "Error running practice streak notifications job:",
          error,
        );
        results.practice = { success: false, error: error.message };
      }
    }

    if (job === "all" || job === "lessons") {
      console.log("Running upcoming lessons notifications job");
      try {
        await checkUpcomingLessons();
        results.lessons = { success: true };
      } catch (error: any) {
        console.error(
          "Error running upcoming lessons notifications job:",
          error,
        );
        results.lessons = { success: false, error: error.message };
      }
    }

    if (job === "all" || job === "billing") {
      console.log("Running billing reminders notifications job");
      try {
        await checkBillingReminders();
        results.billing = { success: true };
      } catch (error: any) {
        console.error(
          "Error running billing reminders notifications job:",
          error,
        );
        results.billing = { success: false, error: error.message };
      }
    }

    return NextResponse.json({
      success: true,
      results,
    });
  } catch (error) {
    console.error("Unexpected error in notifications cron job:", error);
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 },
    );
  }
}
