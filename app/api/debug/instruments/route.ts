import { NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * Debug endpoint to check user instruments directly from database
 */
export async function GET() {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the current user
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    const userId = session.user.id;
    console.log(`[DEBUG] Checking instruments for user: ${userId}`);

    // Query user_instruments table directly with different approaches

    // 1. Query by user_id
    const { data: byUserId, error: userIdError } = await supabase
      .from("user_instruments")
      .select("*")
      .eq("user_id", userId);

    console.log(`[DEBUG] Query by user_id:`, {
      count: byUserId?.length || 0,
      data: byUserId,
    });

    // 2. Query by profile_id
    const { data: byProfileId, error: profileIdError } = await supabase
      .from("user_instruments")
      .select("*")
      .eq("profile_id", userId);

    console.log(`[DEBUG] Query by profile_id:`, {
      count: byProfileId?.length || 0,
      data: byProfileId,
    });

    // 3. Get all user_instruments to see the structure
    const { data: allInstruments, error: allError } = await supabase
      .from("user_instruments")
      .select("*")
      .limit(10);

    console.log(`[DEBUG] All instruments (first 10):`, allInstruments);

    // 4. Check if the user has a profile record
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();

    console.log(`[DEBUG] User profile:`, profile);

    return NextResponse.json({
      userId,
      queries: {
        byUserId: {
          count: byUserId?.length || 0,
          data: byUserId,
          error: userIdError,
        },
        byProfileId: {
          count: byProfileId?.length || 0,
          data: byProfileId,
          error: profileIdError,
        },
      },
      allInstruments: allInstruments?.slice(0, 5), // Only return first 5 for privacy
      profile,
      message: `Found ${byUserId?.length || 0} instruments by user_id, ${byProfileId?.length || 0} by profile_id`,
    });
  } catch (error) {
    console.error("[DEBUG] Unexpected error:", error);
    return NextResponse.json(
      {
        error: "Unexpected error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
