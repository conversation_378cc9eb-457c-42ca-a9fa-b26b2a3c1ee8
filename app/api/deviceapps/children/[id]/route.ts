import { NextRequest, NextResponse } from "next/server";
import { fetchChildById } from "@/lib/queries/childQueries";

/**
 * API Endpoint: GET /api/deviceapps/children/:id
 * Get a child by ID for device apps (no authentication required)
 *
 * Response:
 * {
 *   success: boolean,
 *   data?: Child,
 *   error?: string
 * }
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const childId = id;

    if (!childId) {
      return NextResponse.json(
        { success: false, error: "Child ID is required" },
        { status: 400 },
      );
    }

    console.log("Fetching child data for device app:", childId);

    // Get the child data
    const child = await fetchChildById(childId);

    // Return a simplified version of the child data for security
    return NextResponse.json({
      success: true,
      data: {
        id: child.id,
        name: child.name, // Using name field from the Child type
        username: child.username,
        color_scheme: child.color_scheme,
        is_student: child.is_student,
        is_performer: child.is_performer,
        created_at: child.created_at,
      },
    });
  } catch (error: any) {
    console.error("Error fetching child for device app:", error);
    return NextResponse.json(
      { success: false, error: error.message || "Failed to fetch child" },
      { status: 500 },
    );
  }
}
