export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

/**
 * API Endpoint: POST /api/deviceapps/children/login
 * Initiates a login request for a child from a device app
 * This endpoint does not require authentication
 *
 * Request Body:
 * {
 *   username: string
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   child_id?: string,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.username) {
      return NextResponse.json(
        { success: false, error: "Username is required" },
        { status: 400 },
      );
    }


    // Use the SQL function to find the child and generate a login code
    try {
      const { data, error } = await supabase.rpc(
        "find_child_and_generate_code",
        {
          p_username: body.username,
        },
      );


      if (error) {
        console.error("[API] Error finding child and generating code:", error);
        return NextResponse.json(
          { success: false, error: "Error processing login request" },
          { status: 500 },
        );
      }

      if (!data || data.length === 0 || !data[0].success) {
        return NextResponse.json(
          { success: false, error: "Child not found" },
          { status: 404 },
        );
      }

      const result = data[0];

      // Create a login request for the parent to approve
      try {
        const { error: requestError } = await supabase
          .from("child_login_requests")
          .insert({
            child_id: result.child_id,
            device_type: body.deviceType || "Unknown device",
            device_name: body.deviceName || "Unknown",
            device_id: body.deviceId,
            location: body.location || "Unknown location",
            ip_address: request.headers.get("x-forwarded-for") || "Unknown",
            login_code: result.login_code,
            status: "pending",
            expires_at: result.expires_at,
          });

        if (requestError) {
          console.error("[API] Error creating login request:", requestError);
          // Continue anyway, this is not critical
        } else {
        }
      } catch (err) {
        console.error("[API] Error creating login request:", err);
        // Continue anyway, this is not critical
      }

      return NextResponse.json({
        success: true,
        child_id: result.child_id,
        login_code: result.login_code,
        expires_at: result.expires_at,
      });
    } catch (error) {
      console.error("[API] Error finding child:", error);
      return NextResponse.json(
        { success: false, error: "Error finding child" },
        { status: 500 },
      );
    }

    // This code should never be reached due to the returns in the try/catch block above
    return NextResponse.json(
      { success: false, error: "Unexpected error" },
      { status: 500 },
    );
  } catch (error: any) {
    console.error("[API] Error in device app child login API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Failed to process login request",
      },
      { status: 500 },
    );
  }
}

// Helper function to generate a random code
function generateRandomCode(): string {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let code = "";
  for (let i = 0; i < 8; i++) {
    code += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return code;
}
