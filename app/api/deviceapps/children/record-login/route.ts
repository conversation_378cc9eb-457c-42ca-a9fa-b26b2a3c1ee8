export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { U<PERSON>arser } from "ua-parser-js";

/**
 * API Endpoint: POST /api/deviceapps/children/record-login
 * Records device login information for a child
 *
 * Request Body:
 * {
 *   child_id: string,
 *   ip_address?: string (optional, will use request IP if not provided)
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  const cookieStore = await cookies();
  try {
    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.child_id) {
      return NextResponse.json(
        { success: false, error: "Child ID is required" },
        { status: 400 },
      );
    }

    // Get IP address from request or body
    const ipAddress =
      body.ip_address || request.headers.get("x-forwarded-for") || "unknown";

    // Parse user agent
    const userAgent = request.headers.get("user-agent") || "unknown";
    const parser = new UAParser(userAgent);
    const browserInfo = parser.getBrowser();
    const osInfo = parser.getOS();
    const deviceInfo = parser.getDevice();

    // Determine device type
    let deviceType = "desktop";
    if (deviceInfo.type === "mobile") deviceType = "mobile";
    if (deviceInfo.type === "tablet") deviceType = "tablet";
    if (deviceInfo.type === "smarttv") deviceType = "tv";

    // Get browser name
    const browserName = browserInfo.name || "Unknown Browser";

    // Get OS name
    const osName = osInfo.name || "Unknown OS";

    // Create Supabase client
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    try {
      // Try to record login session using the function
      const { data, error } = await supabase.rpc("record_child_login_session", {
        p_child_id: body.child_id,
        p_device_type: deviceType,
        p_browser: browserName,
        p_os: osName,
        p_ip_address: ipAddress,
        p_user_agent: userAgent,
      });

      if (error) {
        console.warn("Error recording login session:", error);

        // If the function fails (possibly because the table doesn't exist),
        // update the child record directly
        try {
          await supabase
            .from("children")
            .update({
              last_login_at: new Date().toISOString(),
              last_login_device: `${deviceType} - ${browserName} (${osName})`,
              last_login_ip: ipAddress,
              login_count: supabase.rpc("increment", {
                row_id: body.child_id,
                table: "children",
                column: "login_count",
              }),
            })
            .eq("id", body.child_id);
        } catch (updateError) {
          console.warn("Error updating child login info:", updateError);
          // Continue anyway, this is not critical
        }
      }
    } catch (err) {
      console.warn("Exception in login recording:", err);
      // Continue anyway, this is not critical
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Error recording login:", error);
    return NextResponse.json(
      { success: false, error: error.message || "Failed to record login" },
      { status: 500 },
    );
  }
}
