import { NextRequest, NextResponse } from "next/server";
import { findChildByUsername } from "@/lib/queries/childQueries";

/**
 * API Endpoint: GET /api/deviceapps/children/username/:username
 * Get a child by username for device apps (no authentication required)
 *
 * Response:
 * {
 *   success: boolean,
 *   data?: Child,
 *   error?: string
 * }
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string }> },
) {
  try {
    const { username } = await params;
    const normalizedUsername = username.toLowerCase();

    if (!normalizedUsername) {
      return NextResponse.json(
        { success: false, error: "Username is required" },
        { status: 400 },
      );
    }

    console.log("Fetching child data for device app by username:", normalizedUsername);

    // Get the child data
    const child = await findChildByUsername(normalizedUsername);

    if (!child) {
      return NextResponse.json(
        { success: false, error: "Child not found" },
        { status: 404 },
      );
    }

    // Return a simplified version of the child data for security
    return NextResponse.json({
      success: true,
      data: {
        id: child.id,
        name: child.name,
        username: child.username,
        color_scheme: child.color_scheme,
        is_student: child.is_student,
        is_performer: child.is_performer,
        is_teacher: child.is_teacher,
        avatar_url: child.avatar_url,
        created_at: child.created_at,
      },
    });
  } catch (error: any) {
    console.error("Error fetching child by username for device app:", error);
    return NextResponse.json(
      { success: false, error: error.message || "Failed to fetch child" },
      { status: 500 },
    );
  }
}
