export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { verifyAccessCode } from "@/lib/queries/deviceQueries";

/**
 * API Endpoint: POST /api/deviceapps/children/verify-code
 * Verifies an access code for a child from a device app
 * This endpoint does not require authentication
 *
 * Request Body:
 * {
 *   accessCode: string
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   child?: object,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    console.log(
      "[API] /api/deviceapps/children/verify-code - Processing request",
    );

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.accessCode) {
      return NextResponse.json(
        { success: false, error: "Access code is required" },
        { status: 400 },
      );
    }

    // Verify the access code
    const result = await verifyAccessCode(body.accessCode);

    if (!result.valid || !result.childId) {
      return NextResponse.json(
        { success: false, error: "Invalid access code" },
        { status: 401 },
      );
    }


    // Get the child profile
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies });

    const { data: child, error } = await supabase
      .from("children")
      .select("*")
      .eq("id", result.childId)
      .eq("is_active", true)
      .single();

    if (error || !child) {
      console.error("[API] Error fetching child profile:", error);
      return NextResponse.json(
        { success: false, error: "Child profile not found" },
        { status: 404 },
      );
    }


    // Register the device login
    const deviceInfo = {
      deviceName: body.deviceName || "Unknown Device",
      deviceType: body.deviceType || "unknown",
      deviceId: body.deviceId || crypto.randomUUID(),
    };

    const { error: deviceError } = await supabase
      .from("child_device_logins")
      .insert({
        child_id: child.id,
        device_name: deviceInfo.deviceName,
        device_type: deviceInfo.deviceType,
        device_id: deviceInfo.deviceId,
        last_login: new Date().toISOString(),
        is_active: true,
      });

    if (deviceError) {
      console.error("[API] Error registering device login:", deviceError);
      // Continue anyway, this is not critical
    } else {
    }

    // Return success response
    const response = {
      success: true,
      child: { id: child.id, username: child.username },
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error("[API] Error in verify access code API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Failed to verify access code",
      },
      { status: 500 },
    );
  }
}
