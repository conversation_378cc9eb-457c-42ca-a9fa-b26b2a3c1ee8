export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
  const cookieStore = await cookies();
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { verifySharedAccessCode } from "@/lib/queries/deviceQueries";

/**
 * API Endpoint: POST /api/deviceapps/children/verify-shared-code
 * Verifies a shared access code for multiple children from a device app
 * This endpoint does not require authentication
 *
 * Request Body:
 * {
 *   accessCode: string
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   children?: object[],
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    console.log(
      "[API] /api/deviceapps/children/verify-shared-code - Processing request",
    );

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.accessCode) {
      return NextResponse.json(
        { success: false, error: "Access code is required" },
        { status: 400 },
      );
    }

    // Verify the shared access code
    const result = await verifySharedAccessCode(body.accessCode);

    if (!result.valid || !result.childIds || result.childIds.length === 0) {

      // Try as a single code instead
      const singleResult = await verifyAccessCode(body.accessCode);

      if (singleResult.valid && singleResult.childId) {
        console.log(
          "[API] Valid single access code, redirecting to single code endpoint",
        );
        return NextResponse.json(
          { success: false, error: "Not a shared code", tryAsSingle: true },
          { status: 303 }, // See Other
        );
      }

      return NextResponse.json(
        { success: false, error: "Invalid access code" },
        { status: 401 },
      );
    }


    // Get the child profiles
    const cookieStore =  cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const { data: children, error } = await supabase
      .from("children")
      .select("*")
      .in("id", result.childIds)
      .eq("is_active", true);

    if (error) {
      console.error("[API] Error fetching child profiles:", error);
      return NextResponse.json(
        { success: false, error: "Error fetching child profiles" },
        { status: 500 },
      );
    }

    if (!children || children.length === 0) {
      return NextResponse.json(
        { success: false, error: "No child profiles found" },
        { status: 404 },
      );
    }

    console.log(
      "[API] Found child profiles:",
      children.map((c) => c.username),
    );

    // Register the device login for each child
    const deviceInfo = {
      deviceName: body.deviceName || "Unknown Device",
      deviceType: body.deviceType || "unknown",
      deviceId: body.deviceId || crypto.randomUUID(),
    };

    for (const child of children) {
      const { error: deviceError } = await supabase
        .from("child_device_logins")
        .insert({
          child_id: child.id,
          device_name: deviceInfo.deviceName,
          device_type: deviceInfo.deviceType,
          device_id: deviceInfo.deviceId,
          last_login: new Date().toISOString(),
          is_active: true,
        });

      if (deviceError) {
        console.error(
          `[API] Error registering device login for child ${child.id}:`,
          deviceError,
        );
        // Continue anyway, this is not critical
      }
    }


    // Return success response
    const response = {
      success: true,
      children,
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error("[API] Error in verify shared access code API:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Failed to verify shared access code",
      },
      { status: 500 },
    );
  }
}
