import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

/**
 * API Endpoint: POST /api/deviceapps/children/verify
 * Verifies a login code for a child from a device app
 * This endpoint does not require authentication
 *
 * Request Body:
 * {
 *   username: string,
 *   code: string
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   child_id?: string,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.username || !body.code) {
      return NextResponse.json(
        { success: false, error: "Username and code are required" },
        { status: 400 },
      );
    }

    console.log(
      "Verifying login for username:",
      body.username,
      "with code:",
      body.code,
    );

    // Format the code (remove dashes and spaces)
    const formattedCode = body.code.replace(/[\s-]/g, "").toUpperCase();
    console.log("Formatted code for verification:", formattedCode);

    // Use a direct SQL query to find the child and verify the code in one step
    // This avoids multiple round trips to the database
    const { data, error } = await supabase.rpc("verify_child_and_code", {
      p_username: body.username,
      p_code: formattedCode,
    });

    if (error) {
      console.error("Error verifying child and code:", error);
      return NextResponse.json(
        { success: false, error: "Error verifying login" },
        { status: 500 },
      );
    }

    console.log("Verification result:", data);

    if (!data || data.length === 0) {
      return NextResponse.json(
        { success: false, error: "Invalid username or code" },
        { status: 400 },
      );
    }

    const result = data[0];

    if (!result.valid) {
      if (result.child_exists) {
        if (result.code_expired) {
          return NextResponse.json(
            { success: false, error: "Login code has expired" },
            { status: 400 },
          );
        } else {
          return NextResponse.json(
            { success: false, error: "Invalid login code" },
            { status: 400 },
          );
        }
      } else {
        return NextResponse.json(
          { success: false, error: "Child not found" },
          { status: 404 },
        );
      }
    }

    // If we get here, the verification was successful
    console.log("Valid login for child ID:", result.child_id);

    // Update the last login time
    const { error: updateError } = await (supabase as any)
      .from("children")
      .update({ last_login: new Date().toISOString() })
      .eq("id", result.child_id);

    if (updateError) {
      console.error("Error updating last login time:", updateError);
      // Continue anyway, this is not critical
    }

    // Get the full child profile
    const { data: childProfile, error: profileError } = await supabase
      .from("children")
      .select("*")
      .eq("id", result.child_id)
      .single();

    if (profileError) {
      console.error("Error getting child profile:", profileError);
      return NextResponse.json(
        { success: false, error: "Error retrieving child profile" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      child: childProfile,
    });
  } catch (error: any) {
    console.error("Error in device app verify login API:", error);
    return NextResponse.json(
      { success: false, error: error.message || "Failed to verify login code" },
      { status: 500 },
    );
  }
}
