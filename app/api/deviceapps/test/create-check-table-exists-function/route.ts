export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: POST /api/deviceapps/test/create-check-table-exists-function
 * Creates a function to check if a table exists
 *
 * Response:
 * {
 *   success: boolean,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Create the function to check if a table exists
    const { error } = await supabase.rpc("create_check_table_exists_function");

    if (error) {
      console.error("Error creating check_table_exists function:", error);
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      message: "Function created successfully",
    });
  } catch (error: any) {
    console.error("Error creating function:", error);
    return NextResponse.json(
      { success: false, error: error.message || "Failed to create function" },
      { status: 500 },
    );
  }
}
