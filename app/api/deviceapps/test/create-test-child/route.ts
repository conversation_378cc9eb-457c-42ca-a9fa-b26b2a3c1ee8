export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: POST /api/deviceapps/test/create-test-child
 * Creates a test child record for testing device apps
 *
 * Response:
 * {
 *   success: boolean,
 *   child?: {
 *     id: string,
 *     username: string
 *   },
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Check if the children table exists
    const { data: tableExists, error: tableError } = await supabase.rpc(
      "check_table_exists",
      { table_name: "children" },
    );

    if (tableError) {
      console.error("Error checking if children table exists:", tableError);

      // Create the function to check if a table exists
      await supabase.rpc("create_check_table_exists_function");

      // Try again
      const { data: retryTableExists, error: retryTableError } =
        await supabase.rpc("check_table_exists", { table_name: "children" });

      if (retryTableError || !retryTableExists) {
        return NextResponse.json(
          { success: false, error: "Children table does not exist" },
          { status: 500 },
        );
      }
    }

    // Check if the test child already exists
    const { data: existingChild, error: existingChildError } = await supabase
      .from("children")
      .select("id, username")
      .eq("username", "testchild")
      .single();

    if (existingChild) {
      console.log("Test child already exists:", existingChild);
      return NextResponse.json({
        success: true,
        child: existingChild,
        message: "Test child already exists",
      });
    }

    // Get the first user ID
    const {
      data: { session },
    } = await supabase.auth.getSession();
    const userId = session?.user?.id;

    if (!userId) {
      return NextResponse.json(
        { success: false, error: "No authenticated user found" },
        { status: 401 },
      );
    }

    // Create the test child
    const { data: newChild, error: newChildError } = await supabase
      .from("children")
      .insert([
        {
          user_id: userId,
          first_name: "Test",
          last_name: "Child",
          username: "testchild",
          date_of_birth: "2010-01-01",
          is_active: true,
          is_student: true,
          is_performer: false,
          is_teacher: false,
          color_scheme: "purple",
          login_permission: "permanent",
        },
      ])
      .select("id, username")
      .single();

    if (newChildError) {
      console.error("Error creating test child:", newChildError);
      return NextResponse.json(
        { success: false, error: newChildError.message },
        { status: 500 },
      );
    }

    console.log("Test child created successfully:", newChild);
    return NextResponse.json({
      success: true,
      child: newChild,
      message: "Test child created successfully",
    });
  } catch (error: any) {
    console.error("Error in create test child API:", error);
    return NextResponse.json(
      { success: false, error: error.message || "Failed to create test child" },
      { status: 500 },
    );
  }
}
