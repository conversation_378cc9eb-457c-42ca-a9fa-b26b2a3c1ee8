import { NextRequest, NextResponse } from "next/server";
import {
  runNetworkDiagnostics,
  testSpotifyConnectivity,
} from "@/lib/utils/network-diagnostics";

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const quick = url.searchParams.get("quick") === "true";

    if (quick) {
      // Quick Spotify connectivity test
      const result = await testSpotifyConnectivity();
      return NextResponse.json({
        timestamp: new Date().toISOString(),
        test: "spotify-connectivity",
        result,
      });
    } else {
      // Full network diagnostics
      const diagnostics = await runNetworkDiagnostics();
      return NextResponse.json(diagnostics);
    }
  } catch (error: any) {
    return NextResponse.json(
      {
        error: "Failed to run network diagnostics",
        message: error.message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
