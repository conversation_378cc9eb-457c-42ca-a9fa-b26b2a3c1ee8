export const dynamic = "force-dynamic";

import { NextResponse } from "next/server";
import { fetchGearItemById, updateGearItem } from "@/lib/queries/gearQueries";
import {
  withGearAuth,
  GearRequest,
} from "@/lib/middleware/gear-middleware";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

async function handlePOST(
  request: GearRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  const { profileIds } = request.gearAuth;
  const { id } = await params;
  // Validate short UUID
  if (!/^[A-Za-z0-9]{12}$/.test(id)) {
    return NextResponse.json(
      { error: "Invalid gear ID format" },
      { status: 400 },
    );
  }

  // Fetch gear and check ownership
  const gearItem = await fetchGearItemById(id);
  console.log("[FAV] gearItem:", gearItem);
  if (!gearItem || !profileIds.includes(gearItem.profile_id)) {
    return NextResponse.json(
      { error: "Unauthorized or gear not found" },
      { status: 403 },
    );
  }

  // Parse body
  const body = await request.json();
  console.log("[FAV] request body:", body);
  const { favourite } = body;
  if (typeof favourite !== "boolean") {
    return NextResponse.json(
      { error: "Invalid request body" },
      { status: 400 },
    );
  }

  // Prepare tags
  let tags = Array.isArray(gearItem.tags) ? [...gearItem.tags] : [];
  console.log("[FAV] tags before:", tags);
  // Remove existing favourite tag
  tags = tags.filter((t) => t.name !== "favourite");

  if (favourite) {
    // Enforce max 5 favourites
    const supabase = createRouteHandlerClient({ cookies });
    const { count, error: favError } = await supabase
      .from("gear")
      .select("id", { count: "exact", head: true })
      .eq("profile_id", gearItem.profile_id)
      .contains("tags", [{ name: "favourite", value: "true" }]);
    if (favError) {
      console.error("Error counting favourites:", favError);
      return NextResponse.json({ error: favError.message }, { status: 500 });
    }
    if ((count || 0) >= 5) {
      return NextResponse.json(
        { error: `Favourite limit reached (max 5)` },
        { status: 403 },
      );
    }
    // Add favourite tag
    tags.push({ name: "favourite", value: "true" });
  }

  // Ensure tags is always an array of {name, value} strings
  tags = tags.filter(
    (t) => t && typeof t.name === "string" && typeof t.value === "string",
  );
  console.log("[FAV] tags after:", tags);

  try {
    const updated = await updateGearItem(id, { tags });
    console.log("[FAV] updateGearItem result:", updated);
    return NextResponse.json({ success: true, data: updated });
  } catch (error: any) {
    console.error("Error updating favourite:", error);
    return NextResponse.json(
      { success: false, error: error.message || "Failed to update favourite" },
      { status: 500 },
    );
  }
}

export const POST = withGearAuth(handlePOST);
