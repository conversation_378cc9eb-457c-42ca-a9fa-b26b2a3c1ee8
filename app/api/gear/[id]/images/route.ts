import { NextRequest, NextResponse } from "next/server";
import {
  fetchGearItemById,
  uploadGearImage,
  checkImageLimits,
} from "@/lib/queries/gearQueries";
import { withGearAuth, GearRequest } from "@/lib/middleware/gear-middleware";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: GET /api/gear/:id/images
 * Fetches all images for a specific gear item
 *
 * Response:
 * Array of gear images or error
 */
async function handleGET(
  request: GearRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // Get the ID from params (short UUID string)
    const { id } = await params;
    const gearId = id;
    if (!/^[A-Za-z0-9]{12}$/.test(gearId)) {
      return NextResponse.json(
        { error: "Invalid gear ID format" },
        { status: 400 },
      );
    }

    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({
      cookies: async () => cookieStore,
    });
    const { profileIds } = request.gearAuth;

    // Ensure gear accessible
    const { data: gearItem, error: gearErr } = await supabase
      .from("gear")
      .select("id, profile_id, owner_profile_id")
      .eq("id", gearId)
      .maybeSingle();

    if (
      gearErr ||
      !gearItem ||
      (!profileIds.includes(gearItem.profile_id) &&
        !profileIds.includes(gearItem.owner_profile_id))
    ) {
      return NextResponse.json(
        { error: "Gear item not found or unauthorized" },
        { status: 404 },
      );
    }

    const { data, error } = await supabase
      .from("gear_images")
      .select("*")
      .eq("gear_id", gearId)
      .order("created_at", { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data || []);
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: 500 });
  }
}

/**
 * API Endpoint: POST /api/gear/:id/images
 * Uploads a new image for a specific gear item
 *
 * Request Body:
 * FormData with image file and metadata
 *
 * Response:
 * Uploaded image data or error
 */
async function handlePOST(
  request: GearRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // Log cookies and session info for debugging
    const cookieStore = await cookies();
    console.log(
      "[API] POST /api/gear/[id]/images - Cookies:",
      cookieStore.getAll(),
    );
    const supabase = createRouteHandlerClient({
      cookies: async () => cookieStore,
    });
    // If using session, log session info here (if available)
    // e.g., const session = await getSession();

    const { profileIds } = request.gearAuth;
    const { id } = await params;
    const gearId = id;
        if (!/^[A-Za-z0-9]{12}$/.test(gearId)) {
      console.warn("[API] Invalid gear ID format", { gearId });
      return NextResponse.json(
        { error: "Invalid gear ID format" },
        { status: 400 },
      );
    }

    // Get the gear item to check ownership
    const gearItem = await fetchGearItemById(gearId);
    if (!gearItem) {
      console.warn("[API] Gear item not found", { gearId });
      return NextResponse.json(
        { error: "Gear item not found" },
        { status: 404 },
      );
    }

    // Check if the gear item belongs to one of the user's profiles
    if (!profileIds.includes(gearItem.profile_id)) {
      console.warn("[API] Unauthorized: user does not own gear", {
        gearId,
        profileIds,
        gearProfileId: gearItem.profile_id,
      });
      return NextResponse.json(
        { error: "Unauthorized: You do not own this gear item" },
        { status: 403 },
      );
    }

    // Check if the user has reached their image limit
    const { canAddMoreImages, imageCount, maxImages } = await checkImageLimits(
      gearId,
      undefined,
      gearItem.profile_id,
    );

    if (!canAddMoreImages) {
      console.warn("[API] Image limit reached", {
        gearId,
        imageCount,
        maxImages,
      });
      return NextResponse.json(
        {
          error: "Image limit reached",
          message: `You have reached your limit of ${maxImages} images for this gear item.`,
          currentCount: imageCount,
          maxItems: maxImages,
        },
        { status: 403 },
      );
    }

    // Parse the multipart form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const description = formData.get("description") as string;
    const isManufacturerImage =
      formData.get("is_manufacturer_image") === "true";
    const tagsJson = formData.get("tags") as string;
    const tags = tagsJson ? JSON.parse(tagsJson) : undefined;

    if (!file) {
      console.warn("[API] No file provided in upload", { gearId });
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Upload the image
    const imageUpload = {
      file,
      description,
      is_manufacturer_image: isManufacturerImage,
      tags,
    };

    // Use the gear's profile_id for consistency
    const uploadedImage = await uploadGearImage(
      imageUpload,
      gearId,
      undefined,
      gearItem.profile_id,
    );

    return NextResponse.json(uploadedImage, { status: 201 });
  } catch (error: any) {
    console.error("[API] Error uploading gear image:", error);
    return NextResponse.json(
      { error: error.message || "Failed to upload gear image" },
      { status: 500 },
    );
  }
}

// Export the handlers with gear authentication middleware
export const GET = withGearAuth(handleGET);
export const POST = withGearAuth(handlePOST);
