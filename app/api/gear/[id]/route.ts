export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import {
  fetchGearItemById,
  updateGearItem,
  deleteGearItem,
} from "@/lib/queries/gearQueries";
import { withGearAuth, GearRequest } from "@/lib/middleware/gear-middleware";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: GET /api/gear/:id
 * Fetches a specific gear item by ID
 *
 * Response:
 * Gear item or error
 */
async function handleGET(
  request: GearRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({
      cookies: () => cookieStore,
    });

    // Get the ID from params - properly awaited in Next.js 15+
    const { id } = await params;
    const gearId = id; // Keep as string for short UUIDs

    // Validate short UUID format
    if (!/^[A-Za-z0-9]{12}$/.test(gearId)) {
      return NextResponse.json(
        { error: "Invalid gear id format" },
        { status: 400 },
      );
    }

    const { profileIds } = request.gearAuth;

    // Fetch item with category and subcategory joins
    const { data, error } = await supabase
      .from("gear")
      .select(
        `
        *,
        gear_categories ( name ),
        gear_subcategories ( name )
      `,
      )
      .eq("id", gearId)
      .in("profile_id", profileIds);

    if (error) {
      console.error(`Error fetching gear item with ID ${gearId}:`, error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Check if we found exactly one item
    if (!data || data.length === 0) {
      console.error(`Gear item with ID ${gearId} not found or not accessible`);
      return NextResponse.json(
        { error: `Gear item with ID ${gearId} not found` },
        { status: 404 },
      );
    }

    if (data.length > 1) {
      console.warn(
        `Multiple gear items found with ID ${gearId}, returning the first one`,
      );
    }

    // Unwrap the joined data
    const { gear_categories, gear_subcategories, ...gearItem } = data[0];

    // Return the enriched gear item with category names
    return NextResponse.json({
      ...gearItem,
      categoryName: gear_categories?.name || null,
      subcategoryName: gear_subcategories?.name || null,
      // Keep the old fields for backward compatibility temporarily
      category: gear_categories
        ? { id: gearItem.category_id, name: gear_categories.name }
        : null,
      subcategory: gear_subcategories
        ? { id: gearItem.subcategory_id, name: gear_subcategories.name }
        : null,
    });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * API Endpoint: PATCH /api/gear/:id
 * Updates a specific gear item
 *
 * Request Body:
 * Partial gear item data
 *
 * Response:
 * Updated gear item or error
 */
async function handlePATCH(
  request: GearRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { profileIds } = request.gearAuth;
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({
      cookies: () => cookieStore,
    });

    // Get the ID from params - properly awaited in Next.js 15+
    const { id } = await params;
    const gearId = id; // Keep as string for short UUIDs

    // Validate short UUID format
    if (!/^[A-Za-z0-9]{12}$/.test(gearId)) {
      return NextResponse.json(
        { error: "Invalid gear ID format" },
        { status: 400 },
      );
    }

    // Parse the request body early for logging
    const gearData = await request.json();

    // Filter out non-schema fields from gearData
    const allowedFields = [
      "owner_profile_id",
      "owner_org_id",
      "name",
      "status",
      "details",
      "created_at",
      "updated_at",
      "profile_id",
      "color",
      "color_hex",
      "size",
      "manufactured_year",
      "insurer",
      "insured_amount",
      "policy_number",
      "brand",
      "brand_id",
      "category_id",
      "current_value",
      "description",
      "is_active",
      "model",
      "ownership_status",
      "purchase_date",
      "purchase_price",
      "purpose",
      "serial_number",
      "storage_location",
      "subcategory_id",
      "tags",
      "subitems",
      "history",
      "id",
    ];
    const filteredGearData = Object.fromEntries(
      Object.entries(gearData).filter(([key]) => allowedFields.includes(key)),
    );
    console.log("[PATCH] filteredGearData (for update):", filteredGearData);

    // Add logging
    console.log("[PATCH] gearId:", gearId);
        console.log("[PATCH] PATCH body (gearData):", gearData);

    // Get the existing gear item to check ownership
    const { data: existingGear, error: fetchError } = await supabase
      .from("gear")
      .select("*")
      .eq("id", gearId)
      .in("profile_id", profileIds);

    console.log("[PATCH] existingGear:", existingGear);
    if (fetchError) {
      console.error(`Error fetching gear item with ID ${gearId}:`, fetchError);
      return NextResponse.json({ error: fetchError.message }, { status: 500 });
    }

    if (!existingGear || existingGear.length === 0) {
      console.error(`Gear item with ID ${gearId} not found or not accessible`);
      return NextResponse.json(
        { error: `Gear item with ID ${gearId} not found` },
        { status: 404 },
      );
    }

    const existingGearItem = existingGear[0];

    // Ensure the profile_id isn't being changed to an unauthorized profile
    if (gearData.profile_id && !profileIds.includes(gearData.profile_id)) {
      return NextResponse.json(
        { error: "Cannot assign gear to an unauthorized profile" },
        { status: 403 },
      );
    }

    // Update the gear item directly with authenticated client
    const { data: updatedRows, error: updateError } = await supabase
      .from("gear")
      .update(filteredGearData)
      .eq("id", gearId)
      .select();

    if (updateError) {
      console.error(`Error updating gear item with ID ${gearId}:`, updateError);
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    const updatedItem = updatedRows && updatedRows[0];

    // Re-fetch enriched data
    const { data: enrichedData, error: enrichError } = await supabase
      .from("gear")
      .select(
        `
        *,
        gear_categories ( name ),
        gear_subcategories ( name )
      `,
      )
      .eq("id", gearId)
      .single();

    if (enrichError || !enrichedData) {
      return NextResponse.json(updatedItem);
    }

    const { gear_categories, gear_subcategories, ...enrichedItem } =
      enrichedData;
    return NextResponse.json({
      ...enrichedItem,
      categoryName: gear_categories?.name || null,
      subcategoryName: gear_subcategories?.name || null,
      category: gear_categories
        ? { id: enrichedItem.category_id, name: gear_categories.name }
        : null,
      subcategory: gear_subcategories
        ? { id: enrichedItem.subcategory_id, name: gear_subcategories.name }
        : null,
    });
  } catch (error: any) {
    console.error("Error updating gear item:", error);
    return NextResponse.json(
      { error: error.message || "Failed to update gear item" },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: DELETE /api/gear/:id
 * Deletes a specific gear item
 *
 * Response:
 * Success message or error
 */
async function handleDELETE(
  request: GearRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { profileIds } = request.gearAuth;
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({
      cookies: () => cookieStore,
    });

    // Get the ID from params - properly awaited in Next.js 15+
    const { id } = await params;
    const gearId = id; // Keep as string for short UUIDs

    // Validate short UUID format
    if (!/^[A-Za-z0-9]{12}$/.test(gearId)) {
      return NextResponse.json(
        { error: "Invalid gear ID format" },
        { status: 400 },
      );
    }

    // Get the existing gear item to check ownership
    const { data: existingGear, error: fetchError } = await supabase
      .from("gear")
      .select("*")
      .eq("id", gearId)
      .in("profile_id", profileIds);

    if (fetchError) {
      console.error(`Error fetching gear item with ID ${gearId}:`, fetchError);
      return NextResponse.json({ error: fetchError.message }, { status: 500 });
    }

    if (!existingGear || existingGear.length === 0) {
      console.error(`Gear item with ID ${gearId} not found or not accessible`);
      return NextResponse.json(
        { error: `Gear item with ID ${gearId} not found` },
        { status: 404 },
      );
    }

    // Delete the gear item
    await deleteGearItem(gearId);

    return NextResponse.json({
      success: true,
      message: "Gear item deleted successfully",
    });
  } catch (error: any) {
    console.error("Error deleting gear item:", error);
    return NextResponse.json(
      { error: error.message || "Failed to delete gear item" },
      { status: 500 },
    );
  }
}

// Export the handlers with gear authentication middleware
export const GET = withGearAuth(handleGET);
export const PATCH = withGearAuth(handlePATCH);
export const DELETE = withGearAuth(handleDELETE);
