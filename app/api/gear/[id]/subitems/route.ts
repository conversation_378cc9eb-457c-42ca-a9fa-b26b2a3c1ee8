import { NextRequest, NextResponse } from "next/server";
import {
  fetchGearItemById,
  createGearSubitem,
  checkSubitemLimits,
  fetchGearSubitems,
} from "@/lib/queries/gearQueries";
import { withGearAuth, GearRequest } from "@/lib/middleware/gear-middleware";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: GET /api/gear/:id/subitems
 * Fetches all subitems for a specific gear item
 *
 * Response:
 * Array of gear subitems or error
 */
async function handleGET(
  request: GearRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({
      cookies: () => cookieStore,
    });

    // Get the ID from params
    const { id } = await params;
    const gearId = id;

    // Validate short UUID format
    if (!/^[A-Za-z0-9]{12}$/.test(gearId)) {
      return NextResponse.json(
        { error: "Invalid gear ID format" },
        { status: 400 },
      );
    }

    const { profileIds } = request.gearAuth;

    // First verify the gear item belongs to the user
    const { data: gearItem, error: gearError } = await supabase
      .from("gear")
      .select("id, profile_id")
      .eq("id", gearId)
      .in("profile_id", profileIds)
      .single();

    if (gearError || !gearItem) {
      return NextResponse.json(
        { error: "Gear item not found or unauthorized" },
        { status: 404 },
      );
    }

    // Fetch subitems
    const subitems = await fetchGearSubitems(gearId);
    return NextResponse.json(subitems);
  } catch (error: any) {
    console.error("Error fetching subitems:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * API Endpoint: POST /api/gear/:id/subitems
 * Creates a new subitem for a specific gear item
 *
 * Request Body:
 * Gear subitem data
 *
 * Response:
 * Created gear subitem or error
 */
async function handlePOST(
  request: GearRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { userId, profileIds } = request.gearAuth;

    // Get the ID from params - properly awaited in Next.js 15+
    const { id } = await params;
    const gearId = id;  // short-string UUID
    // Validate short UUID format
    if (!/^[A-Za-z0-9]{12}$/.test(gearId)) {
      return NextResponse.json({ error: "Invalid gear ID" }, { status: 400 });
    }

    // Get the gear item to check ownership
    const gearItem = await fetchGearItemById(gearId);

    if (!gearItem) {
      return NextResponse.json(
        { error: "Gear item not found" },
        { status: 404 }
      );
    }

    // Check if the gear item belongs to one of the user's profiles
    if (!profileIds.includes(gearItem.profile_id)) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Check if the user has reached their subitem limit
    const { canAddMoreSubitems, subitemCount, maxSubitems, isPaidUser } =
      await checkSubitemLimits(gearId, gearItem.profile_id);

    if (!isPaidUser) {
      return NextResponse.json(
        {
          error: "Subscription required",
          message:
            "Adding subitems requires a paid subscription. Please upgrade to add subitems.",
          currentCount: subitemCount,
          maxItems: maxSubitems,
          requiresUpgrade: true,
        },
        { status: 403 },
      );
    }

    if (!canAddMoreSubitems) {
      return NextResponse.json(
        {
          error: "Subitem limit reached",
          message: `You have reached your limit of ${maxSubitems} subitems for this gear item.`,
          currentCount: subitemCount,
          maxItems: maxSubitems,
        },
        { status: 403 },
      );
    }

    // Parse the request body
    const subitemData = await request.json();

    // Create the subitem - use the same profile_id as the parent gear item
    const newSubitem = await createGearSubitem(
      subitemData,
      gearId as unknown as number,
      gearItem.profile_id,
    );

    return NextResponse.json(newSubitem, { status: 201 });
  } catch (error: any) {
    console.error("Error creating gear subitem:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create gear subitem" },
      { status: 500 },
    );
  }
}

// Export the handlers with gear authentication middleware
export const GET = withGearAuth(handleGET);
export const POST = withGearAuth(handlePOST);
