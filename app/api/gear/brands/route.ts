import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { addCustomBrand } from "@/lib/queries/gearQueries";
import { levenshteinDistance } from "@/lib/utils/string-utils";

/**
 * API Endpoint: GET /api/gear/brands
 * Searches for gear brands based on a query string
 *
 * This endpoint allows unauthenticated access for searching existing brands
 * to support the gear form, but requires authentication for other operations.
 *
 * Query Parameters:
 * - q: Search query string
 *
 * Response:
 * Array of matching brands or error
 */
export async function GET(request: NextRequest) {
  try {
    // Use service role client for reading brands (public data)
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

    if (!supabaseServiceKey) {
      console.error("[GEAR-BRANDS-API] Missing SUPABASE_SERVICE_ROLE_KEY");
      return NextResponse.json(
        { error: "Server configuration error" },
        { status: 500 },
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get the search query from URL parameters
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get("q") || "";

    console.log(
      `[GEAR-BRANDS-API] Searching for brands with query: "${query}"`,
    );

    // If query is empty, return top brands
    if (!query || query.trim() === "") {
      const { data, error } = await supabase
        .from("gear_brands")
        .select("*")
        .order("usage_count", { ascending: false })
        .limit(10);

      if (error) {
        console.error("[GEAR-BRANDS-API] Error fetching top brands:", error);
        throw error;
      }

      console.log(`[GEAR-BRANDS-API] Found ${data.length} top brands`);
      return NextResponse.json({ exact: [], suggestions: data });
    }

    // Normalize the query
    const normalizedQuery = query.trim().toLowerCase();

    // Search for exact matches first
    const { data: exactMatches, error: exactError } = await supabase
      .from("gear_brands")
      .select("*")
      .ilike("name", `${normalizedQuery}%`)
      .order("usage_count", { ascending: false })
      .limit(5);

    if (exactError) {
      console.error(
        "[GEAR-BRANDS-API] Error fetching exact matches:",
        exactError,
      );
      throw exactError;
    }

    console.log(`[GEAR-BRANDS-API] Found ${exactMatches.length} exact matches`);

    // If we have exact matches, return them
    if (exactMatches.length > 0) {
      return NextResponse.json({
        exact: exactMatches,
        suggestions: [],
      });
    }

    // Get all brands for fuzzy matching if needed
    const { data: allBrands, error: allError } = await supabase
      .from("gear_brands")
      .select("*");

    if (allError) {
      console.error(
        "[GEAR-BRANDS-API] Error fetching all brands for fuzzy matching:",
        allError,
      );
      throw allError;
    }

    console.log(
      `[GEAR-BRANDS-API] Performing fuzzy matching on ${allBrands.length} brands`,
    );

    // Perform fuzzy matching for suggestions
    const suggestions = allBrands
      .map((brand) => ({
        brand,
        distance: levenshteinDistance(
          normalizedQuery,
          brand.name.toLowerCase(),
        ),
      }))
      .filter((item) => item.distance <= 3) // Only include close matches
      .sort((a, b) => a.distance - b.distance) // Sort by closest match
      .slice(0, 5) // Limit to 5 suggestions
      .map((item) => item.brand);

    console.log(
      `[GEAR-BRANDS-API] Found ${suggestions.length} fuzzy suggestions`,
    );

    return NextResponse.json({
      exact: [],
      suggestions: suggestions,
    });
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    console.error("Error searching brands:", error);
    return NextResponse.json(
      { error: errorMessage || "Failed to search brands" },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: POST /api/gear/brands
 * Adds a new custom brand
 *
 * This endpoint requires authentication as it modifies data.
 *
 * Request Body:
 * - name: Brand name
 *
 * Response:
 * Created brand or error
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current user from Supabase auth using consistent pattern
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        { error: "Authentication required to add custom brands" },
        { status: 401 },
      );
    }

    const user = session.user;
    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        { error: "Authentication required to add custom brands" },
        { status: 401 },
      );
    }

    // Parse the request body
    const { name } = await request.json();

    if (!name || typeof name !== "string" || name.trim() === "") {
      return NextResponse.json(
        { error: "Brand name is required" },
        { status: 400 },
      );
    }

    console.log(
      `[GEAR-BRANDS-API] User ${user.id} adding custom brand: "${name}"`,
    );

    // Add the custom brand
    const brand = await addCustomBrand(name.trim());

    console.log(`[GEAR-BRANDS-API] Successfully added custom brand:`, brand);

    return NextResponse.json(brand, { status: 201 });
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    console.error("Error adding custom brand:", error);
    return NextResponse.json(
      { error: errorMessage || "Failed to add custom brand" },
      { status: 500 },
    );
  }
}
