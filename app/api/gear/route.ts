export const dynamic = "force-dynamic";

import { NextResponse } from "next/server";
import { withGearAuth, GearRequest } from "@/lib/middleware/gear-middleware";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import {
  fetchGearItems,
  createGearItem,
  checkGearLimits,
} from "@/lib/queries/gearQueries";
import { generateUniqueShortUUID } from "@/lib/utils/uuid";

/**
 * API Endpoint: GET /api/gear
 * Fetches all gear items for the authenticated user
 *
 * Response:
 * Array of gear items or error
 */
async function handleGET(request: GearRequest) {
  try {
    // Initialise user-scoped Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get all profile IDs the user has access to
    const { profileIds, userId } = request.gearAuth;

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const profileId = searchParams.get("profileId");

    // Debug: Fetching gear items for user

    const targetProfileIds =
      profileId && profileIds.includes(profileId) ? [profileId] : profileIds;

    // Fetch all gear items with category and subcategory joins
    const { data: allGearItems, error } = await supabase
      .from("gear")
      .select(
        `
        *,
        gear_categories ( name ),
        gear_subcategories ( name ),
        subitem_count:gear_subitems(count),
        image_count:gear_images(count)
      `,
      )
      .in("profile_id", targetProfileIds)
      .order("created_at", { ascending: false });

    if (error) {
      console.error(`Error fetching gear items:`, error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Transform the data to include category names and counts
    const enrichedItems = allGearItems.map((item) => {
      const {
        gear_categories,
        gear_subcategories,
        subitem_count,
        image_count,
        ...rest
      } = item;

      return {
        ...rest,
        categoryName: gear_categories?.name || null,
        subcategoryName: gear_subcategories?.name || null,
        // Keep backward compatibility
        category: gear_categories
          ? { id: rest.category_id, name: gear_categories.name }
          : null,
        subcategory: gear_subcategories
          ? { id: rest.subcategory_id, name: gear_subcategories.name }
          : null,
        subitem_count: subitem_count?.[0]?.count || 0,
        image_count: image_count?.[0]?.count || 0,
      };
    });

    // Success: Fetched gear items

    return NextResponse.json(enrichedItems);
  } catch (error: any) {
    console.error("Error in gear API:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch gear items" },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: POST /api/gear
 * Creates a new gear item for the authenticated user
 *
 * Request Body:
 * Gear item data
 *
 * Response:
 * Created gear item or error
 */
async function handlePOST(request: GearRequest) {
  try {
    // Initialise user-scoped Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const { userId, profileIds } = request.gearAuth;

    // Parse the request body
    const gearData = await request.json();

    // Ensure the gear is associated with a valid profile
    const targetProfileId = gearData.profile_id || profileIds[0];

    // Verify the target profile is accessible to this user
    if (!profileIds.includes(targetProfileId)) {
      // User doesn't have access to this profile
      return NextResponse.json(
        { error: "You don't have permission to add gear to this profile" },
        { status: 403 },
      );
    }

    // Check if the user has reached their gear limit using the target profile ID
    const { canAddMoreGear, gearCount, maxGearItems } =
      await checkGearLimits(targetProfileId);

    if (!canAddMoreGear) {
      // Profile has reached gear limit
      return NextResponse.json(
        {
          error: "Gear limit reached",
          message: `You have reached your limit of ${maxGearItems} gear items. Upgrade to add more.`,
          currentCount: gearCount,
          maxItems: maxGearItems,
        },
        { status: 403 },
      );
    }

    // Generate a unique short UUID for this gear item
    // Note: After migration, the database will handle this automatically via DEFAULT
    // const shortId = await generateUniqueShortUUID(supabase, 'gear');
    //     // Prepare the gear data
    // Only use columns that we know exist based on the database schema
    const newGearData = {
      // id: shortId, // Remove this - let the database generate the short UUID
      ...gearData,
      profile_id: targetProfileId,
      owner_profile_id: targetProfileId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      // Ensure JSONB fields have valid JSON values
      details: gearData.details || {},
      purpose: gearData.purpose || {},
      tags: gearData.tags || [],
      history: gearData.history || [],
    };

    // Set current_value to purchase_price if current_value is not provided but purchase_price is
    if (!newGearData.current_value && newGearData.purchase_price) {
      newGearData.current_value = newGearData.purchase_price;
    }

    // Remove any undefined values to avoid database errors
    Object.keys(newGearData).forEach((key) => {
      if (newGearData[key] === undefined) {
        delete newGearData[key];
      }
    });

        // Insert with Supabase - use insert() without .single() to avoid the error
    const { data, error } = await supabase
      .from("gear")
      .insert(newGearData)
      .select();

    if (error) {
      console.error("[API] Error inserting gear item:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Check if we got data back
    if (!data || data.length === 0) {
      console.error("[API] No data returned from insert operation");
      return NextResponse.json(
        { error: "Failed to create gear item - no data returned" },
        { status: 500 },
      );
    }

    // Get the first (and should be only) item
    const createdItem = data[0];
        return NextResponse.json(createdItem, { status: 201 });
  } catch (error: any) {
    console.error("[API] Error creating gear item:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create gear item" },
      { status: 500 },
    );
  }
}

// Export the handlers with gear authentication middleware
export const GET = withGearAuth(handleGET);
export const POST = withGearAuth(handlePOST);
