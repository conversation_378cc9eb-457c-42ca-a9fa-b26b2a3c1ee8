import type { GearOwnershipStatus, GearPurpose } from "@/lib/types/gear";
import { NextResponse } from "next/server";
import { withGearAuth, GearRequest } from "@/lib/middleware/gear-middleware";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import {
  createGearItem,
  updateGearItem,
  deleteGearItem,
} from "@/lib/queries/gearQueries";

/**
 * API Endpoint: GET /api/gear/test
 * A simple test endpoint to verify the gear middleware is working correctly
 *
 * Response:
 * The auth context from the middleware
 */
async function handleGET(request: GearRequest) {
  try {
    // Get all profile IDs the user has access to
    const { profileIds, userId, profile } = request.gearAuth;

    // Fetch some gear items for testing
    let gearItems = [];
    let fetchErrors = [];
    let tableInfo = null;

    try {
      // Get Supabase client for server-side operations
      const cookieStore = await cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

      // Check the gear table structure
      const { data: tableData, error: tableError } = await supabase
        .from("gear")
        .select("*")
        .limit(1);

      if (tableError) {
        console.error("Error checking gear table:", tableError);
        fetchErrors.push({
          type: "table_check",
          error: tableError.message,
        });
      } else {
        // Get column names from the first row
        const columnNames =
          tableData && tableData.length > 0 ? Object.keys(tableData[0]) : [];

        tableInfo = {
          hasData: tableData && tableData.length > 0,
          columnNames,
          sample: tableData && tableData.length > 0 ? tableData[0] : null,
        };
      }

      // Try direct query with different column names
      for (const profileId of profileIds) {
        try {
          // Try with profile_id
          const { data: profileIdData, error: profileIdError } = await supabase
            .from("gear")
            .select("*")
            .eq("profile_id", profileId);

          if (!profileIdError && profileIdData && profileIdData.length > 0) {
            console.log(
              `[GearTest] Found ${profileIdData.length} items with profile_id=${profileId}`,
            );
            gearItems.push(...profileIdData);
          }

          // Try with owner_profile_id
          const { data: ownerProfileIdData, error: ownerProfileIdError } =
            await supabase
              .from("gear")
              .select("*")
              .eq("owner_profile_id", profileId);

          if (
            !ownerProfileIdError &&
            ownerProfileIdData &&
            ownerProfileIdData.length > 0
          ) {
            console.log(
              `[GearTest] Found ${ownerProfileIdData.length} items with owner_profile_id=${profileId}`,
            );
            gearItems.push(...ownerProfileIdData);
          }

          // We know user_id doesn't exist, so we'll skip that query
          console.log(
            `[GearTest] Skipping user_id query as the column doesn't exist`,
          );

          // Just for debugging, let's log the error we would get
          const { error: userIdError } = await supabase
            .from("gear")
            .select("*")
            .eq("profile_id", profileId); // Use profile_id instead, just to have a valid query

          if (userIdError) {
            console.log(`[GearTest] Query error: ${userIdError.message}`);
          }

          // If all direct queries failed, try the API
          if (gearItems.length === 0) {
            const response = await fetch(
              `${request.nextUrl.origin}/api/gear?profileId=${profileId}`,
              {
                headers: {
                  Cookie: request.headers.get("cookie") || "",
                },
              },
            );

            if (response.ok) {
              const items = await response.json();
              console.log(
                `[GearTest] API returned ${items.length} items for profileId=${profileId}`,
              );
              gearItems.push(...items);
            } else {
              fetchErrors.push({
                profileId,
                status: response.status,
                statusText: response.statusText,
              });
            }
          }
        } catch (fetchError) {
          fetchErrors.push({
            profileId,
            error:
              fetchError instanceof Error
                ? fetchError.message
                : String(fetchError),
          });
        }
      }
    } catch (fetchAllError) {
      console.error("Error fetching gear items:", fetchAllError);
    }

    // Return the auth context and test results
    return NextResponse.json({
      success: true,
      message: "Gear middleware test successful",
      authContext: request.gearAuth,
      testResults: {
        gearItemsCount: gearItems.length,
        gearItems: gearItems.slice(0, 5), // Only return the first 5 items to avoid large responses
        tableInfo,
        fetchErrors,
        requestInfo: {
          url: request.nextUrl.toString(),
          method: request.method,
          headers: Object.fromEntries(request.headers.entries()),
          cookies: request.cookies.getAll().map((c) => c.name),
        },
      },
    });
  } catch (error: any) {
    console.error("Error in gear middleware test:", error);
    return NextResponse.json(
      {
        error: error.message || "Failed to test gear middleware",
        stack: error.stack,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: POST /api/gear/test
 * A test endpoint to verify INSERT, UPDATE, and DELETE operations on the gear table
 *
 * Response:
 * Results of the operations
 */
async function handlePOST(request: GearRequest) {
  try {
    const { profileIds } = request.gearAuth;
    const profileId = profileIds[0]; // Use the first profile ID for testing

    // Test INSERT operation
    const newGearData = {
      name: "Test Gear Item",
      category_id: 1,                   // category_id must be a number
      purpose: ["learning"] as GearPurpose[],
      profile_id: profileId,
      ownership_status: "owned" as GearOwnershipStatus, // required by GearFormData
      is_active: true,                  // required by GearFormData
    };
    const newGear = await createGearItem(newGearData, profileId);
    console.log("[GearTest] INSERT operation successful:", newGear);

    // Test UPDATE operation
    const updatedGearData = { name: "Updated Test Gear Item" };
    const updatedGear = await updateGearItem(
      String(newGear.id),
      updatedGearData
    );
    console.log("[GearTest] UPDATE operation successful:", updatedGear);

    // Test DELETE operation
    await deleteGearItem(String(newGear.id));
    console.log("[GearTest] DELETE operation successful for ID:", newGear.id);

    return NextResponse.json({
      success: true,
      message: "POST operations successful",
      results: { newGear, updatedGear },
    });
  } catch (error: any) {
    console.error("Error in POST operations:", error);
    return NextResponse.json(
      {
        error: error.message || "Failed to perform POST operations",
        stack: error.stack,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

// Export the handlers with gear authentication middleware
export const GET = withGearAuth(handleGET);
export const POST = withGearAuth(handlePOST);
