import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get("q");

  if (!query) {
    return NextResponse.json(
      { error: "Search query is required" },
      { status: 400 },
    );
  }

  const GENIUS_API_KEY = process.env.GENIUS_API_KEY;

  if (!GENIUS_API_KEY) {
    return NextResponse.json(
      { error: "Genius API key is not configured" },
      { status: 500 },
    );
  }

  try {
    const response = await fetch(
      `https://api.genius.com/search?q=${encodeURIComponent(query)}`,
      {
        headers: {
          Authorization: `Bearer ${GENIUS_API_KEY}`,
        },
      },
    );

    if (!response.ok) {
      throw new Error(
        `Genius API returned ${response.status}: ${response.statusText}`,
      );
    }

    const data = await response.json();
    return NextResponse.json(data.response);
  } catch (error) {
    console.error("Error searching Genius:", error);
    return NextResponse.json(
      { error: "Failed to search Genius API" },
      { status: 500 },
    );
  }
}
