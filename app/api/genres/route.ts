export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/genres - list genres in user's library ranked by count
export async function GET(request: NextRequest) {
  // Next.js requires cookies() to be read before use
  const cookieStore = cookies();
  const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 },
    );
  }

  // Fetch all tags from user's songs
  const { data, error } = await supabase
    .from("songs")
    .select("tags")
    .eq("user_id", user.id);
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  // Count occurrences of each tag
  const counts: Record<string, number> = {};
  (data || []).forEach((row: any) => {
    if (Array.isArray(row.tags)) {
      row.tags.forEach((tag: string) => {
        if (tag) {
          // Normalize tag to lowercase for grouping
          const key = tag.trim().toLowerCase();
          counts[key] = (counts[key] || 0) + 1;
        }
      });
    }
  });
  const genres = Object.entries(counts)
    .map(([genre, count]) => ({ genre, count }))
    .sort((a, b) => b.count - a.count);

  return NextResponse.json(genres);
}
