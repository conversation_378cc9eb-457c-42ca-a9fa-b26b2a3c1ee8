export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: POST /api/notifications/device-token
 * Registers a device token for push notifications
 *
 * Request Body:
 * {
 *   token: string,
 *   device_type: string, // 'ios', 'android', 'web'
 *   device_name?: string
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies });

    // Get the session first to ensure we have a valid session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.token || !body.device_type) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required fields: token and device_type",
        },
        { status: 400 },
      );
    }

    // Validate device_type
    if (!["ios", "android", "web"].includes(body.device_type)) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid device_type. Must be 'ios', 'android', or 'web'",
        },
        { status: 400 },
      );
    }

    // Check if the token already exists
    const { data: existingToken, error: fetchError } = await supabase
      .from("device_tokens")
      .select("*")
      .eq("token", body.token)
      .single();

    if (fetchError && fetchError.code !== "PGRST116") {
      // PGRST116 is "no rows returned"
      console.error("Error fetching device token:", fetchError);
      return NextResponse.json(
        { success: false, error: "Failed to fetch device token" },
        { status: 500 },
      );
    }

    let error;

    // If token exists, update it
    if (existingToken) {
      const { error: updateError } = await supabase
        .from("device_tokens")
        .update({
          user_id: session.user.id,
          device_type: body.device_type,
          device_name: body.device_name,
          last_used_at: new Date().toISOString(),
        })
        .eq("token", body.token);

      error = updateError;
    }
    // If token doesn't exist, create it
    else {
      const { error: insertError } = await supabase
        .from("device_tokens")
        .insert({
          user_id: session.user.id,
          token: body.token,
          device_type: body.device_type,
          device_name: body.device_name,
        });

      error = insertError;
    }

    if (error) {
      console.error("Error registering device token:", error);
      return NextResponse.json(
        { success: false, error: "Failed to register device token" },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Unexpected error in device-token API:", error);
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: DELETE /api/notifications/device-token
 * Deletes a device token
 *
 * Request Body:
 * {
 *   token: string
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   error?: string
 * }
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies });

    // Get the session first to ensure we have a valid session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.token) {
      return NextResponse.json(
        { success: false, error: "Missing required field: token" },
        { status: 400 },
      );
    }

    // Delete the token
    const { error } = await supabase
      .from("device_tokens")
      .delete()
      .eq("token", body.token)
      .eq("user_id", session.user.id);

    if (error) {
      console.error("Error deleting device token:", error);
      return NextResponse.json(
        { success: false, error: "Failed to delete device token" },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Unexpected error in device-token API:", error);
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 },
    );
  }
}
