export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: PATCH /api/notifications/mark-read
 * Marks one or more notifications as read
 *
 * Request Body:
 * {
 *   notification_ids?: string[], // Optional: Specific notification IDs to mark as read
 *   all?: boolean               // Optional: If true, mark all notifications as read
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   error?: string
 * }
 */
export async function PATCH(request: NextRequest) {
  const cookieStore = await cookies();
  try {
    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the session first to ensure we have a valid session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate that at least one option is provided
    if (!body.notification_ids && !body.all) {
      return NextResponse.json(
        { success: false, error: "Must provide notification_ids or all=true" },
        { status: 400 },
      );
    }

    let error;

    // Mark all notifications as read
    if (body.all) {
      const { error: updateError } = await supabase
        .from("notifications")
        .update({ read: true })
        .eq("user_id", session.user.id)
        .eq("read", false);

      error = updateError;
    }
    // Mark specific notifications as read
    else if (body.notification_ids && body.notification_ids.length > 0) {
      const { error: updateError } = await supabase
        .from("notifications")
        .update({ read: true })
        .eq("user_id", session.user.id)
        .in("id", body.notification_ids);

      error = updateError;
    }

    if (error) {
      console.error("Error marking notifications as read:", error);
      return NextResponse.json(
        { success: false, error: "Failed to mark notifications as read" },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Unexpected error in mark-read API:", error);
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 },
    );
  }
}
