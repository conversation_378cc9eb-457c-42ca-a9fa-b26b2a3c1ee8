export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: PATCH /api/notifications/mute-category
 * Mutes or unmutes a notification category
 *
 * Request Body:
 * {
 *   category: string,  // The category to mute/unmute
 *   muted: boolean     // Whether to mute (true) or unmute (false)
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   error?: string
 * }
 */
export async function PATCH(request: NextRequest) {
  const cookieStore = await cookies();
  try {
    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the session first to ensure we have a valid session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (body.category === undefined || body.muted === undefined) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required fields: category and muted",
        },
        { status: 400 },
      );
    }

    // Check if the preference exists
    const { data: existingPreference, error: fetchError } = await supabase
      .from("notification_preferences")
      .select("*")
      .eq("user_id", session.user.id)
      .eq("category", body.category)
      .single();

    if (fetchError && fetchError.code !== "PGRST116") {
      // PGRST116 is "no rows returned"
      console.error("Error fetching notification preference:", fetchError);
      return NextResponse.json(
        { success: false, error: "Failed to fetch notification preference" },
        { status: 500 },
      );
    }

    let error;

    // If preference exists, update it
    if (existingPreference) {
      const { error: updateError } = await supabase
        .from("notification_preferences")
        .update({
          muted: body.muted,
          updated_at: new Date().toISOString(),
        })
        .eq("user_id", session.user.id)
        .eq("category", body.category);

      error = updateError;
    }
    // If preference doesn't exist, create it
    else {
      const { error: insertError } = await supabase
        .from("notification_preferences")
        .insert({
          user_id: session.user.id,
          category: body.category,
          muted: body.muted,
          in_app: true,
          email: true,
          push: body.category === "critical", // Default push to true only for critical
        });

      error = insertError;
    }

    if (error) {
      console.error("Error updating notification preference:", error);
      return NextResponse.json(
        { success: false, error: "Failed to update notification preference" },
        { status: 500 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Unexpected error in mute-category API:", error);
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 },
    );
  }
}
