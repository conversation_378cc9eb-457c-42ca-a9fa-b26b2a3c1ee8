export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { sendNotification } from "@/lib/notifications/send-notification";

/**
 * API Endpoint: POST /api/notifications/send
 * Sends a notification to a user
 *
 * Request Body:
 * {
 *   user_id: string,
 *   category: 'critical' | 'informational' | 'social',
 *   type: string,
 *   title: string,
 *   message: string,
 *   link?: string,
 *   metadata?: object
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   results?: {
 *     in_app: boolean,
 *     email: boolean,
 *     push: boolean,
 *     muted: boolean
 *   },
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  const cookieStore = await cookies();
  try {
    // Get the current user from Supabase auth
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the session first to ensure we have a valid session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (
      !body.user_id ||
      !body.category ||
      !body.type ||
      !body.title ||
      !body.message
    ) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 },
      );
    }

    // Validate category
    if (!["critical", "informational", "social"].includes(body.category)) {
      return NextResponse.json(
        {
          success: false,
          error:
            "Invalid category. Must be 'critical', 'informational', or 'social'",
        },
        { status: 400 },
      );
    }

    // Check if the user has permission to send notifications
    // Only allow sending notifications to the current user or if the user is an admin
    const isAdmin = session.user.app_metadata?.role === "admin";

    if (body.user_id !== session.user.id && !isAdmin) {
      return NextResponse.json(
        {
          success: false,
          error: "Unauthorized to send notifications to this user",
        },
        { status: 403 },
      );
    }

    // Send the notification
    const results = await sendNotification({
      userId: body.user_id,
      category: body.category,
      type: body.type,
      title: body.title,
      message: body.message,
      link: body.link,
      metadata: body.metadata,
    });

    if (!results.success) {
      return NextResponse.json(
        { success: false, error: "Failed to send notification" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      results,
    });
  } catch (error: any) {
    console.error("Unexpected error in send notification API:", error);
    return NextResponse.json(
      { success: false, error: "An unexpected error occurred" },
      { status: 500 },
    );
  }
}
