export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createChildAccount } from "@/lib/api/accountLinking";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: POST /api/parent/add-child
 * Creates a new child account linked to the parent
 *
 * Request Body:
 * {
 *   name: string,      // Child's name
 *   dob: string,       // Child's date of birth in ISO format
 *   username?: string, // Optional username
 *   bio?: string,      // Optional bio
 *   avatar?: string    // Optional avatar URL
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   childId?: string,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current user from Supabase auth using cookies
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies });

    // Get the session first to ensure we have a valid session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        {
          success: false,
          error: "Authentication failed - please log in again",
        },
        { status: 401 },
      );
    }

    // Get the user from the session
    const user = session.user;

    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        {
          success: false,
          error: "Authentication required - please log in again",
        },
        { status: 401 },
      );
    }

    console.log("Authenticated user:", user.id);

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.dob) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 },
      );
    }

    // Generate username and password
    const username =
      body.username ||
      `${body.name.toLowerCase().replace(/\s+/g, "")}${Math.floor(Math.random() * 1000)}`;
    const password = Array(12)
      .fill(0)
      .map(() => Math.random().toString(36).charAt(2))
      .join("");
    const email = `${username}<EMAIL>`;

    console.log(
      `Creating child account with username: ${username}, parent: ${user.id}`,
    );

    // Call the RPC function directly
    const { data: rpcData, error: rpcError } = await supabase.rpc(
      "create_child_account",
      {
        p_parent_id: user.id,
        p_email: email,
        p_password: password,
        p_username: username,
        p_first_name: body.name.split(" ")[0],
        p_last_name: body.name.split(" ").slice(1).join(" ") || "",
        p_bio: body.bio || "",
        p_avatar: body.avatar || null,
      },
    );

    if (rpcError) {
      console.error("RPC error:", rpcError);
      return NextResponse.json(
        {
          success: false,
          error: `Failed to create child account: ${rpcError.message}`,
        },
        { status: 500 },
      );
    }

    if (!rpcData || !rpcData.child_id) {
      console.error("Invalid RPC response:", rpcData);
      return NextResponse.json(
        { success: false, error: "Invalid response from server" },
        { status: 500 },
      );
    }

    // Return success response
    return NextResponse.json({
      success: true,
      childId: rpcData.child_id,
    });
  } catch (error) {
    console.error("Error in add-child API route:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Server error",
        details: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 },
    );
  }
}
