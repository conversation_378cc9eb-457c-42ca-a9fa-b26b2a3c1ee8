export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import {
  verifyActivationToken,
  finalizeDeviceActivation,
} from "@/lib/api/accountLinking";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: POST /api/parent/verify-token
 * Verifies a QR token and links the child's device
 *
 * Request Body:
 * {
 *   token: string,      // QR token from child's device
 *   deviceInfo?: object // Optional device information
 * }
 *
 * Response:
 * {
 *   success: boolean,
 *   childId?: string,
 *   error?: string
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, error: "Invalid authorization header" },
        { status: 401 },
      );
    }

    // Extract the token
    const token = authHeader.split(" ")[1];

    // Get the current user from Supabase auth
    const cookieStore =  cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Verify the token
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token);

    if (authError) {
      return NextResponse.json(
        { success: false, error: "Invalid token" },
        { status: 401 },
      );
    }

    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.token) {
      return NextResponse.json(
        { success: false, error: "Missing token" },
        { status: 400 },
      );
    }

    // Verify the token and get the child ID
    const verificationResult = await verifyActivationToken(body.token, user.id);

    if (!verificationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: verificationResult.error || "Token verification failed",
        },
        { status: 400 },
      );
    }

    // Finalize device activation
    const finalizeResult = await finalizeDeviceActivation(
      verificationResult.childId!,
      body.deviceInfo,
    );

    if (!finalizeResult) {
      return NextResponse.json(
        { success: false, error: "Failed to finalize device activation" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      childId: verificationResult.childId,
    });
  } catch (error) {
    console.error("Error in verify-token API route:", error);
    return NextResponse.json(
      { success: false, error: "Server error" },
      { status: 500 },
    );
  }
}
