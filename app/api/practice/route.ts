import { NextRequest, NextResponse } from "next/server";
import {
  createPracticeSession,
  fetchPracticeSessions,
  updatePracticeSession,
} from "@/lib/queries/practiceQueries";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(7);
  console.log(
    `[PRACTICE-API-${requestId}] === Starting practice session creation ===`,
  );

  try {
    // Log request details
    console.log(`[PRACTICE-API-${requestId}] Request URL:`, request.url);
    console.log(`[PRACTICE-API-${requestId}] Request method:`, request.method);
    console.log(
      `[PRACTICE-API-${requestId}] Request headers:`,
      Object.fromEntries(request.headers.entries()),
    );

    // Get the current user from Supabase auth using route handler client
    console.log(`[PRACTICE-API-${requestId}] Creating Supabase client...`);
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    console.log(`[PRACTICE-API-${requestId}] Getting user session...`);
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError) {
      console.error(`[PRACTICE-API-${requestId}] User error:`, userError);
      return NextResponse.json(
        { error: "Authentication error", details: userError.message },
        { status: 401 },
      );
    }

    if (!user) {
      console.error(`[PRACTICE-API-${requestId}] No authenticated user found`);
      return NextResponse.json(
        { error: "You must be logged in to save a practice session" },
        { status: 401 },
      );
    }

    console.log(
      `[PRACTICE-API-${requestId}] Authenticated user:`,
      user.id,
      user.email,
    );

    // Parse the request body
    console.log(`[PRACTICE-API-${requestId}] Parsing request body...`);
    const sessionData = await request.json();
    console.log(
      `[PRACTICE-API-${requestId}] Parsed session data:`,
      JSON.stringify(sessionData, null, 2),
    );

    // Validate required fields
    if (!sessionData.duration || sessionData.duration <= 0) {
      console.error(
        `[PRACTICE-API-${requestId}] Invalid duration:`,
        sessionData.duration,
      );
      return NextResponse.json(
        { error: "Duration is required and must be greater than 0" },
        { status: 400 },
      );
    }

    // Create the practice session
    console.log(`[PRACTICE-API-${requestId}] Creating practice session...`);
    const practiceSession = await createPracticeSession(sessionData, user.id);

    console.log(
      `[PRACTICE-API-${requestId}] Practice session created successfully:`,
      practiceSession.id,
    );
    console.log(
      `[PRACTICE-API-${requestId}] === Practice session creation completed ===`,
    );

    return NextResponse.json(practiceSession, { status: 201 });
  } catch (error: any) {
    console.error(
      `[PRACTICE-API-${requestId}] === ERROR in practice session creation ===`,
    );
    console.error(
      `[PRACTICE-API-${requestId}] Error type:`,
      error.constructor.name,
    );
    console.error(`[PRACTICE-API-${requestId}] Error message:`, error.message);
    console.error(`[PRACTICE-API-${requestId}] Error stack:`, error.stack);
    console.error(`[PRACTICE-API-${requestId}] Full error object:`, error);

    return NextResponse.json(
      {
        error: "Failed to create practice session",
        details: error.message,
        requestId: requestId,
      },
      { status: 500 },
    );
  }
}

export const dynamic = "force-dynamic"; // Ensure cookies() is available

export async function GET(request: Request) {
  console.log("[GET /api/practice] Received request");
  const cookieStore = await cookies(); // Must be called before using it in createRouteHandlerClient
  const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

  try {
    console.log("[GET /api/practice] Attempting to get user session...");
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession(); // Using getSession() to be consistent with stats route

    if (sessionError) {
      console.error(
        "[GET /api/practice] Error getting session:",
        sessionError.message,
      );
      return NextResponse.json(
        { error: "Failed to get session", details: sessionError.message },
        { status: 500 },
      );
    }

    if (!session) {
      console.log("[GET /api/practice] No active session found.");
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }
    console.log("[GET /api/practice] Session found for user:", session.user.id);

    const userId = session.user.id;

    console.log(
      "[GET /api/practice] Fetching practice sessions for user:",
      userId,
    );
    const { data: practiceSessions, error: fetchError } = await supabase
      .from("practice_sessions")
      .select(
        `
        id,
        user_id,
        instrument_id,
        duration, 
        notes,
        practiced_at,
        created_at,
        metadata, 
        child_id,
        practice_type,
        songs,
        skills,
        lifetime_minutes,
        start_time,
        end_time
      `,
      )
      .eq("user_id", userId)
      .order("practiced_at", { ascending: false });

    if (fetchError) {
      console.error(
        "[GET /api/practice] Error fetching practice sessions:",
        fetchError.message,
      );
      return NextResponse.json(
        {
          error: "Failed to fetch practice sessions",
          details: fetchError.message,
        },
        { status: 500 },
      );
    }

    // Transform the data to include song_id from metadata for compatibility
    const transformedSessions = practiceSessions?.map(session => ({
      ...session,
      song_id: session.metadata?.song_id || null, // Extract song_id from metadata
    })) || [];

    console.log(
      "[GET /api/practice] Successfully fetched",
      transformedSessions?.length || 0,
      "sessions.",
    );
    return NextResponse.json(transformedSessions);
  } catch (error: any) {
    console.error("[GET /api/practice] Unknown error:", error.message);
    console.error("[GET /api/practice] Error stack:", error.stack);
    return NextResponse.json(
      { error: "Failed to process request.", details: error.message },
      { status: 500 },
    );
  }
}

export async function PATCH(request: NextRequest) {
  const cookieStore = await cookies();
  const requestId = Math.random().toString(36).substring(7);
  console.log(
    `[PRACTICE-API-PATCH-${requestId}] === Starting practice session update ===`,
  );

  try {
    // Get the current user from Supabase auth
    console.log(`[PRACTICE-API-PATCH-${requestId}] Creating Supabase client...`);
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    console.log(`[PRACTICE-API-PATCH-${requestId}] Getting user session...`);
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError) {
      console.error(`[PRACTICE-API-PATCH-${requestId}] User error:`, userError);
      return NextResponse.json(
        { error: "Authentication error", details: userError.message },
        { status: 401 },
      );
    }

    if (!user) {
      console.error(`[PRACTICE-API-PATCH-${requestId}] No authenticated user found`);
      return NextResponse.json(
        { error: "You must be logged in to update a practice session" },
        { status: 401 },
      );
    }

    // Parse the request body and URL
    const url = new URL(request.url);
    const sessionId = url.searchParams.get('id');
    
    if (!sessionId) {
      return NextResponse.json(
        { error: "Session ID is required" },
        { status: 400 },
      );
    }

    console.log(
      `[PRACTICE-API-PATCH-${requestId}] Authenticated user:`,
      user.id,
      user.email,
    );

    // Parse the request body
    console.log(`[PRACTICE-API-PATCH-${requestId}] Parsing request body...`);
    const sessionData = await request.json();
    console.log(
      `[PRACTICE-API-PATCH-${requestId}] Parsed session data:`,
      JSON.stringify(sessionData, null, 2),
    );

    // Update the practice session
    console.log(`[PRACTICE-API-PATCH-${requestId}] Updating practice session...`);
    const updatedSession = await updatePracticeSession(sessionId, sessionData, user.id);

    if (!updatedSession) {
      return NextResponse.json(
        { error: "Practice session not found" },
        { status: 404 },
      );
    }

    console.log(
      `[PRACTICE-API-PATCH-${requestId}] Practice session updated successfully:`,
      updatedSession.id,
    );
    console.log(
      `[PRACTICE-API-PATCH-${requestId}] === Practice session update completed ===`,
    );

    return NextResponse.json(updatedSession, { status: 200 });
  } catch (error: any) {
    console.error(
      `[PRACTICE-API-PATCH-${requestId}] === ERROR in practice session update ===`,
    );
    console.error(
      `[PRACTICE-API-PATCH-${requestId}] Error type:`,
      error.constructor.name,
    );
    console.error(`[PRACTICE-API-PATCH-${requestId}] Error message:`, error.message);
    console.error(`[PRACTICE-API-PATCH-${requestId}] Error stack:`, error.stack);

    return NextResponse.json(
      {
        error: "Failed to update practice session",
        details: error.message,
        requestId: requestId,
      },
      { status: 500 },
    );
  }
}
