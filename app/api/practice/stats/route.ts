import { NextResponse } from "next/server";
import { cookies } from "next/headers";
// import { subDays, startOfDay } from 'date-fns'; // No longer needed here
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { fetchPracticeStats } from "@/lib/queries/practiceQueries"; // Import the centralized function

export const dynamic = "force-dynamic"; // Required for cookies()

// Helper function to calculate streaks is now in practiceQueries.ts

export async function GET(request: Request) {
  console.log("[GET /api/practice/stats] Received request");
  const supabase = createRouteHandlerClient({ cookies });

  try {
    console.log("[GET /api/practice/stats] Attempting to get user session...");
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();
    if (sessionError) {
      console.error(
        "[GET /api/practice/stats] Error getting session:",
        sessionError.message,
      );
      return NextResponse.json(
        { error: "Failed to get session", details: sessionError.message },
        { status: 500 },
      );
    }
    if (!session) {
      console.log("[GET /api/practice/stats] No active session found.");
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }
    console.log(
      "[GET /api/practice/stats] Session found for user:",
      session.user.id,
    );
    const userId = session.user.id;

    // Get child_id from query parameters if present
    const { searchParams } = new URL(request.url);
    const childId = searchParams.get("child_id");
    console.log(`[GET /api/practice/stats] childId from params: ${childId}`);

    console.log(
      `[GET /api/practice/stats] Fetching stats for user: ${userId}${childId ? ", child: " + childId : ""}`,
    );

    // Call the centralized fetchPracticeStats function
    // The fetchPracticeStats function will need to be adapted if child-specific stats are required
    // For now, it fetches all for the user. Filtering by child_id could be added there.
    const stats = await fetchPracticeStats(supabase, userId /*, childId */);
    // If you modify fetchPracticeStats to accept childId, uncomment and pass it.

    console.log("[GET /api/practice/stats] Returning calculated stats.");
    return NextResponse.json(stats);
  } catch (error: any) {
    console.error("[GET /api/practice/stats] Unknown error:", error.message);
    console.error("[GET /api/practice/stats] Error stack:", error.stack);
    return NextResponse.json(
      { error: "Failed to process stats request.", details: error.message },
      { status: 500 },
    );
  }
}
