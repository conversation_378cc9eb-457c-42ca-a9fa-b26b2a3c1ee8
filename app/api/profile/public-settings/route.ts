export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoints for managing public profile settings
 * Requires authentication
 */

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies });

    // Get the current user
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Get profile_id from query params or use current user's personal profile
    const url = new URL(request.url);
    const profileId = url.searchParams.get("profile_id");

    let targetProfileId = profileId;

    if (!profileId) {
      // Get user's personal profile
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("id")
        .eq("user_id", session.user.id)
        .eq("profile_type", "personal")
        .single();

      if (profileError || !profile) {
        return NextResponse.json(
          { error: "Profile not found" },
          { status: 404 },
        );
      }

      targetProfileId = profile.id;
    } else {
      // Verify user has access to this profile
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("user_id")
        .eq("id", profileId)
        .single();

      if (profileError || !profile || profile.user_id !== session.user.id) {
        return NextResponse.json(
          { error: "Profile not found or access denied" },
          { status: 403 },
        );
      }
    }

    // Get public profile settings
    const { data: settings, error: settingsError } = await supabase
      .from("public_profile_settings")
      .select("*")
      .eq("profile_id", targetProfileId)
      .single();

    if (settingsError && settingsError.code !== "PGRST116") {
      // PGRST116 is "not found" error, which is okay for new profiles
      throw settingsError;
    }

    // If no settings exist, return default settings
    if (!settings) {
      const defaultSettings = {
        profile_id: targetProfileId,
        is_public: false,
        show_display_name: true,
        show_username: true,
        show_avatar: true,
        show_bio: true,
        show_location: false,
        show_availability_status: false,
        show_public_email: false,
        show_social_links: true,
        show_booking_link: false,
        embed_social_feeds: false,
        show_roles: true,
        show_genres: true,
        show_instruments: true,
        show_groups: false,
        show_repertoire: false,
        show_teaching_services: false,
        show_featured_media: true,
        show_image_gallery: false,
        show_availability_calendar: false,
        show_achievements: false,
        show_testimonials: false,
        show_pricing: false,
        show_downloads: false,
      };

      return NextResponse.json({
        success: true,
        data: defaultSettings,
      });
    }

    return NextResponse.json({
      success: true,
      data: settings,
    });
  } catch (error: any) {
    console.error("Error fetching public profile settings:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies });

    // Get the current user
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const body = await request.json();
    const { profile_id, ...settingsData } = body;

    // Verify user has access to this profile
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("user_id")
      .eq("id", profile_id)
      .single();

    if (profileError || !profile || profile.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Profile not found or access denied" },
        { status: 403 },
      );
    }

    // Upsert public profile settings
    const { data: settings, error: settingsError } = await supabase
      .from("public_profile_settings")
      .upsert({
        profile_id,
        ...settingsData,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (settingsError) {
      throw settingsError;
    }

    return NextResponse.json({
      success: true,
      data: settings,
    });
  } catch (error: any) {
    console.error("Error updating public profile settings:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function PUT(request: NextRequest) {
  // PUT is the same as POST for this endpoint (upsert behavior)
  return POST(request);
}
