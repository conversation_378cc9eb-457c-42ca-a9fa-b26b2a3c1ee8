export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoints for managing social links
 * Requires authentication
 */

export async function GET(request: NextRequest) {
  const cookieStore = await cookies();
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the current user
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Get profile_id from query params
    const url = new URL(request.url);
    const profileId = url.searchParams.get("profile_id");

    if (!profileId) {
      return NextResponse.json(
        { error: "Profile ID is required" },
        { status: 400 },
      );
    }

    // Verify user has access to this profile
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("user_id")
      .eq("id", profileId)
      .single();

    if (profileError || !profile || profile.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Profile not found or access denied" },
        { status: 403 },
      );
    }

    // Get social links
    const { data: socialLinks, error: linksError } = await supabase
      .from("social_links")
      .select("*")
      .eq("profile_id", profileId)
      .order("sort_order");

    if (linksError) {
      throw linksError;
    }

    return NextResponse.json({
      success: true,
      data: socialLinks || [],
    });
  } catch (error: any) {
    console.error("Error fetching social links:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  const cookieStore = await cookies();
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the current user
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const body = await request.json();
    const {
      profile_id,
      platform,
      url,
      display_name,
      is_visible = true,
      sort_order = 0,
    } = body;

    if (!profile_id || !platform || !url) {
      return NextResponse.json(
        { error: "Profile ID, platform, and URL are required" },
        { status: 400 },
      );
    }

    // Verify user has access to this profile
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("user_id")
      .eq("id", profile_id)
      .single();

    if (profileError || !profile || profile.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Profile not found or access denied" },
        { status: 403 },
      );
    }

    // Validate platform
    const validPlatforms = [
      "instagram",
      "youtube",
      "tiktok",
      "twitter",
      "facebook",
      "bandcamp",
      "soundcloud",
      "spotify",
      "apple_music",
      "linkedin",
      "website",
      "other",
    ];

    if (!validPlatforms.includes(platform)) {
      return NextResponse.json({ error: "Invalid platform" }, { status: 400 });
    }

    // Upsert social link (update if exists, insert if not)
    const { data: socialLink, error: linkError } = await supabase
      .from("social_links")
      .upsert({
        profile_id,
        platform,
        url,
        display_name,
        is_visible,
        sort_order,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (linkError) {
      throw linkError;
    }

    return NextResponse.json({
      success: true,
      data: socialLink,
    });
  } catch (error: any) {
    console.error("Error creating/updating social link:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function DELETE(request: NextRequest) {
  const cookieStore = await cookies();
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the current user
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const url = new URL(request.url);
    const linkId = url.searchParams.get("id");
    const profileId = url.searchParams.get("profile_id");

    if (!linkId || !profileId) {
      return NextResponse.json(
        { error: "Link ID and Profile ID are required" },
        { status: 400 },
      );
    }

    // Verify user has access to this profile
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("user_id")
      .eq("id", profileId)
      .single();

    if (profileError || !profile || profile.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Profile not found or access denied" },
        { status: 403 },
      );
    }

    // Delete social link
    const { error: deleteError } = await supabase
      .from("social_links")
      .delete()
      .eq("id", linkId)
      .eq("profile_id", profileId);

    if (deleteError) {
      throw deleteError;
    }

    return NextResponse.json({
      success: true,
      message: "Social link deleted successfully",
    });
  } catch (error: any) {
    console.error("Error deleting social link:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
