import { NextRequest, NextResponse } from "next/server";
import { fetchProfileById } from "@/lib/queries/profileQueries";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

/**
 * API Endpoint: GET /api/profiles/:id
 * Fetches a profile by ID
 *
 * Response:
 * Profile object or error
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id } = await params;
  try {
    // Get the current user from Supabase auth
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Fetch the profile
    const profile = await fetchProfileById(id);

    // Check if the user has permission to view this profile
    // Users can view their own profile or profiles of their children
    if (user.id !== profile.id && profile.parent_id !== user.id) {
      // Check if the user is a parent of this profile
      const { data } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", profile.id)
        .eq("parent_id", user.id)
        .single();

      if (!data) {
        return NextResponse.json(
          { error: "You don't have permission to view this profile" },
          { status: 403 },
        );
      }
    }

    return NextResponse.json(profile);
  } catch (error) {
    console.error("Error fetching profile:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch profile",
        message: error instanceof Error ? error.message : String(error),
        details: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 },
    );
  }
}

/**
 * API Endpoint: PATCH /api/profiles/:id
 * Updates a profile by ID
 *
 * Request Body:
 * Partial profile object with fields to update
 *
 * Response:
 * Updated profile object or error
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id } = await params;
    const profileIdToUpdate = id;
  console.log(
    `[PROFILE-UPDATE-API] Received PATCH request for profile ID: ${profileIdToUpdate}`,
  );

  try {
    // Explicitly await cookies() before use
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    console.log(`[PROFILE-UPDATE-API] Attempting to get current user...`);
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError) {
      console.error(`[PROFILE-UPDATE-API] Error getting user:`, userError);
      return NextResponse.json(
        { error: "Authentication error", details: userError.message },
        { status: 401 },
      );
    }
    if (!user) {
      console.warn(`[PROFILE-UPDATE-API] No authenticated user found.`);
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }
    console.log(`[PROFILE-UPDATE-API] Current user ID: ${user.id}`);

    // Fetch the profile that is being updated
    console.log(
      `[PROFILE-UPDATE-API] Fetching profile to update (ID: ${profileIdToUpdate})...`,
    );
    const profileToUpdate = await fetchProfileById(profileIdToUpdate); // This uses the corrected fetchProfileById

    if (!profileToUpdate) {
      // fetchProfileById now throws if not found, so this might be redundant, but good for safety.
      console.warn(
        `[PROFILE-UPDATE-API] Profile with ID ${profileIdToUpdate} not found.`,
      );
      return NextResponse.json({ error: "Profile not found" }, { status: 404 });
    }
    console.log(
      `[PROFILE-UPDATE-API] Found profile to update: ${profileToUpdate.username}`,
    );

    // Permission check: User must be the profile owner or the parent of the child profile
    const isOwner = user.id === profileToUpdate.id;
    const isParent = user.id === profileToUpdate.parent_id;

    if (!isOwner && !isParent) {
      console.warn(
        `[PROFILE-UPDATE-API] User ${user.id} does not have permission to update profile ${profileIdToUpdate}. Owner: ${profileToUpdate.user_id}, Parent: ${profileToUpdate.parent_id}`,
      );
      return NextResponse.json(
        { error: "You don't have permission to update this profile" },
        { status: 403 },
      );
    }
    console.log(
      `[PROFILE-UPDATE-API] User ${user.id} has permission. Owner: ${isOwner}, Parent: ${isParent}`,
    );

    // Parse the request body for update data
    const updateData = await request.json();
    console.log(
      `[PROFILE-UPDATE-API] Updating profile ${profileIdToUpdate} with data:`,
      JSON.stringify(updateData, null, 2),
    );

    // Update the profile in the database
    const { data: updatedProfile, error: updateError } = await supabase
      .from("profiles")
      .update(updateData) // Pass the direct updateData from request
      .eq("id", profileIdToUpdate)
      .select()
      .single();

    if (updateError) {
      console.error(
        `[PROFILE-UPDATE-API] Error during Supabase update for profile ${profileIdToUpdate}:`,
        updateError,
      );
      return NextResponse.json(
        {
          error: "Failed to update profile database",
          details: updateError.message,
        },
        { status: 500 },
      );
    }

    console.log(
      `[PROFILE-UPDATE-API] Profile ${profileIdToUpdate} updated successfully:`,
      updatedProfile,
    );
    return NextResponse.json(updatedProfile);
  } catch (error: any) {
    console.error(
      `[PROFILE-UPDATE-API] Overall error in PATCH /api/profiles/${profileIdToUpdate}:`,
      error.message,
    );
    console.error("[PROFILE-UPDATE-API] Error stack:", error.stack);
    return NextResponse.json(
      {
        error: "Failed to update profile",
        message: error.message,
        details: error.stack,
      },
      { status: 500 },
    );
  }
}
