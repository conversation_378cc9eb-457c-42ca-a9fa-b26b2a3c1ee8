export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { GoogleCloudVisionService } from "@/lib/services/vision/vision.service";

export async function POST(request: NextRequest) {
  try {
    // Get the current user from Supabase
    const cookieStore = await cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options),
            );
          },
        },
      },
    );

    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check Google Cloud Vision credentials
    const googleApiKey =
      process.env.GOOGLE_CLOUD_API_KEY ||
      process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

    if (!googleApiKey) {
      return NextResponse.json(
        { error: "Google Cloud Vision API credentials not configured" },
        { status: 500 },
      );
    }

    // Get the form data from the request
    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Convert the file to a buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    const base64Image = buffer.toString("base64");

    // Initialize Google Cloud Vision service
    const visionService = new GoogleCloudVisionService();

    // Process the receipt with Google Cloud Vision
    console.log("Processing receipt with Google Cloud Vision...");
    const visionResponse = await visionService.processReceipt(
      base64Image,
      file.name,
    );

    // Map Google Cloud Vision response to our format
    const parsedReceipt = {
      lineItems: visionResponse.items.map((item) => ({
        description: item.description,
        qty: item.quantity,
        sku: undefined, // Not extracted by Vision API
        cost: item.totalPrice,
        discount: undefined, // Could be enhanced
        musicRelated: item.musicRelated,
        gearType: item.gearType,
      })),
      subtotal: visionResponse.subtotal || undefined,
      tax: visionResponse.tax || undefined,
      totalAmount: visionResponse.total,
      amountPaid: undefined, // Could be enhanced
      amountOutstanding: undefined, // Could be enhanced
      vendor: visionResponse.merchant,
      vendorAddress: visionResponse.address || undefined,
      vendorPhone: visionResponse.phone || undefined,
      vendorUrl: visionResponse.website || undefined,
      abn: undefined, // Could be enhanced for Australian receipts
      server: visionResponse.cashier || undefined,
      dateTime: visionResponse.date || new Date().toISOString(),
      invoiceNumber: visionResponse.receiptNumber || undefined,
      // Enhanced fields from Google Cloud Vision
      category: visionResponse.category,
      confidence: visionResponse.confidence,
      musicRelated: visionResponse.musicRelated,
      gearItems: visionResponse.gearItems,
      eventDetails: visionResponse.eventDetails,
      paymentMethod: visionResponse.paymentMethod,
      currency: visionResponse.currency,
      rawText: visionResponse.rawText,
    };

    return NextResponse.json({ parsedReceipt });
  } catch (error) {
    console.error("Error processing receipt:", error);
    return NextResponse.json(
      { error: "Failed to process receipt" },
      { status: 500 },
    );
  }
}

export async function GET() {
  return NextResponse.json({ message: "Receipt processing API is running" });
}
