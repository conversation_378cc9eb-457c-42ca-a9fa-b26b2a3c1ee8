export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { createReceiptWithItems } from "@/lib/queries/receiptQueries";

export async function POST(request: NextRequest) {
  try {
    // Get the current user from Supabase
    const cookieStore = await cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(name: string, value: string, options: any) {
            cookieStore.set({ name, value, ...options });
          },
          remove(name: string, options: any) {
            cookieStore.set({ name, value: "", ...options });
          },
        },
      },
    );

    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the receipt data from the request
    const { receipt, items } = await request.json();

    // Validate receipt data
    if (
      !receipt ||
      !receipt.merchant ||
      !receipt.date ||
      receipt.total === undefined
    ) {
      return NextResponse.json(
        { error: "Invalid receipt data" },
        { status: 400 },
      );
    }

    // Make sure the profile_id matches the current user
    if (receipt.profile_id !== user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Create the receipt with items
    const savedReceipt = await createReceiptWithItems(receipt, items);

    return NextResponse.json({ receipt: savedReceipt });
  } catch (error) {
    console.error("Error saving receipt:", error);
    return NextResponse.json(
      { error: "Failed to save receipt" },
      { status: 500 },
    );
  }
}

export async function GET() {
  return NextResponse.json({ message: "Receipt API is running" });
}
