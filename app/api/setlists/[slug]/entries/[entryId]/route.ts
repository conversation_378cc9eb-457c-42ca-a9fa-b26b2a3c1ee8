export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
  const cookieStore = await cookies();
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// PATCH /api/setlists/[slug]/entries/[entryId] - update entry order or note
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string; entryId: string }> },
) {
  const { slug, entryId } = await params;
  const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user)
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 },
    );

  const { order, note, instrument } = await request.json();
  const updates: any = {};
  if (order !== undefined) updates.order = order;
  if (note !== undefined) updates.note = note;
  if (instrument !== undefined) updates.instrument = instrument;

  const { data, error } = await supabase
    .from("setlist_entries")
    .update(updates)
    .eq("id", entryId)
    .single();

  if (error)
    return NextResponse.json({ error: error.message }, { status: 400 });
  return NextResponse.json(data);
}

// DELETE /api/setlists/[slug]/entries/[entryId] - delete an entry
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string; entryId: string }> },
) {
  const { slug, entryId } = await params;
  const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user)
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 },
    );

  const { error } = await supabase
    .from("setlist_entries")
    .delete()
    .eq("id", entryId);

  if (error)
    return NextResponse.json({ error: error.message }, { status: 400 });
  return NextResponse.json({ success: true });
}
