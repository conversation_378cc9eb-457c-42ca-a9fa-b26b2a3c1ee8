export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/setlists/[slug]/entries - list entries for a setlist
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> },
) {
  const cookieStore = await cookies();
  const { slug } = await params;
  const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user)
    return NextResponse.json({ error: "Auth required" }, { status: 401 });

  // Fetch setlist ID
  const { data: setlist, error: setlistError } = await supabase
    .from("setlists")
    .select("id")
    .eq("slug", slug)
    .single();
  if (setlistError || !setlist)
    return NextResponse.json({ error: "Setlist not found" }, { status: 404 });

  // Fetch entries ordered by 'order'
  const { data, error } = await supabase
    .from("setlist_entries")
    .select("*")
    .eq("setlist_id", setlist.id)
    .order("order", { ascending: true });

  if (error)
    return NextResponse.json({ error: error.message }, { status: 500 });
  return NextResponse.json(data);
}

// POST /api/setlists/[slug]/entries - add a new entry
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> },
) {
  const cookieStore = await cookies();
  const { slug } = await params;
  const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user)
    return NextResponse.json({ error: "Auth required" }, { status: 401 });

  const { type, songId, note, instrument, order } = await request.json();
  if (!type || order == null) {
    return NextResponse.json(
      { error: "Type and order are required" },
      { status: 400 },
    );
  }

  // Get setlist ID
  const { data: setlist } = await supabase
    .from("setlists")
    .select("id")
    .eq("slug", slug)
    .single();

  const { data, error } = await supabase
    .from("setlist_entries")
    .insert({
      setlist_id: setlist!.id,
      type,
      song_id: songId,
      note,
      instrument,
      order,
    })
    .select()
    .single();

  if (error)
    return NextResponse.json({ error: error.message }, { status: 400 });
  return NextResponse.json(data, { status: 201 });
}
