// Add Node.js runtime override
export const runtime = "nodejs";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { createApiLogger } from "@/lib/logs/api-logger";
import {
  fetchSetlistBySlug,
  updateSetlist,
} from "@/lib/queries/setlistQueries"; // Ensure updateSetlist is imported
import type {
  Setlist,
  SetlistItem,
  SetlistUpdateData,
} from "@/lib/types/setlist"; // SetlistUpdateData for PATCH

// Create route-specific logger
const logger = createApiLogger("setlists/[slug]");

interface AuthenticatedUser {
  userId: string;
  // Add other user properties if needed
}

// Helper function to get authenticated user (replace with your actual auth logic if different)
async function getAuthenticatedUser(
  supabase: any,
): Promise<AuthenticatedUser | null> {
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser(); // Renamed error to authError
  if (authError || !user) {
    logger.error("Authentication error or no user", { error: authError });
    return null;
  }
  return { userId: user.id };
}

// Ensure params is destructured from the second argument object
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> },
) {
  const cookieStore = await cookies();
  const startTime = Date.now();
  const { slug } = await params;
  try {
    logger.info(`Processing GET request for setlist slug: ${slug}`);

    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const authenticatedUser = await getAuthenticatedUser(supabase);
    if (!authenticatedUser) {
      logger.warn("GET: Unauthenticated access attempt", { slug });
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    logger.debug(`User authenticated:`, { userId: authenticatedUser.userId });

    // Corrected usage of fetchSetlistBySlug
    const setlist = await fetchSetlistBySlug(supabase, slug);
    // fetchSetlistBySlug returns Setlist | null and throws on DB errors, so no 'error' object to destructure here.
    // The function itself handles logging DB errors.

    if (!setlist) {
      // This case is now handled because fetchSetlistBySlug returns null if not found.
      logger.warn("Setlist not found by slug in GET", { slug });
      return NextResponse.json({ error: "Setlist not found" }, { status: 404 });
    }

    if (setlist.creator_profile_id !== authenticatedUser.userId) {
      logger.warn("User attempting to access unauthorized setlist", {
        slug,
        userId: authenticatedUser.userId,
        ownerId: setlist.creator_profile_id,
      });
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    logger.info(`Successfully fetched setlist`, {
      slug,
      setlistId: setlist.id,
      itemCount: setlist.list_items?.length || 0,
      duration: `${Date.now() - startTime}ms`,
    });
    return NextResponse.json(setlist);
  } catch (e: any) {
    // Catch errors thrown by fetchSetlistBySlug (like DB connection issues) or other unexpected errors.
    logger.error("Unexpected error in GET /api/setlists/[slug]", {
      slug,
      error: e.message,
      stack: e.stack,
    });
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}

// PATCH /api/setlists/[slug] - update setlist details or list_items
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }, // Ensure params is destructured correctly here too
) {
  const startTime = Date.now();
  const cookieStore = await cookies();
  const { slug } = await params;
  try {
    logger.info(`Processing PATCH request for setlist slug: ${slug}`);

    const body = await request.json();
    // Removed target_duration_minutes and tags for now, as they are not in Setlist type
    const {
      title,
      description,
      list_items,
      is_public /*, target_duration_minutes, tags */,
    } = body;

    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const authenticatedUser = await getAuthenticatedUser(supabase);
    if (!authenticatedUser) {
      logger.warn("PATCH: Unauthenticated access attempt", { slug });
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Corrected usage of fetchSetlistBySlug
    const existingSetlist = await fetchSetlistBySlug(supabase, slug);
    // No 'fetchError' object to destructure here from fetchSetlistBySlug

    if (!existingSetlist) {
      logger.warn("Setlist not found for PATCH", { slug });
      return NextResponse.json({ error: "Setlist not found" }, { status: 404 });
    }

    if (existingSetlist.creator_profile_id !== authenticatedUser.userId) {
      logger.warn("User attempting to PATCH unauthorized setlist", {
        slug,
        userId: authenticatedUser.userId,
        ownerId: existingSetlist.creator_profile_id,
      });
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Use SetlistUpdateData for the update payload
    const updateData: SetlistUpdateData = {};
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (list_items !== undefined) {
      // Ensure list_items conforms to SetlistItem[] if SetlistUpdateData expects that for list_items
      // The SetlistUpdateData type makes list_items: SetlistItem[] optional.
      // The database expects JSONB here. The query function should handle JSON.stringify if needed.
      updateData.list_items = list_items as SetlistItem[];
      logger.debug("Updating list_items array", {
        itemCount: list_items.length,
      });
    }
    if (is_public !== undefined) updateData.is_public = is_public;
    // if (target_duration_minutes !== undefined) updateData.target_duration_minutes = target_duration_minutes; // Commented out
    // if (tags !== undefined) updateData.tags = tags; // Commented out

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { message: "No update data provided" },
        { status: 400 },
      );
    }

    logger.debug("Starting: updateSetlistInApi", {
      updateData: Object.keys(updateData),
    });

    // Corrected function call to updateSetlist
    const updatedSetlist = await updateSetlist(
      supabase,
      existingSetlist.id,
      updateData,
    );
    // updateSetlist returns Setlist | null and throws on DB errors.

    if (!updatedSetlist) {
      // This could happen if updateSetlist returns null (e.g., RLS failed, or row not found after update)
      logger.error(
        "Failed to update setlist or setlist not found after update",
        { slug, existingSetlistId: existingSetlist.id },
      );
      return NextResponse.json(
        { error: "Failed to update setlist or setlist not returned" },
        { status: 500 },
      );
    }

    logger.info(`Successfully updated setlist`, {
      slug,
      setlistId: updatedSetlist.id,
      itemCount: updatedSetlist.list_items?.length,
      duration: `${Date.now() - startTime}ms`,
    });
    return NextResponse.json(updatedSetlist);
  } catch (e: any) {
    logger.error("Unexpected error in PATCH /api/setlists/[slug]", {
      slug,
      error: e.message,
      stack: e.stack,
    });
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
