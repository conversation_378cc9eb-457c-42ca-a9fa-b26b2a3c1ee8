import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { createApiLogger } from "@/lib/logs/api-logger";
import { createSetlist } from "@/lib/queries/setlistQueries";
import type { SetlistCreationData } from "@/lib/types/setlist";

// Add Node.js runtime override
export const runtime = "nodejs";

// Create route-specific logger
const logger = createApiLogger("setlists");

// GET /api/setlists - list authenticated user's setlists
export async function GET(request: NextRequest) {
  const cookieStore = await cookies();
  const startTime = Date.now();
  try {
    logger.info("Processing GET request for setlists list");

    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      logger.warn("Unauthorized API access attempt for GET /api/setlists");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    logger.debug("User authenticated for GET /api/setlists", {
      userId: user.id,
    });

    return await logger.traceCall("fetchUserSetlistsDirectly", async () => {
      const { data, error } = await supabase
        .from("setlists")
        .select("*")
        .eq("creator_profile_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        logger.error("Failed to fetch user setlists", {
          userId: user.id,
          error,
        });
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      const duration = Date.now() - startTime;
      logger.info("Successfully fetched user setlists", {
        userId: user.id,
        count: data?.length || 0,
        duration: `${duration}ms`,
      });

      return NextResponse.json(data);
    });
  } catch (err) {
    const duration = Date.now() - startTime;
    logger.error("Error in GET /api/setlists", {
      error: err instanceof Error ? err.message : String(err),
      stack: err instanceof Error ? err.stack : undefined,
      duration: `${duration}ms`,
    });

    return NextResponse.json(
      { error: err instanceof Error ? err.message : String(err) },
      { status: 500 },
    );
  }
}

// POST /api/setlists - create a new setlist
export async function POST(request: NextRequest) {
  const cookieStore = await cookies();
  const startTime = Date.now();
  try {
    logger.info("Processing POST request to create setlist");

    const body = await request.json();
    logger.debug("POST payload received", body);

    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user || !user.id) {
      logger.warn("Unauthorized API access attempt for POST /api/setlists");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    logger.debug("User authenticated for POST /api/setlists", {
      userId: user.id,
    });

    const {
      title,
      slug,
      description,
      purpose,
      genres,
      list_items,
      is_public,
      shared_with,
      is_rehearsal,
      metadata,
    } = body as Partial<SetlistCreationData>;

    if (!title) {
      logger.warn("Missing required field: title", { title: !!title });
      return NextResponse.json(
        { error: "Missing required field: title" },
        { status: 400 },
      );
    }

    const setlistPayload: SetlistCreationData = {
      creator_profile_id: user.id,
      title,
      slug:
        slug ||
        title
          .trim()
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-")
          .replace(/^-+|-+$/g, ""),
      description: description || undefined,
      purpose: purpose || "practice",
      genres: genres || [],
      list_items: list_items || [],
      is_public: is_public === undefined ? false : is_public,
      shared_with: shared_with || [],
      is_rehearsal: is_rehearsal === undefined ? false : is_rehearsal,
      metadata: metadata || undefined,
    };

    return await logger.traceCall("createSetlistQuery", async () => {
      const newSetlist = await createSetlist(supabase, setlistPayload);

      const duration = Date.now() - startTime;
      logger.info("Successfully created setlist via query function", {
        setlistId: newSetlist.id,
        slug: newSetlist.slug,
        duration: `${duration}ms`,
      });

      return NextResponse.json(newSetlist, { status: 201 });
    });
  } catch (err: any) {
    const duration = Date.now() - startTime;
    let errorMessage = "An unexpected error occurred";
    let errorDetails: any = {};

    if (err instanceof Error) {
      errorMessage = err.message;
      errorDetails.stack = err.stack;
    }
    if (err.code) {
      errorDetails.code = err.code;
      errorDetails.details = err.details;
      errorDetails.hint = err.hint;
    }

    logger.error("Error in POST /api/setlists", {
      error: errorMessage,
      details: errorDetails,
      originalError: String(err),
      duration: `${duration}ms`,
    });

    return NextResponse.json(
      {
        error: errorMessage,
        ...(errorDetails.code && { details: errorDetails }),
      },
      {
        status:
          typeof err.status === "number" &&
          err.status >= 400 &&
          err.status < 600
            ? err.status
            : 500,
      },
    );
  }
}
