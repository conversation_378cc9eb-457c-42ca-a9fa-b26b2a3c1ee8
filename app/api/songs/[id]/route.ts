import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { createApiLogger } from "@/lib/logs/api-logger";
import songService from "@/lib/services/song-service";

export const runtime = "nodejs";

// Create route-specific logger
const logger = createApiLogger("song-details");

// GET /api/songs/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  const startTime = Date.now();
  let songId: string | undefined;
  try {
    const { id } = await params;
    songId = id;
    const url = new URL(request.url);
    const enrichWithSpotify = url.searchParams.get("enrich") !== "false"; // Default to true

    logger.info(`Processing GET request for song details`, {
      songId,
      enrichWithSpotify,
    });

    // Handle cookies properly
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Require user to be authenticated
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      logger.warn("Unauthorized API access attempt");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    logger.debug("User authenticated", { userId: user.id });

    // Check if this is a local database ID first
    if (!songId.includes(":")) {
      const { data: dbSong, error: dbError } = await supabase
        .from("songs")
        .select("*")
        .eq("id", songId)
        .single();

      if (dbError) {
        if (dbError.code === "PGRST116") {
          logger.info("Song not found in database", { songId });
        } else {
          logger.error("Database error", { error: dbError });
          return NextResponse.json({ error: dbError.message }, { status: 500 });
        }
      }

      if (dbSong) {
        logger.info("Found song in database", { songId });

        // If enrichment is requested, add Spotify data
        if (enrichWithSpotify) {
          try {
            // Use the song service to find and merge Spotify data
            const spotifyData = await songService.getSongDetails(
              `genius:${dbSong.genius_id || songId}`,
              true,
            );

            if (spotifyData) {
              // Merge relevant Spotify audio features into our DB song
              const enrichedSong = {
                ...dbSong,
                duration_ms: dbSong.duration_ms || spotifyData.duration_ms,
                tempo: spotifyData.tempo,
                key: spotifyData.key,
                mode: spotifyData.mode,
                time_signature: spotifyData.time_signature,
                acousticness: spotifyData.acousticness,
                danceability: spotifyData.danceability,
                energy: spotifyData.energy,
                instrumentalness: spotifyData.instrumentalness,
                liveness: spotifyData.liveness,
                loudness: spotifyData.loudness,
                speechiness: spotifyData.speechiness,
                valence: spotifyData.valence,
              };

              return NextResponse.json(enrichedSong);
            }
          } catch (error) {
            logger.warn("Error enriching with Spotify", { error });
            // Continue with DB song if enrichment fails
          }
        }

        return NextResponse.json(dbSong);
      }
    }

    // If not found in database or has a provider prefix, use the song service
    const metadata = await songService.getSongDetails(
      songId,
      enrichWithSpotify,
    );

    if (!metadata) {
      logger.warn("Song not found", { songId });
      return NextResponse.json({ error: "Song not found" }, { status: 404 });
    }

    const duration = Date.now() - startTime;
    logger.info("Song details fetched", {
      songId,
      provider: metadata.provider,
      duration: `${duration}ms`,
    });

    return NextResponse.json(metadata);
  } catch (err) {
    const duration = Date.now() - startTime;
    logger.error("Error fetching song details", {
      songId,
      error: err instanceof Error ? err.message : String(err),
      stack: err instanceof Error ? err.stack : undefined,
      duration: `${duration}ms`,
    });

    return NextResponse.json(
      { error: err instanceof Error ? err.message : String(err) },
      { status: 500 },
    );
  }
}
