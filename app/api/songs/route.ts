import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { createApiLogger } from "@/lib/logs/api-logger";
import songService from "@/lib/services/song-service";
import { enrichSongWithSpotifyData } from "@/lib/services/spotify";
import {
  withAuth,
  AuthenticatedRequest,
} from "@/lib/middleware/api-auth-middleware";

export const runtime = "nodejs";

// Create route-specific logger
const logger = createApiLogger("songs");

// Define a type for the song results to address linter errors and for clarity
interface ApiSongResult {
  id: string; // Database song ID
  title: string;
  primary_artist_name?: string | null;
  album_name?: string | null;
  album_cover_url?: string | null;
  duration_ms?: number | null;
  provider: "database" | "spotify" | "genius" | string; // Identifies the source
  provider_id: string; // ID from the source (could be same as id if DB)
  // Optional fields that might come from providers or be structured differently
  artist?: string | null;
  album?: string | null;
  imageUrl?: string | null;
  // Add any other fields you expect from songService.searchSongs
}

// GET /api/songs?search=term&providers=genius,spotify&fetch_mode=library
async function getSongs(request: AuthenticatedRequest) {
  const startTime = Date.now();
  try {
    const url = new URL(request.url);
    const search = url.searchParams.get("search") || "";
    const providersParam = url.searchParams.get("providers");
    const providers = providersParam ? providersParam.split(",") : undefined;
    const fetchMode = url.searchParams.get("fetch_mode");
    const userId = request.auth.userId;

    logger.info(`Processing GET request for songs`, {
      search,
      providers,
      fetchMode,
      userId,
    });

    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    let results: ApiSongResult[] = [];

    if (fetchMode === "library") {
      logger.info(`[API GET /songs] Fetching library for user: ${userId}`);
      const query = supabase
        .from("songs")
        .select(
          `
          id,
          title,
          primary_artist_name,
          album_name,
          album_cover_url,
          song_art_image_url,
          duration_ms,
          release_date,
          genres,
          spotify_data,
          genius_data,
          user_id
        `,
        )
        .eq("user_id", userId)
        .order("title", { ascending: true });

      // Log the query (optional, for debugging complex queries)
      // console.log("[API GET /songs] Supabase query for library:", query.toString()); // .toString() may not give executable SQL for Supabase JS client

      const { data: librarySongs, error: libraryError } = await query;

      if (libraryError) {
        logger.error(
          "[API GET /songs] Error fetching user's library songs from database",
          { error: libraryError.message, details: libraryError, userId },
        );
        throw libraryError;
      }

      if (librarySongs) {
        logger.info(
          `[API GET /songs] Found ${librarySongs.length} songs in DB for user ${userId}. Mapping...`,
        );
        results = librarySongs.map((song: any) => ({
          id: song.id.toString(),
          title: song.title || "Untitled Song",
          primary_artist_name: song.primary_artist_name,
          album_name: song.album_name,
          album_cover_url:
            song.album_cover_url ||
            song.spotify_data?.album?.images?.[0]?.url ||
            song.genius_data?.album_art_url ||
            song.spotify_data?.album_art_url,
          duration_ms: song.duration_ms,
          provider: "database",
          provider_id: song.id.toString(),
          artist:
            song.primary_artist_name ||
            song.spotify_data?.artists?.[0]?.name ||
            song.genius_data?.primary_artist?.name,
          album: song.album_name || song.spotify_data?.album?.name,
          imageUrl:
            song.song_art_image_url ||
            song.album_cover_url ||
            song.spotify_data?.album?.images?.[0]?.url ||
            song.genius_data?.song_art_image_url ||
            song.spotify_data?.album_art_url,
          genre:
            song.genres && song.genres.length > 0
              ? song.genres[0]
              : song.spotify_data?.genres?.[0],
          year: song.release_date
            ? parseInt(song.release_date.substring(0, 4))
            : undefined,
          valence: song.spotify_data?.valence,
          danceability: song.spotify_data?.danceability,
          energy: song.spotify_data?.energy,
          bpm: song.spotify_data?.tempo,
          key: song.spotify_data?.key,
          mode: song.spotify_data?.mode,
          popularity: song.spotify_data?.popularity,
          isrc: song.spotify_data?.isrc,
          spotify_id: song.spotify_data?.id,
          genius_id: song.genius_data?.id?.toString(),
        }));
        logger.info(
          `[API GET /songs] Finished mapping ${results.length} songs for user ${userId}.`,
        );
      }
    } else {
      // Original search logic (DB + external providers)
      const { data: dbSongs, error: dbError } = await supabase
        .from("songs")
        .select(
          "id, title, primary_artist_name, album_name, album_cover_url, duration_ms",
        )
        .ilike("title", `%${search}%`)
        // Add .eq('user_id', userId) if general search should also be user-specific
        // If general search is public, omit user_id filter here.
        // For now, assuming general search can also be filtered by user if a user is logged in
        // and search is not empty. If search is empty, it might list user's songs or be empty.
        .eq("user_id", userId) // Added user_id filter here too for consistency in search context
        .order("title", { ascending: true })
        .limit(10);

      if (dbError) {
        logger.error("Error searching user's songs in database (search mode)", {
          error: dbError,
          userId,
          search,
        });
        throw dbError;
      }

      if (dbSongs && dbSongs.length > 0) {
        results = dbSongs.map((song) => ({
          id: song.id.toString(),
          title: song.title,
          primary_artist_name: song.primary_artist_name,
          album_name: song.album_name,
          album_cover_url: song.album_cover_url,
          duration_ms: song.duration_ms,
          provider: "database",
          provider_id: song.id.toString(),
        }));
      }

      if (
        results.length < 10 &&
        search.length > 0 &&
        providers &&
        providers.length > 0
      ) {
        try {
          const providerResults = await songService.searchSongs(
            search,
            10 - results.length,
            providers,
          );

          if (providerResults && Array.isArray(providerResults)) {
            // providerResults are expected to conform to a structure that can be spread into ApiSongResult
            // or need explicit mapping.
            const mappedProviderResults: ApiSongResult[] = providerResults.map(
              (pSong: any) => ({
                id: pSong.id, // This might need adjustment if provider ID is not what we use for main `id`
                title: pSong.title,
                artist: pSong.artist, // from provider
                album: pSong.album, // from provider
                imageUrl: pSong.imageUrl, // from provider
                duration_ms: pSong.duration_ms,
                provider: pSong.provider,
                provider_id: pSong.id, // ID from the provider
                // Ensure other ApiSongResult fields are present or undefined
                primary_artist_name: pSong.artist,
                album_name: pSong.album,
                album_cover_url: pSong.imageUrl,
              }),
            );
            results = [...results, ...mappedProviderResults];
          }
        } catch (error) {
          logger.error("Error searching external providers", { error, search });
        }
      }
    }

    const duration = Date.now() - startTime;
    logger.info("GET /api/songs completed", {
      resultsCount: results.length,
      search,
      providers,
      fetchMode,
      duration: `${duration}ms`,
    });
    return NextResponse.json(results);
  } catch (err) {
    const duration = Date.now() - startTime;
    logger.error("Error in GET /api/songs", {
      error: err instanceof Error ? err.message : String(err),
      // stack: err instanceof Error ? err.stack : undefined, // Stack can be very verbose
      duration: `${duration}ms`,
    });
    return NextResponse.json(
      {
        error: "Failed to fetch songs",
        details: err instanceof Error ? err.message : String(err),
      },
      { status: 500 },
    );
  }
}

// POST /api/songs - create a song, enriched with Spotify data
async function createSong(request: AuthenticatedRequest) {
  const startTime = Date.now();
  try {
    logger.info("Processing POST request to create song", {
      userId: request.auth.userId,
    });

    const body = await request.json();
    const { title, primary_artist_name, enrichWithSpotify = true } = body;

    if (!title) {
      return NextResponse.json(
        { error: "Song title is required" },
        { status: 400 },
      );
    }

    // Handle cookies properly
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Prepare the song data
    let songData = {
      ...body,
      user_id: request.auth.userId,
    };

    // Enrich with Spotify data if requested
    if (enrichWithSpotify) {
      logger.debug("Enriching song with Spotify data", {
        title,
        primary_artist_name,
      });
      const spotifyData = await enrichSongWithSpotifyData({
        title,
        primary_artist_name,
      });

      if (spotifyData) {
        songData = {
          ...songData,
          ...spotifyData,
        };
        logger.debug("Song enriched with Spotify data", {
          spotify_id: spotifyData.spotify_id,
        });
      } else {
        logger.warn("No Spotify data found for song", {
          title,
          primary_artist_name,
        });
      }
    }

    // Insert the song
    const { data, error } = await supabase
      .from("songs")
      .insert([songData])
      .select()
      .single();

    if (error) {
      logger.error("Error creating song", { error });
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    const duration = Date.now() - startTime;
    logger.info("Song created successfully", {
      songId: data.id,
      title,
      duration: `${duration}ms`,
    });

    return NextResponse.json(data);
  } catch (err) {
    const duration = Date.now() - startTime;
    logger.error("Error in POST /api/songs", {
      error: err instanceof Error ? err.message : String(err),
      stack: err instanceof Error ? err.stack : undefined,
      duration: `${duration}ms`,
    });

    return NextResponse.json(
      { error: err instanceof Error ? err.message : String(err) },
      { status: 500 },
    );
  }
}

// Export handlers with authentication middleware
export const GET = withAuth(getSongs);
export const POST = withAuth(createSong);
