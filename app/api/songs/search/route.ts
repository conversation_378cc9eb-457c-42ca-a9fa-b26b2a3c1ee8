export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createLogger } from "@/lib/logs/logger";
import {
  searchTracks,
  SpotifyTrackWithFeatures,
} from "@/lib/services/spotify-search";
import { formatDuration } from "@/lib/services/spotify-search";
import { searchGenius, GeniusSong } from "@/lib/queries/geniusQueries";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import {
  Song as DatabaseSong,
  SpotifyData as DatabaseSpotifyData,
  GeniusData as DatabaseGeniusData,
} from "@/lib/queries/songQueries";

const logger = createLogger("song-search-api");

// Helper function to normalize dates (example implementation)
function normalizeDate(dateString?: string | null): string | null {
  if (!dateString) return null;
  try {
    // Attempt to create a date and format it to YYYY-MM-DD
    // This is a basic normalization, might need adjustment based on actual date formats
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString; // Return original if invalid date
    return date.toISOString().split("T")[0];
  } catch (e) {
    return dateString; // Return original on error
  }
}

// Define a more specific type for the API response structure
interface SongSearchResultItem {
  id: string; // Unique ID for the list (can be spotify_id, genius_id, or db_id)
  title: string;
  primary_artist_name?: string;
  album_name?: string;
  album_cover_url?: string | null;
  song_art_image_url?: string | null; // General image, could be from Spotify or Genius
  duration_ms?: number | null;
  duration_formatted?: string;
  release_date?: string | null; // Common release date
  isrc?: string | null;

  provider: "spotify" | "genius" | "database";

  // Spotify specific
  spotify_id?: string;
  // Audio features for Spotify are now expected to be passed directly if available
  tempo?: number | null;
  key?: number | null;
  mode?: number | null;
  time_signature?: number | null;
  acousticness?: number | null;
  danceability?: number | null;
  energy?: number | null;
  instrumentalness?: number | null;
  liveness?: number | null;
  loudness?: number | null;
  speechiness?: number | null;
  valence?: number | null;
  genres?: string[]; // from spotify

  // Genius specific fields for the frontend to construct genius_data
  genius_id?: string | number;
  genius_url?: string;
  genius_full_title?: string; // Genius specific full title
  genius_release_date_for_display?: string; // Genius specific display date
  genius_primary_artist_name?: string; // If Genius has a different primary artist name
  genius_album_name?: string; // If Genius has a different album name
  // genius_song_art_image_url?: string; // If we want a distinct genius image, covered by general song_art_image_url for now

  // Database specific
  db_id?: number;
  // other fields if needed for display from DB like tags, difficulty etc.
}

interface ApiResponse {
  tracks: SongSearchResultItem[];
  total: number;
  query: string | null;
  sources: string[];
  fallbackUsed: boolean;
  errors?: Record<string, string>;
  message?: string; // Added message to the interface
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");
    const limit = parseInt(searchParams.get("limit") || "10");
    const providers = searchParams.get("providers")?.split(",") || [
      "database",
      "spotify",
      "genius",
    ];
    const fallbackEnabled = searchParams.get("fallback") !== "false";

    if (!query) {
      return NextResponse.json(
        { error: "Query parameter 'q' is required" },
        { status: 400 },
      );
    }

    logger.info("Comprehensive song search", {
      query,
      limit,
      providers,
      fallbackEnabled,
    });

    let spotifyResults: SongSearchResultItem[] = [];
    let geniusResults: SongSearchResultItem[] = [];
    let databaseResults: SongSearchResultItem[] = [];

    let sourcesUsed: string[] = [];
    let errors: Record<string, string> = {};

    // --- 1. Search Database ---
    if (providers.includes("database")) {
      try {
        const cookieStore = cookies();
        const supabase = createRouteHandlerClient({
          cookies: () => cookieStore,
        });
        const { data: dbSongs, error: dbError } = await supabase
          .from("songs")
          .select(
            "id, title, primary_artist_name, album_name, song_art_image_url, duration_ms, release_date, spotify_id, genius_id, genres, tempo, key, mode, time_signature, acousticness, danceability, energy, instrumentalness, liveness, loudness, speechiness, valence, isrc",
          )
          .ilike("title", `%${query}%`)
          .order("title", { ascending: true })
          .limit(Math.min(limit, 10)); // Keep DB results somewhat limited to allow for fresh external results

        if (!dbError && dbSongs && dbSongs.length > 0) {
          databaseResults = (dbSongs as unknown as DatabaseSong[]).map(
            (song: DatabaseSong) => ({
              id: song.id.toString(),
              title: song.title,
              primary_artist_name: song.primary_artist_name,
              album_name: song.album_name,
              album_cover_url: song.album_cover_url || null,
              song_art_image_url:
                song.song_art_image_url || song.album_cover_url || null,
              duration_ms: song.duration_ms,
              duration_formatted: song.duration_ms
                ? formatDuration(song.duration_ms)
                : undefined,
              release_date: song.release_date
                ? normalizeDate(song.release_date)
                : null,
              isrc: (song as any).isrc || null,
              provider: "database",
              db_id: song.id,
              spotify_id: (song.spotify_data as DatabaseSpotifyData)?.id,
              tempo: (song.spotify_data as DatabaseSpotifyData)?.tempo,
              key: (song.spotify_data as DatabaseSpotifyData)?.key,
              mode: (song.spotify_data as DatabaseSpotifyData)?.mode,
              time_signature: (song.spotify_data as DatabaseSpotifyData)
                ?.time_signature,
              acousticness: (song.spotify_data as DatabaseSpotifyData)
                ?.acousticness,
              danceability: (song.spotify_data as DatabaseSpotifyData)
                ?.danceability,
              energy: (song.spotify_data as DatabaseSpotifyData)?.energy,
              instrumentalness: (song.spotify_data as DatabaseSpotifyData)
                ?.instrumentalness,
              liveness: (song.spotify_data as DatabaseSpotifyData)?.liveness,
              loudness: (song.spotify_data as DatabaseSpotifyData)?.loudness,
              speechiness: (song.spotify_data as DatabaseSpotifyData)
                ?.speechiness,
              valence: (song.spotify_data as DatabaseSpotifyData)?.valence,
              genres: song.genres || [],

              genius_id: (song.genius_data as DatabaseGeniusData)?.id,
              genius_url: (song.genius_data as DatabaseGeniusData)?.url,
              genius_full_title: (song.genius_data as DatabaseGeniusData)
                ?.full_title,
              genius_release_date_for_display: (
                song.genius_data as DatabaseGeniusData
              )?.release_date_for_display,
              genius_primary_artist_name: (
                song.genius_data as DatabaseGeniusData
              )?.primary_artist_name,
              genius_album_name: (song.genius_data as DatabaseGeniusData)
                ?.album_name,
            }),
          );
          sourcesUsed.push("database");
          logger.info("Database search successful", {
            query,
            resultCount: databaseResults.length,
          });
        } else if (dbError) {
          logger.warn("Database search failed during query execution", {
            query,
            error: dbError.message,
          });
          errors.database = dbError.message;
        }
      } catch (error: any) {
        errors.database = error.message;
        logger.warn("Database search failed with exception", {
          query,
          error: error.message,
        });
      }
    }

    // --- 2. Search Spotify ---
    if (providers.includes("spotify")) {
      try {
        logger.info("Attempting Spotify search", { query });
        const spotifySearchResponse = await searchTracks(query, limit); // Fetch up to the limit initially

        if (
          spotifySearchResponse.tracks &&
          spotifySearchResponse.tracks.items &&
          spotifySearchResponse.tracks.items.length > 0
        ) {
          spotifyResults = spotifySearchResponse.tracks.items.map(
            (track: SpotifyTrackWithFeatures) => ({
              id: track.id, // Spotify ID is the primary ID here
              title: track.name,
              primary_artist_name: track.artists.map((a) => a.name).join(", "),
              album_name: track.album?.name,
              album_cover_url: track.album?.images?.[0]?.url || null,
              song_art_image_url: track.album?.images?.[0]?.url || null,
              duration_ms: track.duration_ms,
              duration_formatted: track.duration_ms
                ? formatDuration(track.duration_ms)
                : undefined,
              release_date: track.album?.release_date
                ? normalizeDate(track.album.release_date)
                : null,
              isrc: (track as any).external_ids?.isrc || null,
              provider: "spotify",
              spotify_id: track.id,
              tempo: track.audio_features?.tempo || null,
              key: track.audio_features?.key || null,
              mode: track.audio_features?.mode || null,
              time_signature: track.audio_features?.time_signature || null,
              acousticness: track.audio_features?.acousticness || null,
              danceability: track.audio_features?.danceability || null,
              energy: track.audio_features?.energy || null,
              instrumentalness: track.audio_features?.instrumentalness || null,
              liveness: track.audio_features?.liveness || null,
              loudness: track.audio_features?.loudness || null,
              speechiness: track.audio_features?.speechiness || null,
              valence: track.audio_features?.valence || null,
              genres:
                (track as any).genres ||
                (track.album as any)?.genres ||
                (track.audio_features as any)?.genres ||
                [],
            }),
          );
          sourcesUsed.push("spotify");
          logger.info("Spotify search successful", {
            query,
            resultCount: spotifyResults.length,
          });
        } else {
          logger.info("Spotify search returned no results", { query });
        }
      } catch (error: any) {
        errors.spotify = error.message;
        logger.warn("Spotify search failed", { query, error: error.message });
      }
    }

    // --- 3. Search Genius ---
    const shouldSearchGenius = providers.includes("genius");
    if (shouldSearchGenius) {
      try {
        logger.info("Attempting Genius search", { query });
        const geniusRawResults = await searchGenius(query); // Fetch up to the limit initially

        if (geniusRawResults && geniusRawResults.length > 0) {
          geniusResults = geniusRawResults.map((result: GeniusSong) => ({
            id: `genius-${result.id}`, // Genius ID is the primary ID here, prefixed
            title: result.title,
            primary_artist_name:
              result.primary_artist?.name || "Unknown Artist",
            // Genius specific fields for genius_data construction on client
            genius_id: String(result.id),
            genius_url: result.url,
            genius_full_title: (result as any).full_title,
            genius_release_date_for_display: (result as any)
              .release_date_for_display,
            genius_primary_artist_name: result.primary_artist?.name,
            genius_album_name: result.album?.name,

            album_name: result.album?.name,
            album_cover_url:
              result.album?.cover_art_url ||
              (result as any).song_art_image_thumbnail_url ||
              null,
            song_art_image_url:
              (result as any).song_art_image_url ||
              result.album?.cover_art_url ||
              null,
            release_date: (result as any).release_date
              ? normalizeDate((result as any).release_date)
              : null,
            provider: "genius",
            // Spotify fields will be null/undefined here unless matched later
          }));
          sourcesUsed.push("genius");
          logger.info("Genius search successful", {
            query,
            resultCount: geniusResults.length,
          });
        } else {
          logger.info("Genius search returned no results", { query });
        }
      } catch (error: any) {
        errors.genius = error.message;
        logger.warn("Genius search failed", { query, error: error.message });
      }
    }

    // --- 4. Combine and Augment Results ---
    let combinedResults: SongSearchResultItem[] = [];
    const DURATION_MATCH_TOLERANCE_MS = 5000; // 5 seconds

    // Normalize strings for matching
    const normalize = (str?: string) => (str || "").toLowerCase().trim();

    spotifyResults.forEach((spotifyTrack) => {
      let augmentedTrack = { ...spotifyTrack }; // Start with the Spotify track
      const geniusMatch = geniusResults.find((geniusSong) => {
        const titleMatch =
          normalize(geniusSong.title) === normalize(spotifyTrack.title);
        const artistMatch =
          normalize(geniusSong.primary_artist_name) ===
          normalize(spotifyTrack.primary_artist_name);
        // Album match is optional, consider it a stronger signal if present
        const albumMatch =
          geniusSong.album_name && spotifyTrack.album_name
            ? normalize(geniusSong.album_name) ===
              normalize(spotifyTrack.album_name)
            : true; // True if one or both don't have album

        let durationMatch = true; // Assume true if Genius doesn't have duration
        if (geniusSong.duration_ms && spotifyTrack.duration_ms) {
          // Only compare if both have it
          durationMatch =
            Math.abs(geniusSong.duration_ms - spotifyTrack.duration_ms) <=
            DURATION_MATCH_TOLERANCE_MS;
        }

        return titleMatch && artistMatch && albumMatch && durationMatch;
      });

      if (geniusMatch) {
        augmentedTrack.genius_id = geniusMatch.genius_id
          ? String(geniusMatch.genius_id)
          : undefined;
        augmentedTrack.genius_url = geniusMatch.genius_url;
        // Remove the matched Genius song so it's not added again as a separate item
        geniusResults = geniusResults.filter((g) => g.id !== geniusMatch.id);
        logger.info("Matched Spotify track with Genius data", {
          spotify: spotifyTrack.title,
          genius: geniusMatch.title,
        });
      }
      combinedResults.push(augmentedTrack);
    });

    // Add remaining Genius results that weren't matched
    combinedResults.push(...geniusResults);

    // Add database results (they are already SongSearchResultItem compliant)
    // We might want to prefer fresh data over DB, or merge if DB entry is older and lacks spotify/genius IDs
    // For now, simple concatenation and then deduplication by unique provider IDs or a combination
    let allProcessedResults = [...databaseResults, ...combinedResults];

    // --- 5. Deduplicate Results ---
    // Deduplicate based on a hierarchy: prefer Spotify if same song, then Genius, then DB if IDs match.
    // More robust deduplication might involve checking (title, artist) for items from different providers without shared IDs.
    const finalUniqueResults: SongSearchResultItem[] = [];
    const seenSpotifyIds = new Set<string>();
    const seenGeniusIds = new Set<string>();
    const seenDatabaseIds = new Set<string>();
    const seenTitleArtist = new Set<string>();

    allProcessedResults.forEach((item) => {
      const titleArtistKey = `${normalize(item.title)}|${normalize(item.primary_artist_name)}`;
      let add = false;

      if (item.spotify_id) {
        if (!seenSpotifyIds.has(item.spotify_id)) {
          seenSpotifyIds.add(item.spotify_id);
          add = true;
        }
      } else if (item.genius_id) {
        if (!seenGeniusIds.has(item.genius_id.toString())) {
          seenGeniusIds.add(item.genius_id.toString());
          add = true;
        }
      } else if (item.provider === "database") {
        if (!seenDatabaseIds.has(item.id)) {
          // item.id is DB id here
          seenDatabaseIds.add(item.id);
          add = true;
        }
      }

      // If we decided to add based on ID, or if no ID matched but title/artist is new
      if (add || !seenTitleArtist.has(titleArtistKey)) {
        finalUniqueResults.push(item);
        seenTitleArtist.add(titleArtistKey);
        // If it was added due to title/artist, also mark its IDs as seen if they exist
        if (item.spotify_id) seenSpotifyIds.add(item.spotify_id);
        if (item.genius_id) seenGeniusIds.add(item.genius_id.toString());
        if (item.provider === "database") seenDatabaseIds.add(item.id);
      }
    });

    const finalResults = finalUniqueResults.slice(0, limit);

    // --- 6. Prepare Response ---
    // Determine if any providers actually returned results vs. just being attempted
    const successfulSources = sourcesUsed.filter((source) => {
      if (source === "spotify" && spotifyResults.length > 0) return true;
      if (source === "genius" && geniusResults.length > 0) return true;
      if (source === "database" && databaseResults.length > 0) return true;
      return false;
    });

    const apiResponse: ApiResponse = {
      tracks: finalResults,
      total: finalResults.length,
      query,
      sources: sourcesUsed, // Reflects sources attempted
      fallbackUsed: false, // Simplified: remove noisy fallback logic for user messages
      errors: Object.keys(errors).length > 0 ? errors : undefined, // Keep for internal logging/debugging
    };

    if (finalResults.length === 0) {
      if (successfulSources.length === 0 && sourcesUsed.length > 0) {
        // Only message if all attempted sources yielded no results or failed
        apiResponse.message =
          "No songs found. All attempted search providers returned no results or encountered an error.";
      } else if (sourcesUsed.length === 0 && providers.length > 0) {
        // This case should ideally not happen if providers array is not empty
        apiResponse.message =
          "No search providers were configured or enabled for the query.";
      } else {
        apiResponse.message =
          "No songs found matching your search criteria from available sources.";
      }
    } else {
      // Results found, no need for a top-level error/warning message about providers
      // Individual provider errors are still in apiResponse.errors for debugging if needed
      // We can optionally add a success message if desired, but often just showing results is enough
    }

    logger.info("Song search completed", {
      query,
      totalResults: finalResults.length,
      sources: sourcesUsed,
      fallbackUsed: false,
      errors: apiResponse.errors,
    });

    return NextResponse.json(apiResponse);
  } catch (error: any) {
    const currentQuery = new URL(request.url).searchParams.get("q");
    logger.error("Error in song search API", {
      error: error.message,
      query: currentQuery,
    });
    return NextResponse.json(
      { error: "Failed to search songs", details: error.message },
      { status: 500 },
    );
  }
}
