export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = createRouteHandlerClient({ cookies });
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    console.log("🔍 Checking Spotify environment variables...");

    const clientId = process.env.SPOTIFY_CLIENT_ID;
    const clientSecret = process.env.SPOTIFY_CLIENT_SECRET;

    // Log for debugging (without exposing actual values)
    console.log("Environment check:", {
      hasClientId: !!clientId,
      hasClientSecret: !!clientSecret,
      clientIdLength: clientId?.length || 0,
      clientSecretLength: clientSecret?.length || 0,
      clientIdPrefix: clientId?.substring(0, 8) + "...",
      clientSecretPrefix: clientSecret?.substring(0, 8) + "...",
    });

    const envCheck = {
      timestamp: new Date().toISOString(),
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
      },
      credentials: {
        hasClientId: !!clientId,
        hasClientSecret: !!clientSecret,
        clientIdLength: clientId?.length || 0,
        clientSecretLength: clientSecret?.length || 0,
        clientIdValid: !!(
          clientId &&
          clientId.length > 10 &&
          clientId !== "your_spotify_client_id_here"
        ),
        clientSecretValid: !!(
          clientSecret &&
          clientSecret.length > 10 &&
          clientSecret !== "your_spotify_client_secret_here"
        ),
        // Safe prefixes for debugging
        clientIdPrefix: clientId ? clientId.substring(0, 8) + "..." : null,
        clientSecretPrefix: clientSecret
          ? clientSecret.substring(0, 8) + "..."
          : null,
      },
      allEnvVars: Object.keys(process.env)
        .filter((key) => key.includes("SPOTIFY"))
        .map((key) => ({
          name: key,
          hasValue: !!process.env[key],
          length: process.env[key]?.length || 0,
        })),
    };

    return NextResponse.json(envCheck);
  } catch (error: any) {
    console.error("❌ Error checking environment:", error);

    return NextResponse.json(
      {
        error: "Failed to check environment",
        details: error.message,
      },
      { status: 500 },
    );
  }
}
