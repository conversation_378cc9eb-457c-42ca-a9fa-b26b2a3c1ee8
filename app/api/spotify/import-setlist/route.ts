export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { getPlaylist } from "@/lib/services/spotify";

interface SpotifyTrack {
  id: string;
  name: string;
  artists: string[];
  album: string;
  duration_ms: number;
  external_urls: any;
  preview_url: string | null;
  popularity: number;
  explicit: boolean;
  isrc?: string;
}

interface ImportRequest {
  playlistUrl: string;
  setlistTitle: string;
  setlistDescription?: string;
  purpose: string;
  genres: string[];
  breakConfig: {
    enabled: boolean;
    type: "songs" | "minutes";
    interval: number; // number of songs or minutes
    breakDuration: number; // break duration in minutes
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: ImportRequest = await request.json();
    const {
      playlistUrl,
      setlistTitle,
      setlistDescription,
      purpose,
      genres,
      breakConfig,
    } = body;

    // Verify user is authenticated
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies });
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Get user's current profile
    const { data: profile } = await supabase
      .from("profiles")
      .select("id")
      .eq("user_id", user.id)
      .single();

    if (!profile) {
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 },
      );
    }

    // Extract playlist ID and fetch tracks
    const playlistId = extractPlaylistId(playlistUrl);
    if (!playlistId) {
      return NextResponse.json(
        { error: "Invalid Spotify playlist URL" },
        { status: 400 },
      );
    }

    const playlistData = await getPlaylist(playlistId);

    if (!playlistData) {
      return NextResponse.json(
        { error: "Failed to fetch playlist from Spotify" },
        { status: 404 },
      );
    }

    // Transform Spotify tracks
    const spotifyTracks: SpotifyTrack[] = playlistData.tracks.items
      .filter((item: any) => item.track && item.track.type === "track")
      .map((item: any) => {
        const track = item.track;
        return {
          id: track.id,
          name: track.name,
          artists: track.artists.map((artist: any) => artist.name),
          album: track.album.name,
          duration_ms: track.duration_ms,
          external_urls: track.external_urls,
          preview_url: track.preview_url,
          popularity: track.popularity,
          explicit: track.explicit,
          isrc: track.external_ids?.isrc,
        };
      });

    // Get existing songs from user's repertoire
    const { data: existingSongs } = await supabase
      .from("songs")
      .select("*")
      .eq("user_id", user.id);

    // Match tracks with existing songs and create new ones for unmatched tracks
    const { matchedSongs, newSongs } = await matchAndCreateSongs(
      spotifyTracks,
      existingSongs || [],
      user.id,
      supabase,
    );

    // Create setlist entries with optional breaks
    const setlistEntries = createSetlistEntries(
      [...matchedSongs, ...newSongs],
      breakConfig,
    );

    // Create the setlist
    const slug = setlistTitle.trim().toLowerCase().replace(/\W+/g, "-");
    const { data: newSetlist, error: setlistError } = await supabase
      .from("setlists")
      .insert({
        creator_profile_id: profile.id,
        title: setlistTitle,
        slug,
        description:
          setlistDescription ||
          `Imported from Spotify playlist: ${playlistData.name}`,
        purpose,
        genres,
        songs: setlistEntries,
        is_public: false,
        shared_with: [],
        is_rehearsal: false,
        performance_readiness: 0,
      })
      .select()
      .single();

    if (setlistError) {
      console.error("Failed to create setlist:", setlistError);
      return NextResponse.json(
        { error: "Failed to create setlist" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      setlist: newSetlist,
      stats: {
        totalTracks: spotifyTracks.length,
        matchedSongs: matchedSongs.length,
        newSongs: newSongs.length,
        breaksAdded: setlistEntries.filter((entry) => entry.type === "break")
          .length,
      },
    });
  } catch (error) {
    console.error("Error importing Spotify playlist:", error);
    return NextResponse.json(
      { error: "Failed to import playlist" },
      { status: 500 },
    );
  }
}

async function matchAndCreateSongs(
  spotifyTracks: SpotifyTrack[],
  existingSongs: any[],
  userId: string,
  supabase: any,
) {
  const matchedSongs: any[] = [];
  const newSongs: any[] = [];

  for (const track of spotifyTracks) {
    // Try to match by title and artist
    const match = existingSongs.find(
      (song) =>
        song.title.toLowerCase() === track.name.toLowerCase() &&
        track.artists.some(
          (artist) =>
            song.primary_artist_name
              ?.toLowerCase()
              .includes(artist.toLowerCase()) ||
            artist
              .toLowerCase()
              .includes(song.primary_artist_name?.toLowerCase()),
        ),
    );

    if (match) {
      matchedSongs.push(match);
    } else {
      // Create new song
      const newSong = {
        user_id: userId,
        title: track.name,
        primary_artist_name: track.artists[0],
        album_name: track.album,
        duration_ms: track.duration_ms,
        spotify_id: track.id,
        spotify_url: track.external_urls.spotify,
        preview_url: track.preview_url,
        popularity: track.popularity,
        explicit: track.explicit,
        isrc: track.isrc,
        confidence_level: 1, // Default confidence for imported songs
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { data: createdSong, error } = await supabase
        .from("songs")
        .insert(newSong)
        .select()
        .single();

      if (!error && createdSong) {
        newSongs.push(createdSong);
      }
    }
  }

  return { matchedSongs, newSongs };
}

function createSetlistEntries(songs: any[], breakConfig: any) {
  const entries: any[] = [];
  let position = 0;

  for (let i = 0; i < songs.length; i++) {
    const song = songs[i];

    // Add song entry
    entries.push({
      type: "song",
      position: position++,
      song_uuid: song.id.toString(),
      variant: null,
      title: song.title,
      notes: "",
      provider: song.primary_artist_name,
      confidence: song.confidence_level,
      duration_ms: song.duration_ms,
    });

    // Add break if configured and not the last song
    if (breakConfig.enabled && i < songs.length - 1) {
      const shouldAddBreak =
        breakConfig.type === "songs"
          ? (i + 1) % breakConfig.interval === 0
          : shouldAddBreakByTime(entries, breakConfig.interval);

      if (shouldAddBreak) {
        entries.push({
          type: "break",
          position: position++,
          title: `Break ${Math.floor(i / breakConfig.interval) + 1}`,
          length: breakConfig.breakDuration * 60, // Convert minutes to seconds
          notes: "Auto-generated break",
        });
      }
    }
  }

  return entries;
}

function shouldAddBreakByTime(
  entries: any[],
  intervalMinutes: number,
): boolean {
  // Calculate total duration of songs since last break
  let durationSinceLastBreak = 0;

  for (let i = entries.length - 1; i >= 0; i--) {
    const entry = entries[i];
    if (entry.type === "break") break;
    if (entry.type === "song" && entry.duration_ms) {
      durationSinceLastBreak += entry.duration_ms;
    }
  }

  const minutesSinceLastBreak = durationSinceLastBreak / (1000 * 60);
  return minutesSinceLastBreak >= intervalMinutes;
}

function extractPlaylistId(url: string): string | null {
  const patterns = [
    /spotify\.com\/playlist\/([a-zA-Z0-9]+)/,
    /spotify:playlist:([a-zA-Z0-9]+)/,
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return match[1];
    }
  }

  return null;
}
