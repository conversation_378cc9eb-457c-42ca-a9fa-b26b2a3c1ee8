export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { getPlaylist } from "@/lib/services/spotify";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function POST(request: NextRequest) {
  const cookieStore = await cookies();
  try {
    const { playlistUrl } = await request.json();

    if (!playlistUrl) {
      return NextResponse.json(
        { error: "Playlist URL is required" },
        { status: 400 },
      );
    }

    // Verify user is authenticated
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Extract playlist ID from URL
    const playlistId = extractPlaylistId(playlistUrl);
    if (!playlistId) {
      return NextResponse.json(
        { error: "Invalid Spotify playlist URL" },
        { status: 400 },
      );
    }

    // Fetch playlist details and tracks
    const playlistData = await getPlaylist(playlistId);

    if (!playlistData) {
      return NextResponse.json(
        {
          error:
            "Failed to fetch playlist from Spotify. The playlist might be private or the URL might be incorrect.",
        },
        { status: 404 },
      );
    }

    // Transform tracks to our format
    const tracks = playlistData.tracks.items
      .filter((item: any) => item.track && item.track.type === "track")
      .map((item: any) => {
        const track = item.track;
        return {
          id: track.id,
          name: track.name,
          artists: track.artists.map((artist: any) => artist.name),
          album: track.album.name,
          duration_ms: track.duration_ms,
          external_urls: track.external_urls,
          preview_url: track.preview_url,
          popularity: track.popularity,
          explicit: track.explicit,
          isrc: track.external_ids?.isrc,
        };
      });

    return NextResponse.json({
      playlist: {
        id: playlistData.id,
        name: playlistData.name,
        description: playlistData.description,
        owner: playlistData.owner.display_name,
        public: playlistData.public,
        collaborative: playlistData.collaborative,
        total_tracks: playlistData.tracks.total,
        external_urls: playlistData.external_urls,
      },
      tracks,
    });
  } catch (error: any) {
    console.error("Error fetching Spotify playlist:", error);

    // Check if it's a network connectivity error
    const isNetworkError =
      error.message?.includes("ENETUNREACH") ||
      error.message?.includes("network") ||
      error.message?.includes("timeout") ||
      error.message?.includes("Failed to fetch");

    if (isNetworkError) {
      return NextResponse.json(
        {
          error:
            "Unable to connect to Spotify's servers. Please check your internet connection and try again.",
          type: "network_error",
        },
        { status: 503 }, // Service Unavailable
      );
    }

    // Check if it's a credentials error
    if (
      error.message?.includes("credentials") ||
      error.message?.includes("token")
    ) {
      return NextResponse.json(
        {
          error: "Spotify service configuration error. Please contact support.",
          type: "config_error",
        },
        { status: 502 }, // Bad Gateway
      );
    }

    // Generic error
    return NextResponse.json(
      {
        error: "Failed to fetch playlist from Spotify. Please try again later.",
        type: "unknown_error",
      },
      { status: 500 },
    );
  }
}

function extractPlaylistId(url: string): string | null {
  // Handle various Spotify playlist URL formats:
  // https://open.spotify.com/playlist/37i9dQZF1DXcBWIGoYBM5M
  // https://open.spotify.com/playlist/37i9dQZF1DXcBWIGoYBM5M?si=...
  // spotify:playlist:37i9dQZF1DXcBWIGoYBM5M

  const patterns = [
    /spotify\.com\/playlist\/([a-zA-Z0-9]+)/,
    /spotify:playlist:([a-zA-Z0-9]+)/,
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return match[1];
    }
  }

  return null;
}
