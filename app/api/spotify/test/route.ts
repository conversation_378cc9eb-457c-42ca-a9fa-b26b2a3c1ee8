export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { runSpotifyAPITests } from "@/lib/tests/spotify-api-test";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated (optional - you might want to restrict this)
    const supabase = createRouteHandlerClient({ cookies });
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    console.log("🧪 Running comprehensive Spotify API tests...");

    const testResults = await runSpotifyAPITests();

    console.log("✅ Spotify API tests completed:", {
      passed: testResults.summary.passed,
      failed: testResults.summary.failed,
      duration: `${testResults.summary.totalDuration}ms`,
    });

    return NextResponse.json(testResults);
  } catch (error: any) {
    console.error("❌ Error running Spotify API tests:", error);

    return NextResponse.json(
      {
        error: "Failed to run Spotify API tests",
        details: error.message,
      },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { testName } = await request.json();

    // Check if user is authenticated
    const supabase = createRouteHandlerClient({ cookies });
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    console.log(`🧪 Running specific Spotify API test: ${testName}`);

    // For now, just run all tests - could be extended to run specific tests
    const testResults = await runSpotifyAPITests();

    return NextResponse.json(testResults);
  } catch (error: any) {
    console.error("❌ Error running specific Spotify API test:", error);

    return NextResponse.json(
      {
        error: "Failed to run Spotify API test",
        details: error.message,
      },
      { status: 500 },
    );
  }
}
