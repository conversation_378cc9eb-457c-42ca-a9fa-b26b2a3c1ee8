import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { getTrack, getAudioFeatures } from "@/lib/services/spotify";
import { createApiLogger } from "@/lib/logs/api-logger";

export const runtime = "nodejs";

// Create route-specific logger
const logger = createApiLogger("spotify-track");

// GET /api/spotify/track/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  const startTime = Date.now();
  try {
    const { id } = await params;
    const trackId = id;
    logger.info(`Processing Spotify track request`, { trackId });

    // Handle cookies properly
    const supabase = createRouteHandlerClient({ cookies });

    // Require user to be authenticated
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      logger.warn("Unauthorized API access attempt");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    logger.debug("User authenticated", { userId: user.id });

    return await logger.traceCall("getSpotifyTrackDetails", async () => {
      // Get track details and audio features in parallel
      const [track, audioFeatures] = await Promise.all([
        getTrack(trackId),
        getAudioFeatures(trackId),
      ]);

      // Combine the data
      const result = {
        id: track.id,
        title: track.name,
        duration_ms: track.duration_ms,
        album: {
          name: track.album.name,
          cover_url: track.album.images[0]?.url,
          release_date: track.album.release_date,
        },
        artists: track.artists.map((artist) => ({
          id: artist.id,
          name: artist.name,
        })),
        audio_features: {
          tempo: audioFeatures.tempo,
          key: audioFeatures.key,
          mode: audioFeatures.mode,
          time_signature: audioFeatures.time_signature,
          acousticness: audioFeatures.acousticness,
          danceability: audioFeatures.danceability,
          energy: audioFeatures.energy,
          instrumentalness: audioFeatures.instrumentalness,
          liveness: audioFeatures.liveness,
          loudness: audioFeatures.loudness,
          speechiness: audioFeatures.speechiness,
          valence: audioFeatures.valence,
        },
      };

      const duration = Date.now() - startTime;
      logger.info("Spotify track details fetched", {
        trackId,
        duration: `${duration}ms`,
      });

      return NextResponse.json(result);
    });
  } catch (err) {
    const duration = Date.now() - startTime;
    logger.error("Error fetching Spotify track", {
      error: err instanceof Error ? err.message : String(err),
      stack: err instanceof Error ? err.stack : undefined,
      duration: `${duration}ms`,
    });

    return NextResponse.json(
      { error: err instanceof Error ? err.message : String(err) },
      { status: 500 },
    );
  }
}
