export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
  try {
    // Get the current user from Supabase auth using consistent pattern
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const user = session.user;
    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const profileId = searchParams.get("profile_id") || user.id;

    // Verify user has access to the requested profile
    if (profileId !== user.id) {
      // Check if it's a child profile the user has access to
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("user_id")
        .eq("id", profileId)
        .single();

      if (profileError || profile.user_id !== user.id) {
        return NextResponse.json({ error: "Access denied" }, { status: 403 });
      }
    }

    // Fetch transactions
    const { data, error } = await supabase
      .from("money")
      .select("*")
      .eq("profile_id", profileId)
      .order("transaction_date", { ascending: false });

    if (error) {
      console.error("Error fetching transactions:", error);
      return NextResponse.json(
        { error: "Failed to fetch transactions" },
        { status: 500 },
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error in transactions API:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch transactions" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the current user from Supabase auth using consistent pattern
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error("Session error:", sessionError || "No session found");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    const user = session.user;
    if (!user) {
      console.error("No user in session");
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      amount,
      transaction_type,
      description,
      transaction_date,
      profile_id,
      metadata,
      event_associated,
      equipment_associated,
    } = body;

    // Validate required fields
    if (!amount || !transaction_type || !description) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: amount, transaction_type, description",
        },
        { status: 400 },
      );
    }

    // Use the authenticated user's ID if no profile_id provided
    const targetProfileId = profile_id || user.id;

    // Verify user has access to the target profile
    if (targetProfileId !== user.id) {
      // Check if it's a child profile the user has access to
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("user_id")
        .eq("id", targetProfileId)
        .single();

      if (profileError || profile.user_id !== user.id) {
        return NextResponse.json({ error: "Access denied" }, { status: 403 });
      }
    }

    // Create the transaction
    const transactionData = {
      profile_id: targetProfileId,
      amount: parseFloat(amount),
      transaction_type,
      description,
      transaction_date: transaction_date || new Date().toISOString(),
      metadata: metadata || {},
      event_associated,
      equipment_associated,
    };

    const { data, error } = await supabase
      .from("money")
      .insert([transactionData])
      .select()
      .single();

    if (error) {
      console.error("Error creating transaction:", error);
      return NextResponse.json(
        { error: "Failed to create transaction" },
        { status: 500 },
      );
    }

    return NextResponse.json(data, { status: 201 });
  } catch (error: any) {
    console.error("Error in transactions API:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create transaction" },
      { status: 500 },
    );
  }
}
