"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import type { Database } from "@/lib/supabase-types";
import { fetchCoreUserData } from "@/lib/queries/userData";
import { useAppStore } from "@/lib/store";
import { Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createClientComponentClient<Database>();
  const [status, setStatus] = useState<"loading" | "success" | "error">("loading");
  const [message, setMessage] = useState("");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log("[AuthCallback] Processing auth callback...");
        
        // Handle the auth callback
        const { data, error: authError } = await supabase.auth.getSession();
        
        if (authError) {
          console.error("[AuthCallback] Auth error:", authError);
          setStatus("error");
          setError(authError.message);
          return;
        }

        if (!data.session) {
          console.log("[AuthCallback] No session found, checking URL params...");
          
          // Check for error in URL params
          const errorParam = searchParams.get("error");
          const errorDescription = searchParams.get("error_description");
          
          if (errorParam) {
            console.error("[AuthCallback] URL error:", errorParam, errorDescription);
            setStatus("error");
            setError(errorDescription || errorParam);
            return;
          }
          
          // No session and no error - might be email confirmation
          setStatus("success");
          setMessage("Email confirmed successfully! Please sign in to continue.");
          
          // Redirect to sign in after a delay
          setTimeout(() => {
            router.push("/auth/signin?message=email_confirmed");
          }, 3000);
          return;
        }

        // We have a session - user is authenticated
        console.log("[AuthCallback] Session found, user authenticated:", data.session.user.id);
        
        try {
          // Fetch user data to check if profile exists
          const userData = await fetchCoreUserData(data.session.user.id);
          
          if (!userData.profile) {
            console.log("[AuthCallback] No profile found, redirecting to profile creation");
            setStatus("success");
            setMessage("Account confirmed! Setting up your profile...");
            
            // Update store with auth user
            useAppStore.getState().setCurrentAuthUser(data.session.user);
            
            // Redirect to profile creation
            setTimeout(() => {
              router.push("/new-profile");
            }, 2000);
            return;
          }
          
          // Profile exists - complete sign in
          console.log("[AuthCallback] Profile found, completing sign in");
          useAppStore.getState().setCoreUserData(userData);
          
          setStatus("success");
          setMessage("Welcome back! Redirecting to your dashboard...");
          
          // Redirect to dashboard
          setTimeout(() => {
            router.push("/");
          }, 2000);
          
        } catch (userDataError) {
          console.error("[AuthCallback] Error fetching user data:", userDataError);
          setStatus("error");
          setError("Failed to load user profile. Please try signing in manually.");
        }
        
      } catch (error: any) {
        console.error("[AuthCallback] Unexpected error:", error);
        setStatus("error");
        setError(error.message || "An unexpected error occurred");
      }
    };

    handleAuthCallback();
  }, [router, searchParams, supabase]);

  const handleRetry = () => {
    window.location.reload();
  };

  const handleSignIn = () => {
    router.push("/auth/signin");
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-purple-waves bg-cover bg-center py-12">
      <div className="container flex flex-col items-center justify-center px-4 md:px-6">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              {status === "loading" && (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  Processing...
                </>
              )}
              {status === "success" && (
                <>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  Success!
                </>
              )}
              {status === "error" && (
                <>
                  <AlertCircle className="h-5 w-5 text-red-500" />
                  Something went wrong
                </>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {status === "loading" && (
              <div className="text-center">
                <p className="text-muted-foreground">
                  Please wait while we confirm your email and set up your account...
                </p>
              </div>
            )}
            
            {status === "success" && (
              <div className="text-center">
                <p className="text-green-600 font-medium">{message}</p>
                <p className="text-sm text-muted-foreground mt-2">
                  You'll be redirected automatically in a few seconds.
                </p>
              </div>
            )}
            
            {status === "error" && (
              <div className="space-y-4">
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    {error || "An unexpected error occurred during authentication."}
                  </AlertDescription>
                </Alert>
                
                <div className="flex flex-col gap-2">
                  <Button onClick={handleRetry} variant="outline" className="w-full">
                    Try Again
                  </Button>
                  <Button onClick={handleSignIn} className="w-full">
                    Go to Sign In
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
