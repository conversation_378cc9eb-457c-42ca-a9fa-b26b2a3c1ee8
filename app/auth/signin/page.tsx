"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { useAppStore } from "@/lib/store";
import { fetchCoreUserData } from "@/lib/queries/userData";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import type { Database } from "@/lib/supabase-types";

// Renaming back to SignInPage, but keeping minimal JSX for now
export const dynamic = "force-dynamic";

export default function SignInPage() {
  console.log("[SignInPage] Rendering (Step 4 - Full handleSignIn Active).");

  const supabase = createClientComponentClient<Database>();
  const router = useRouter();

  const {
    isLoading: storeIsLoading,
    error: storeError,
    authUser,
    currentProfile,
    clearCoreUserData, // Get clearCoreUserData from the store
  } = useAppStore();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pageError, setPageError] = useState<string | null>(null);
  const [redirectPath, setRedirectPath] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Force isLoading to false on component mount to ensure form is interactive
  useEffect(() => {
    console.log(
      "[SignInPage] Forcing isLoading to false to ensure form is interactive",
    );
    useAppStore.getState().setIsLoading(false);
  }, []);

  // Restore useEffect for redirectPath and success messages
  useEffect(() => {
    if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search);
      const redirectedFrom = params.get("redirectedFrom");
      const message = params.get("message");

      if (redirectedFrom) {
        console.log("[SignInPage] redirectPath found:", redirectedFrom);
        setRedirectPath(redirectedFrom);
      }

      if (message === "email_confirmed") {
        setSuccessMessage("Email confirmed successfully! You can now sign in.");
      }
    }
  }, []); // Empty dependency array, runs once on mount

  // Restore useEffect for handling redirection and errors
  useEffect(() => {
    console.log(
      "[SignInPage] Auth/Profile Effect triggered. StoreisLoading:",
      storeIsLoading,
      "AuthUser:",
      !!authUser,
      "CurrentProfile:",
      !!currentProfile,
      "StoreError:",
      storeError,
    );
    // Only redirect if not loading, we have an authUser, and a currentProfile
    if (!storeIsLoading && authUser && currentProfile) {
      console.log(
        "[SignInPage] AuthUser and CurrentProfile detected, attempting redirect.",
      );
      const targetPath = redirectPath || "/";
      console.log(`[SignInPage] Redirecting to ${targetPath}`);
      router.push(targetPath);
    }
    // Handle case where user is authenticated but profile fetch failed or no profile (and no explicit error yet from useAuth)
    else if (!storeIsLoading && authUser && !currentProfile && !storeError) {
      console.log(
        "[SignInPage] AuthUser detected, but no CurrentProfile and no explicit storeError. Redirecting to new-profile.",
      );
      router.push("/new-profile");
    }
    // If there's a storeError (e.g. from useAuth failing to fetch profile due to RLS)
    else if (storeError) {
      console.log("[SignInPage] Store error detected:", storeError);
      setPageError(storeError);
    }
    // Clear pageError if storeError is cleared and not loading (e.g. after a successful retry or logout)
    else if (!storeError && pageError) {
      console.log("[SignInPage] Store error cleared, clearing pageError.");
      setPageError(null);
    }
  }, [
    authUser,
    currentProfile,
    storeIsLoading,
    storeError,
    router,
    redirectPath,
    pageError,
  ]);

  // Enhanced handleSignIn logic with better error handling and state management
  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setPageError(null); // Clear previous page-specific errors

    // Clear any existing auth state to ensure a clean start
    clearCoreUserData();

    console.log(`[SignInPage] Attempting Supabase sign-in for email: ${email}`);

    try {
      // First, ensure we're starting with a clean session
      await supabase.auth.signOut({ scope: "local" });

      // Now attempt to sign in
      const { data, error: signInError } =
        await supabase.auth.signInWithPassword({
          email,
          password,
        });

      if (signInError) {
        console.error(
          "[SignInPage] Supabase signInWithPassword error:",
          signInError,
        );
        setPageError(
          signInError.message || "Sign in failed. Please check credentials.",
        );
        return;
      }

      if (!data.session || !data.user) {
        console.error(
          "[SignInPage] Sign in succeeded but no session or user returned",
        );
        setPageError(
          "Authentication succeeded but session data is missing. Please try again.",
        );
        return;
      }

      console.log(
        "[SignInPage] Supabase signInWithPassword successful. User ID:",
        data.user.id,
      );

      // Manually update the store with the user
      useAppStore.getState().setCurrentAuthUser(data.user as any);

      // Fetch core user data
      try {
        console.log("[SignInPage] Fetching core user data");
        const userData = await fetchCoreUserData(data.user.id);

        if (!userData.profile) {
          console.warn("[SignInPage] No profile found in core user data");
          // Redirect to new profile page
          router.push("/new-profile");
          return;
        }

        // Check if profile is just a basic auto-created one
        const isBasicProfile = userData.profile.username.startsWith('user_') &&
                               !userData.profile.first_name &&
                               !userData.profile.last_name &&
                               (!userData.profile.roles || userData.profile.roles.length === 0) &&
                               !userData.profile.bio;

        if (isBasicProfile) {
          console.log("[SignInPage] Basic auto-created profile found, redirecting to profile customization");
          // Update store with auth user and basic profile
          useAppStore.getState().setCurrentAuthUser(data.user as any);
          useAppStore.getState().setCoreUserData(userData);

          // Redirect to profile customization
          router.push("/new-profile");
          return;
        }

        // Update the store with the core data
        useAppStore.getState().setCoreUserData(userData);

        // Redirect to the appropriate page
        const targetPath = redirectPath || "/";
        console.log(
          `[SignInPage] Sign in complete, redirecting to ${targetPath}`,
        );
        router.push(targetPath);
      } catch (coreDataError) {
        console.error(
          "[SignInPage] Error fetching core user data:",
          coreDataError,
        );
        setPageError(
          `Error loading your profile: ${coreDataError instanceof Error ? coreDataError.message : String(coreDataError)}`,
        );
      }
    } catch (error: any) {
      console.error(
        "[SignInPage] Unexpected error in handleSignIn try/catch block:",
        error,
      );
      setPageError(
        error.message || "An unexpected error occurred during sign in.",
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-purple-waves bg-cover bg-center py-12">
      <div className="container flex flex-col items-center justify-center px-4 md:px-6">
        <div className="mb-8">
          <Image
            src="/crescender-logo.svg"
            alt="Crescender"
            width={250}
            height={70}
            style={{ width: "auto", height: "auto" }}
            priority
          />
        </div>
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center">
              Sign in
            </CardTitle>
            <CardDescription className="text-center">
              Enter your email and password to access your account
            </CardDescription>
          </CardHeader>
          <CardContent>
            {successMessage && (
              <Alert variant="default" className="mb-4 border-green-200 bg-green-50">
                <AlertDescription className="text-green-700">
                  {successMessage}
                </AlertDescription>
              </Alert>
            )}
            {(pageError || storeError) && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{pageError || storeError}</AlertDescription>
              </Alert>
            )}
            <form onSubmit={handleSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  autoComplete="email"
                  required
                  disabled={isSubmitting}
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    href="/auth/forgot-password"
                    className="text-sm text-crescender-600 hover:text-crescender-700"
                  >
                    Forgot password?
                  </Link>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  autoComplete="current-password"
                  required
                  disabled={isSubmitting}
                />
              </div>
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? "Signing in..." : "Sign in"}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col">
            <div className="text-center text-sm">
              Don't have an account?{" "}
              <Link
                href="/auth/signup"
                className="text-crescender-600 hover:text-crescender-700"
              >
                Sign up
              </Link>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
