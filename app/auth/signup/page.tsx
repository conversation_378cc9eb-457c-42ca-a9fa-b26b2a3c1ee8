"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import type { Database } from "@/lib/supabase-types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  PasswordStrengthIndicator,
  validatePassword,
} from "@/components/auth/password-strength-indicator";
import { EnhancedErrorDisplay } from "@/components/auth/enhanced-error-display";
import { Check, X } from "lucide-react";

export const dynamic = "force-dynamic";

export default function SignUpPage() {
  const router = useRouter();
  const supabase = createClientComponentClient<Database>();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<any>(null);
  const [showPasswordHelp, setShowPasswordHelp] = useState(false);

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    if (password !== confirmPassword) {
      setError(new Error("Passwords do not match"));
      setIsLoading(false);
      return;
    }

    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      setError(
        new Error(
          `Password requirements not met: ${passwordValidation.errors.join(", ")}`,
        ),
      );
      setIsLoading(false);
      return;
    }

    console.log("[SignUp] Starting signup process for email:", email);

    try {
      // Basic signup call following Supabase best practices
      console.log("[SignUp] Calling supabase.auth.signUp...");
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      console.log("[SignUp] Supabase signup response:", {
        user: data.user?.id,
        session: !!data.session,
        error: error?.message,
      });

      if (error) {
        console.error("[SignUp] Supabase signup error:", error);
        setError(error);
        return;
      }

      if (data.user) {
        console.log("[SignUp] User signed up successfully:", data.user.id);

        // Check if user needs email confirmation
        if (!data.session) {
          console.log("[SignUp] Email confirmation required");
          setError({
            message: "Email confirmation required",
            userMessage:
              "Please check your email and click the confirmation link to complete your signup.",
            isConfirmationRequired: true,
          });
          return;
        }

        // If we have a session, redirect to create profile page
        console.log(
          "[SignUp] User has session, redirecting to new-profile page",
        );
        router.push("/new-profile");
      } else {
        console.error("[SignUp] No user returned from signup");
        setError("Signup failed - no user created");
      }
    } catch (error: any) {
      console.error("[SignUp] Unexpected signup error:", error);
      setError(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRetry = () => {
    setError(null);
  };

  const handleSignInInstead = () => {
    router.push("/auth/signin");
  };

  const handleContactSupport = () => {
    window.open(
      "mailto:<EMAIL>?subject=Signup Issue",
      "_blank",
    );
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-purple-waves bg-cover bg-center py-12">
      <div className="container flex flex-col items-center justify-center px-4 md:px-6">
        <div className="mb-8">
          <Image
            src="/crescender-logo.svg"
            alt="Crescender"
            width={250}
            height={70}
            style={{ width: "auto", height: "auto" }}
            priority
          />
        </div>
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center">
              Create an account
            </CardTitle>
            <CardDescription className="text-center">
              Enter your email and create a password to get started
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <div className="mb-4">
                <EnhancedErrorDisplay
                  error={error}
                  onRetry={handleRetry}
                  onSignInInstead={handleSignInInstead}
                  onContactSupport={handleContactSupport}
                />
              </div>
            )}
            <form onSubmit={handleSignUp} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  autoComplete="email"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value);
                    setShowPasswordHelp(e.target.value.length > 0);
                  }}
                  autoComplete="new-password"
                  required
                />
                {showPasswordHelp && (
                  <div className="mt-3">
                    <PasswordStrengthIndicator password={password} />
                  </div>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirm-password">Confirm Password</Label>
                <Input
                  id="confirm-password"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  autoComplete="new-password"
                  required
                />
                {confirmPassword.length > 0 && (
                  <div className="mt-2">
                    <div className="flex items-center space-x-2 text-sm">
                      {password === confirmPassword && password.length > 0 ? (
                        <>
                          <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                          <span className="text-green-700">
                            Passwords match
                          </span>
                        </>
                      ) : confirmPassword.length > 0 ? (
                        <>
                          <X className="h-4 w-4 text-red-500 flex-shrink-0" />
                          <span className="text-red-600">
                            Passwords do not match
                          </span>
                        </>
                      ) : null}
                    </div>
                  </div>
                )}
              </div>
              <Button
                type="submit"
                className="w-full"
                disabled={
                  isLoading ||
                  !validatePassword(password).isValid ||
                  password !== confirmPassword
                }
              >
                {isLoading ? "Creating account..." : "Create account"}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col">
            <div className="text-center text-sm">
              Already have an account?{" "}
              <Link
                href="/auth/signin"
                className="text-crescender-600 hover:text-crescender-700"
              >
                Sign in
              </Link>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
