"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, Calendar, Pencil, Trash2, MapPin } from "lucide-react";
import { useAppStore } from "@/lib/store";
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameDay,
} from "date-fns";
import { EventDetail } from "@/components/calendar/event-detail";

export type CalendarEvent = {
  id: string;
  created_at: string;
  updated_at: string;
  title: string;
  description?: string;
  start_date: string;
  end_date?: string;
  location_name?: string;
  location_address?: string;
  event_type: string;
  creator_profile_id: string;
};

export default function CalendarPage() {
  const authUser = useAppStore((state) => state.authUser);
  const currentProfile = useAppStore((state) => state.currentProfile);
  const storeIsLoading = useAppStore((state) => state.isLoading);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);

  // Year and month selector state
  const [selectedYear, setSelectedYear] = useState(selectedDate.getFullYear());
  const [selectedMonth, setSelectedMonth] = useState(selectedDate.getMonth());

  useEffect(() => {
    setSelectedDate(new Date(selectedYear, selectedMonth, 1));
  }, [selectedYear, selectedMonth]);

  useEffect(() => {
    const loadEvents = async () => {
      if (!currentProfile) return;
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from("events")
          .select("*")
          .eq("creator_profile_id", currentProfile.id)
          .gte("start_date", format(startOfMonth(selectedDate), "yyyy-MM-dd"))
          .lte("start_date", format(endOfMonth(selectedDate), "yyyy-MM-dd"))
          .order("start_date", { ascending: true });
        if (error) throw error;

        const rawEventsData = data || []; // Handle null case for data
        const transformedEvents: CalendarEvent[] = rawEventsData.map((event: any): CalendarEvent => ({
          id: String(event.id), // Ensure id is a string
          created_at: event.created_at || new Date().toISOString(),
          updated_at: event.updated_at || event.created_at || new Date().toISOString(),
          title: event.title || "Untitled Event",
          description: event.description || undefined,
          start_date: event.start_date || new Date().toISOString().split("T")[0],
          end_date: event.end_date || undefined,
          location_name: event.location_name || undefined,
          location_address: event.location_address || undefined,
          event_type: event.event_type || "other",
          creator_profile_id: String(event.creator_profile_id || currentProfile!.id), // currentProfile is checked not null
        }));
        setEvents(transformedEvents);
      } catch (error) {
        console.error("Failed to fetch events:", error);
      } finally {
        setIsLoading(false);
      }
    };
    loadEvents();
  }, [currentProfile, selectedDate]);

  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
    setSelectedYear(date.getFullYear());
    setSelectedMonth(date.getMonth());
    setSelectedEvent(null);
  };

  const handleSelectEvent = (event: CalendarEvent) => {
    setSelectedEvent(event);
  };

  const router = useRouter();

  const handleAddEvent = () => {
    router.push(`/calendar/events/new?date=${format(selectedDate, "yyyy-MM-dd")}`);
  };

  const handleEventUpdated = (updatedEvent: CalendarEvent) => {
    setEvents((prev) =>
      prev.map((event) =>
        event.id === updatedEvent.id ? updatedEvent : event,
      ),
    );
    setSelectedEvent(updatedEvent);
  };

  const handleEventDeleted = (eventId: string) => {
    setEvents((prev) => prev.filter((event) => event.id !== eventId));
    setSelectedEvent(null);
  };

  const daysInMonth = eachDayOfInterval({
    start: startOfMonth(selectedDate),
    end: endOfMonth(selectedDate),
  });

  // Generate year and month options
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 10 }, (_, i) => currentYear - 5 + i);
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  if (storeIsLoading || isLoading) {
    return (
      <div className="flex justify-center items-center h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!authUser || !currentProfile) {
    return (
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Events</h1>
        <p className="mt-4">Please log in to view your events.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Events</h1>
        <Button onClick={handleAddEvent}>
          <Plus className="mr-2 h-4 w-4" />
          Add Event
        </Button>
      </div>

      {/* Year and Month Selector */}
      <div className="flex items-center gap-4 mb-4">
        <label htmlFor="year-select" className="font-medium">
          Year:
        </label>
        <select
          id="year-select"
          value={selectedYear}
          onChange={(e) => setSelectedYear(Number(e.target.value))}
          className="border rounded px-2 py-1"
        >
          {years.map((year) => (
            <option key={year} value={year}>
              {year}
            </option>
          ))}
        </select>
        <label htmlFor="month-select" className="font-medium">
          Month:
        </label>
        <select
          id="month-select"
          value={selectedMonth}
          onChange={(e) => setSelectedMonth(Number(e.target.value))}
          className="border rounded px-2 py-1"
        >
          {months.map((month, idx) => (
            <option key={month} value={idx}>
              {month}
            </option>
          ))}
        </select>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div>
          <div className="border rounded-md p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold">
                {months[selectedMonth]} {selectedYear}
              </h2>
            </div>
            <div className="grid grid-cols-7 gap-1">
              {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
                <div
                  key={day}
                  className="text-center text-sm text-muted-foreground"
                >
                  {day}
                </div>
              ))}
              {daysInMonth.map((day) => {
                const dayEvents = events.filter((event) =>
                  isSameDay(new Date(event.start_date), day),
                );
                return (
                  <Button
                    key={day.toISOString()}
                    variant="ghost"
                    className={`w-full h-10 rounded-full relative ${
                      isSameDay(day, selectedDate)
                        ? "bg-accent text-accent-foreground"
                        : ""
                    }`}
                    onClick={() => handleDateChange(day)}
                  >
                    {day.getDate()}
                    {dayEvents.length > 0 && (
                      <span className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"></span>
                    )}
                  </Button>
                );
              })}
            </div>
          </div>

          <div className="mt-4">
            {/* Group events by date */}
            {Array.from(
              new Set(
                events.map((event) =>
                  format(new Date(event.start_date), "yyyy-MM-dd"),
                ),
              ),
            )
              .sort()
              .map((dateStr) => {
                const date = new Date(dateStr);
                const eventsOnDate = events.filter((event) =>
                  isSameDay(new Date(event.start_date), date),
                );

                return (
                  <div key={dateStr} className="mb-6">
                    <h3 className="font-medium mb-3 sticky top-0 bg-background py-2">
                      {format(date, "EEEE, MMMM d, yyyy")}
                    </h3>

                    {eventsOnDate.length === 0 ? (
                      <p className="text-sm text-muted-foreground">
                        No events scheduled for this day
                      </p>
                    ) : (
                      <div className="space-y-3">
                        {eventsOnDate.map((event) => {
                          // Calculate duration
                          const startDate = new Date(event.start_date);
                          const endDate = event.end_date
                            ? new Date(event.end_date)
                            : null;
                          const durationText = endDate
                            ? `${format(startDate, "h:mm a")} - ${format(endDate, "h:mm a")}`
                            : format(startDate, "h:mm a");

                          return (
                            <div
                              key={event.id}
                              className={`p-3 border rounded-md cursor-pointer hover:bg-accent/50 ${
                                selectedEvent?.id === event.id
                                  ? "bg-accent"
                                  : ""
                              }`}
                              onClick={() => handleSelectEvent(event)}
                            >
                              <div className="flex justify-between items-start">
                                <div>
                                  <p className="font-medium">{event.title}</p>
                                  <p className="text-xs text-muted-foreground">
                                    {durationText}
                                  </p>
                                  {event.location_name && (
                                    <p className="text-xs text-muted-foreground">
                                      {event.location_name}
                                      {event.location_address &&
                                        `, ${event.location_address.split(",")[0]}`}
                                    </p>
                                  )}
                                </div>
                                <div className="flex space-x-1">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-7 w-7"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // Handle edit - redirect to edit page
                                      router.push(`/calendar/edit/${event.id}`);
                                    }}
                                  >
                                    <Pencil className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-7 w-7 text-red-500"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // Handle delete
                                      if (
                                        confirm(
                                          "Are you sure you want to delete this event?",
                                        )
                                      ) {
                                        handleEventDeleted(event.id);
                                      }
                                    }}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                              <div className="flex mt-2 flex-wrap gap-1">
                                <Badge
                                  variant="outline"
                                  className="text-xs capitalize"
                                >
                                  {event.event_type}
                                </Badge>
                                {event.location_address && (
                                  <Badge variant="outline" className="text-xs">
                                    <MapPin className="h-3 w-3 mr-1" />
                                    {/* Calculate distance - placeholder for now */}
                                    2.5 mi
                                  </Badge>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
        </div>

        <div>
          {selectedEvent ? (
            <EventDetail
              event={selectedEvent}
              onUpdate={handleEventUpdated}
              onDelete={handleEventDeleted}
            />
          ) : (
            <div className="flex h-[400px] items-center justify-center rounded-lg border border-dashed p-8 text-center">
              <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center">
                <Calendar className="h-10 w-10 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-semibold">
                  No event selected
                </h3>
                <p className="mb-4 mt-2 text-sm text-muted-foreground">
                  Select a date to view events or add a new event.
                </p>
                <Button variant="outline" onClick={handleAddEvent}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Event
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
