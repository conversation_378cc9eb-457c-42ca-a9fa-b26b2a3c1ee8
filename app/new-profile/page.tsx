"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { supabase } from "@/lib/supabase";
import { useAppStore, type UserRoleType } from "@/lib/store";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import { createProfile } from "@/lib/queries/profileQueries";
import type { Tables } from "@/lib/supabase-types";

export const dynamic = "force-dynamic";

export default function NewProfilePage() {
  const router = useRouter();
  const { setCurrentProfile } = useAppStore();
  const [userId, setUserId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    username: "",
    full_name: "", // We'll use this for input but split it when submitting
    roles: [] as UserRoleType[],
    bio: "",
    // Remove avatar_url from initial state
  });

  // Add state for selected avatar
  const [selectedAvatar, setSelectedAvatar] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkUserAndProfile = async () => {
      const { data } = await supabase.auth.getUser();
      if (!data.user) {
        router.push("/auth/signin");
        return;
      }

      setUserId(data.user.id);

      // Check if user already has a profile
      const { data: existingProfile } = await supabase
        .from("profiles")
        .select("*")
        .eq("user_id", data.user.id)
        .eq("profile_type", "personal")
        .single();

      if (existingProfile) {
        console.log("[NewProfile] User already has a profile, pre-filling form");
        // Pre-fill the form with existing profile data
        setFormData({
          username: existingProfile.username,
          full_name: `${existingProfile.first_name || ""} ${existingProfile.last_name || ""}`.trim() || existingProfile.display_name || "",
          roles: existingProfile.roles || [],
          bio: existingProfile.bio || "",
        });

        // Extract avatar number if it exists
        if (existingProfile.avatar) {
          const avatarMatch = existingProfile.avatar.match(/avatar-(\d+)\.svg/);
          if (avatarMatch) {
            setSelectedAvatar(parseInt(avatarMatch[1]));
          }
        }
      }
    };

    checkUserAndProfile();
  }, [router]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleRoleToggle = (role: UserRoleType, checked: boolean) => {
    setFormData((prev) => {
      if (checked) {
        // Add the role if it's not already in the array
        return { ...prev, roles: [...prev.roles, role] };
      } else {
        // Remove the role if it's in the array
        return { ...prev, roles: prev.roles.filter((r) => r !== role) };
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Parse full name into first and last name
      let firstName = formData.full_name;
      let lastName = "";

      // If there's at least one space, split the name
      if (formData.full_name.includes(" ")) {
        // Count spaces to determine how to split
        const spaceCount = (formData.full_name.match(/ /g) || []).length;

        if (spaceCount === 1) {
          // Simple case: one space, split into first and last
          [firstName, lastName] = formData.full_name.split(" ");
        } else {
          // Multiple spaces, assume first name is before the first space
          // and last name is everything after
          const firstSpaceIndex = formData.full_name.indexOf(" ");
          firstName = formData.full_name.substring(0, firstSpaceIndex);
          lastName = formData.full_name.substring(firstSpaceIndex + 1);
        }
      }

      // Check if profile already exists and update instead of create
      const { data: existingProfile } = await supabase
        .from("profiles")
        .select("*")
        .eq("user_id", userId)
        .eq("profile_type", "personal")
        .single();

      let profile;

      if (existingProfile) {
        // Update existing profile
        const updateData = {
          username: formData.username,
          first_name: firstName,
          last_name: lastName,
          display_name: formData.full_name,
          roles: formData.roles,
          bio: formData.bio,
          // Only include avatar if one was selected
          ...(selectedAvatar !== null && {
            avatar: `/avatars/avatar-${selectedAvatar}.svg`,
          }),
          updated_at: new Date().toISOString(),
        };

        const { data: updatedProfile, error: updateError } = await supabase
          .from("profiles")
          .update(updateData)
          .eq("id", existingProfile.id)
          .select()
          .single();

        if (updateError) throw updateError;
        profile = updatedProfile;
      } else {
        // Create new profile
        const profileData = {
          id: userId,
          username: formData.username,
          first_name: firstName,
          last_name: lastName,
          display_name: formData.full_name,
          profile_type: "personal",
          user_id: userId,
          roles: formData.roles,
          bio: formData.bio,
          // Only include avatar if one was selected
          ...(selectedAvatar !== null && {
            avatar: `/avatars/avatar-${selectedAvatar}.svg`,
          }),
        };

        profile = await createProfile(profileData);
      }

      setCurrentProfile(profile as Tables<"profiles">);
      router.push("/");
    } catch (error: any) {
      setError(error.message || "Failed to create profile");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-purple-waves bg-cover bg-center py-12">
      <div className="container flex flex-col items-center justify-center px-4 md:px-6">
        <div className="mb-8">
          <Image
            src="/crescender-logo.svg"
            alt="Crescender"
            width={250}
            height={70}
            style={{ width: "auto", height: "auto" }}
            priority
          />
        </div>
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center">
              Complete your profile
            </CardTitle>
            <CardDescription className="text-center">
              Tell us a bit about yourself to personalize your experience
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  name="username"
                  placeholder="johndoe"
                  value={formData.username}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="full_name">Full Name</Label>
                <Input
                  id="full_name"
                  name="full_name"
                  placeholder="John Doe"
                  value={formData.full_name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label>I am a... (select all that apply)</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="performer"
                      checked={formData.roles.includes("performer")}
                      onCheckedChange={(checked) =>
                        handleRoleToggle("performer", checked as boolean)
                      }
                    />
                    <Label htmlFor="performer" className="cursor-pointer">
                      Performer
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="teacher"
                      checked={formData.roles.includes("teacher")}
                      onCheckedChange={(checked) =>
                        handleRoleToggle("teacher", checked as boolean)
                      }
                    />
                    <Label htmlFor="teacher" className="cursor-pointer">
                      Teacher
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="student"
                      checked={formData.roles.includes("student")}
                      onCheckedChange={(checked) =>
                        handleRoleToggle("student", checked as boolean)
                      }
                    />
                    <Label htmlFor="student" className="cursor-pointer">
                      Student
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="parent"
                      checked={formData.roles.includes("parent")}
                      onCheckedChange={(checked) =>
                        handleRoleToggle("parent", checked as boolean)
                      }
                    />
                    <Label htmlFor="parent" className="cursor-pointer">
                      Parent
                    </Label>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  name="bio"
                  placeholder="Tell us a bit about yourself..."
                  value={formData.bio}
                  onChange={handleChange}
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label>Choose an avatar (optional)</Label>
                <div className="grid grid-cols-4 gap-4">
                  {[
                    { id: 1, name: "Drum" },
                    { id: 2, name: "Guitar" },
                    { id: 3, name: "Music Note" },
                    { id: 4, name: "Upload" },
                  ].map((avatar) => (
                    <div
                      key={avatar.id}
                      className={`cursor-pointer rounded-lg p-2 text-center ${selectedAvatar === avatar.id ? "ring-2 ring-crescender-600 bg-crescender-100" : "hover:bg-gray-100"}`}
                      onClick={() => setSelectedAvatar(avatar.id)}
                    >
                      <Image
                        src={`/avatars/avatar-${avatar.id}.svg`}
                        alt={`${avatar.name} Avatar`}
                        width={60}
                        height={60}
                        className="rounded-full mx-auto mb-1"
                      />
                      <span className="text-xs text-gray-600">
                        {avatar.name}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Saving profile..." : "Save profile"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
