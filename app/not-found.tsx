"use client";

import Link from "next/link";
import Image from "next/image";

export default function NotFound() {
  return (
    <div className="relative flex flex-col items-center justify-center min-h-screen text-white overflow-hidden">
      <Image
        src="/images/purple-waves.png"
        alt="Purple waves background"
        fill
        style={{ objectFit: 'cover' }}
        quality={100}
        className="-z-10 border-0"
        priority
      />
      <div className="z-10 p-8 bg-black bg-opacity-60 rounded-lg shadow-xl text-center max-w-lg mx-auto">
        <h1
          className="text-6xl font-bold mb-4"
          style={{ color: "hsl(var(--foreground))" }}
        >
          404
        </h1>
        <p
          className="text-2xl mb-8"
          style={{ color: "hsl(var(--foreground))" }}
        >
          Oops! Page Not Found.
        </p>
        <p
          className="text-lg mb-8"
          style={{ color: "hsl(var(--muted-foreground))" }}
        >
          The page you are looking for might have been removed, had its name
          changed, or is temporarily unavailable.
        </p>
        <div className="space-y-4">
          <Link href="/" legacyBehavior passHref>
            <a
              className="block w-full px-6 py-3 text-lg font-medium rounded-md transition duration-300 text-center"
              style={{
                backgroundColor: "hsl(var(--primary))",
                color: "hsl(var(--primary-foreground))",
              }}
              onMouseOver={(e) => (e.currentTarget.style.opacity = "0.9")}
              onMouseOut={(e) => (e.currentTarget.style.opacity = "1")}
            >
              Go to Homepage
            </a>
          </Link>
          <Link href="/settings" legacyBehavior passHref>
            <a
              className="block w-full px-6 py-3 text-lg font-medium rounded-md transition duration-300 text-center"
              style={{
                backgroundColor: "hsl(var(--secondary))",
                color: "hsl(var(--secondary-foreground))",
              }}
              onMouseOver={(e) => (e.currentTarget.style.opacity = "0.9")}
              onMouseOut={(e) => (e.currentTarget.style.opacity = "1")}
            >
              Your Account Settings
            </a>
          </Link>
          <Link href="/dashboard" legacyBehavior passHref>
            <a
              className="block w-full px-6 py-3 text-lg font-medium rounded-md transition duration-300 text-center"
              style={{
                backgroundColor: "hsl(var(--accent))",
                color: "hsl(var(--accent-foreground))",
              }}
              onMouseOver={(e) => (e.currentTarget.style.opacity = "0.9")}
              onMouseOut={(e) => (e.currentTarget.style.opacity = "1")}
            >
              Check Dashboard
            </a>
          </Link>
        </div>
      </div>
    </div>
  );
}
