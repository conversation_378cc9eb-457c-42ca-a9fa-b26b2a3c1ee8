"use client";

import Link from "next/link";
import Image from "next/image";

export default function NotFound() {
  return (
    <div className="relative flex flex-col items-center justify-center min-h-screen overflow-hidden bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700">
      <Image
        src="/images/purple-waves.png"
        alt="Purple waves background"
        fill
        style={{ objectFit: 'cover' }}
        quality={100}
        className="-z-10 opacity-30"
        priority
      />
      <div className="z-10 p-8 bg-white/95 backdrop-blur-sm rounded-xl shadow-2xl text-center max-w-lg mx-auto border-0">
        <h1 className="text-6xl font-bold mb-4 text-gray-900">
          404
        </h1>
        <p className="text-2xl mb-8 text-gray-800 font-semibold">
          Oops! Page Not Found.
        </p>
        <p className="text-lg mb-8 text-gray-600 leading-relaxed">
          The page you are looking for might have been removed, had its name
          changed, or is temporarily unavailable.
        </p>
        <div className="space-y-4">
          <Link
            href="/"
            className="block w-full px-6 py-3 text-lg font-medium rounded-lg transition-all duration-300 text-center bg-purple-600 hover:bg-purple-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105 border-0"
          >
            Go to Homepage
          </Link>
          <Link
            href="/settings"
            className="block w-full px-6 py-3 text-lg font-medium rounded-lg transition-all duration-300 text-center bg-gray-100 hover:bg-gray-200 text-gray-800 shadow-md hover:shadow-lg transform hover:scale-105 border-0"
          >
            Your Account Settings
          </Link>
          <Link
            href="/dashboard"
            className="block w-full px-6 py-3 text-lg font-medium rounded-lg transition-all duration-300 text-center bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105 border-0"
          >
            Check Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
}
