"use client";

import type React from "react";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { X, Calendar, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

interface AddChildFormProps {
  parentId: string;
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
}

export function AddChildForm({
  parentId,
  onSuccess,
  onCancel,
}: AddChildFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    username: "",
    dob: "",
    avatar: "",
    bio: "",
  });
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Validate date of birth
    if (!date) {
      toast({
        title: "Error",
        description: "Please select a date of birth.",
        variant: "destructive",
      });
      setIsLoading(false);
      return;
    }

    try {
      // Get the current session token
      const supabase = createClientComponentClient();
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session) {
        toast({
          title: "Authentication Required",
          description: "Your session has expired. Please log in again.",
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }

      // Call our API endpoint
      const response = await fetch("/api/parent/add-child", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          // No need to send Authorization header, the cookie will be sent automatically
        },
        body: JSON.stringify({
          name: formData.name,
          dob: date.toISOString(),
          username: formData.username,
          bio: formData.bio,
          avatar: formData.avatar,
        }),
        credentials: "include", // Include cookies in the request
      });

      // Check if the response is OK
      if (!response.ok) {
        const errorText = await response.text();
        console.error("API error:", response.status, errorText);
        throw new Error(`API error: ${response.status} ${errorText}`);
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Child profile created",
          description: "The child profile has been created successfully.",
        });

        // Fetch the newly created profile
        const profileResponse = await fetch(`/api/profiles/${data.childId}`, {
          credentials: "include", // Include cookies in the request
        });

        if (!profileResponse.ok) {
          console.warn(
            "Could not fetch profile, but child was created successfully",
          );
          if (onSuccess) onSuccess({ id: data.childId, name: formData.name });
          return;
        }

        const profileData = await profileResponse.json();

        if (onSuccess) onSuccess(profileData);
      } else {
        throw new Error(data.error || "Failed to create child profile");
      }
    } catch (error) {
      console.error("Error creating child profile:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to create child profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Child's Name</Label>
        <Input
          id="name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="dob">Date of Birth</Label>
        <div className="flex gap-2">
          <Input
            type="date"
            id="dob"
            name="dob"
            value={date ? format(date, "yyyy-MM-dd") : ""}
            max={format(new Date(), "yyyy-MM-dd")}
            onChange={(e) => {
              if (e.target.value) {
                setDate(new Date(e.target.value));
              } else {
                setDate(undefined);
              }
            }}
            className="flex-1"
          />
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="icon">
                <Calendar className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <CalendarComponent
                value={date}
                onChange={setDate}
                disabled={(date) =>
                  date > new Date() || date < new Date("1900-01-01")
                }
                minDate={new Date("1900-01-01")}
                maxDate={new Date()}
                showNavigation={true}
                showNeighboringMonth={true}
                view="month"
                className="border-0"
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="username">Username (Optional)</Label>
        <Input
          id="username"
          name="username"
          value={formData.username}
          onChange={handleChange}
        />
        <p className="text-xs text-muted-foreground">
          If left blank, a username will be generated automatically.
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="avatar">Avatar URL (Optional)</Label>
        <Input
          id="avatar"
          name="avatar"
          value={formData.avatar}
          onChange={handleChange}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="bio">Bio (Optional)</Label>
        <Textarea
          id="bio"
          name="bio"
          value={formData.bio}
          onChange={handleChange}
          placeholder="Add a bio for your child..."
          rows={3}
        />
      </div>

      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            "Add Child"
          )}
        </Button>
      </div>
    </form>
  );
}
