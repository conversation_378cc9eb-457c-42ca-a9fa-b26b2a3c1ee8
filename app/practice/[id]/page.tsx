"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import Image from "next/image";
import { useAppStore } from "@/lib/store";

// Force dynamic rendering to prevent static generation issues
export const dynamic = 'force-dynamic';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { SongSelector } from "@/components/practice/song-selector";
import { useToast } from "@/hooks/use-toast";
import { fetchSongs } from "@/lib/queries/songQueries";
import {
  ArrowLeft,
  Edit,
  Save,
  X,
  Clock,
  Calendar,
  Music,
  Star,
  Loader2,
} from "lucide-react";
import { format } from "date-fns";
import { Song } from "@/lib/queries/songQueries";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

interface PracticeSession {
  id: string;
  duration: number;
  notes?: string;
  practiced_at: string;
  created_at: string;
  songs?: Array<{
    id?: string | number;
    title: string;
    artist?: string;
    album?: string;
    album_cover_url?: string;
  }>;
  metadata?: {
    self_rating?: number;
    focus_rating?: number;
    mood?: string;
    skills?: string[];
    [key: string]: any;
  };
}

export default function PracticeSessionPage() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const authUser = useAppStore((state) => state.authUser);
  const supabase = createClientComponentClient();

  const [session, setSession] = useState<PracticeSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Edit state
  const [editNotes, setEditNotes] = useState("");
  const [editSkills, setEditSkills] = useState<string[]>([]);
  const [userSongs, setUserSongs] = useState<Song[]>([]);
  const [songsLoading, setSongsLoading] = useState(false);
  const [showSongSelector, setShowSongSelector] = useState(false);
  const [selectedSongIds, setSelectedSongIds] = useState<(string | number)[]>(
    [],
  );

  // Load practice session
  useEffect(() => {
    const loadSession = async () => {
      if (!params.id || !authUser?.id) return;

      try {
        setIsLoading(true);
        const response = await fetch(`/api/practice/${params.id}`);

        if (!response.ok) {
          throw new Error("Failed to load practice session");
        }

        const data = await response.json();
        setSession(data);

        // Initialize edit state
        setEditNotes(data.notes || "");
        setEditSkills(data.metadata?.skills || []);
        setSelectedSongIds(
          (data.songs || []).map((s: any) => s.id).filter(Boolean),
        );
      } catch (error) {
        console.error("Error loading practice session:", error);
        toast({
          title: "Error",
          description: "Failed to load practice session",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadSession();
  }, [params.id, authUser?.id, toast]);

  // Load user songs when editing
  useEffect(() => {
    const loadSongs = async () => {
      if (!isEditing || !authUser?.id) return;

      try {
        setSongsLoading(true);
        const userSongsData = await fetchSongs(supabase);
        setUserSongs(userSongsData);
      } catch (error) {
        console.error("Error loading songs:", error);
      } finally {
        setSongsLoading(false);
      }
    };

    loadSongs();
  }, [isEditing, authUser?.id, supabase]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    if (!session) return;

    // Reset edit state
    setEditNotes(session.notes || "");
    setEditSkills(session.metadata?.skills || []);
    setSelectedSongIds(
      (session.songs || []).map((s: any) => s.id).filter(Boolean),
    );
    setShowSongSelector(false);
    setIsEditing(false);
  };

  const handleSave = async () => {
    if (!session || !authUser?.id) return;

    try {
      setIsSaving(true);

      const songsToSave = userSongs
        .filter(song => selectedSongIds.includes(song.id))
        .map(song => ({
          id: song.id,
          title: song.title,
          artist: song.primary_artist_name,
          album: song.album_name,
          album_cover_url: song.album_cover_url,
        }));

      const updateData = {
        notes: editNotes || null,
        metadata: {
          ...session.metadata,
          skills: editSkills,
          songs: songsToSave,
        },
      };

      const response = await fetch(`/api/practice/${session.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error("Failed to update practice session");
      }

      const updatedSession = await response.json();
      setSession(updatedSession);
      setIsEditing(false);

      toast({
        title: "Success",
        description: "Practice session updated successfully",
      });
    } catch (error) {
      console.error("Error updating practice session:", error);
      toast({
        title: "Error",
        description: "Failed to update practice session",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const addSkill = (skill: string) => {
    if (skill.trim() && !editSkills.includes(skill.trim())) {
      setEditSkills([...editSkills, skill.trim()]);
    }
  };

  const removeSkill = (index: number) => {
    setEditSkills(editSkills.filter((_, i) => i !== index));
  };

  const removeSong = (songIdToRemove: string | number) => {
    setSelectedSongIds(prevIds => prevIds.filter((id) => id !== songIdToRemove));
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 max-w-4xl">
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading practice session...</span>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="container mx-auto py-6 max-w-4xl">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">
            Practice session not found
          </h1>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">Practice Session</h1>
        </div>

        {!isEditing ? (
          <Button onClick={handleEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={isSaving}>
              {isSaving ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save
            </Button>
          </div>
        )}
      </div>

      <div className="space-y-6">
        {/* Session Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Session Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{session.duration} minutes</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>
                  {format(new Date(session.practiced_at), "dd MMM yyyy")}
                </span>
              </div>
              {session.metadata?.self_rating && (
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4 text-muted-foreground" />
                  <span>{session.metadata.self_rating}/5 rating</span>
                </div>
              )}
            </div>

            {session.metadata?.mood && (
              <div>
                <span className="text-sm font-medium">Mood: </span>
                <Badge variant="secondary">{session.metadata.mood}</Badge>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Songs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Music className="h-5 w-5" />
              Songs Practiced
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!isEditing ? (
              <div>
                {session.songs && session.songs.length > 0 ? (
                  <div className="space-y-2">
                    {session.songs.map((song, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-3 p-3 border rounded-lg"
                      >
                        {song.album_cover_url && (
                          <Image
                            src={song.album_cover_url}
                            alt={song.title}
                            width={48}
                            height={48}
                            className="w-12 h-12 rounded object-cover"
                          />
                        )}
                        <div>
                          <div className="font-medium">{song.title}</div>
                          {song.artist && (
                            <div className="text-sm text-muted-foreground">
                              {song.artist}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">No songs recorded</p>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {/* Song selector button */}
                <Button
                  type="button"
                  variant="outline"
                  className="w-full justify-between"
                  onClick={() => setShowSongSelector(!showSongSelector)}
                >
                  <span className="flex items-center gap-2">
                    <Music className="h-4 w-4" />
                    {selectedSongIds.length > 0
                      ? `${selectedSongIds.length} song${selectedSongIds.length > 1 ? "s" : ""} selected`
                      : "Select songs from your library"}
                  </span>
                </Button>

                {/* Selected songs display */}
                {selectedSongIds.length > 0 && (
                  <div className="space-y-2">
                    {selectedSongIds.map((songId) => (
                      <div
                        key={songId}
                        className="flex items-center gap-3 p-3 border rounded-lg"
                      >
                        <Image
                          src={userSongs.find(song => song.id === songId)?.album_cover_url || "/placeholder.png"}
                          alt={userSongs.find(song => song.id === songId)?.title || "Song cover"}
                          width={48}
                          height={48}
                          className="w-12 h-12 rounded object-cover"
                        />
                        <div className="flex-1">
                          <div className="font-medium">{userSongs.find(song => song.id === songId)?.title}</div>
                          {userSongs.find(song => song.id === songId)?.primary_artist_name && (
                            <div className="text-sm text-muted-foreground">
                              {userSongs.find(song => song.id === songId)?.primary_artist_name}
                            </div>
                          )}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeSong(songId)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}

                {/* Collapsible song selector */}
                {showSongSelector && (
                  <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                    <SongSelector
                      songs={userSongs.map(s => ({
                        id: s.id,
                        title: s.title,
                        primary_artist_name: s.primary_artist_name,
                        album_cover_url: s.album_cover_url,
                      }))}
                      isLoading={songsLoading}
                      selectedSongIds={selectedSongIds}
                      setSelectedSongIds={setSelectedSongIds}
                      showSongParts={false}
                      multiSelect={true}
                      selectedSongId={undefined}
                      setSelectedSongId={() => {}}
                      selectedSongParts={undefined}
                      setSelectedSongParts={() => {}}
                      setSongs={() => {}}
                    />
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Skills */}
        <Card>
          <CardHeader>
            <CardTitle>Skills & Techniques</CardTitle>
          </CardHeader>
          <CardContent>
            {!isEditing ? (
              <div>
                {session.metadata?.skills &&
                session.metadata.skills.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {session.metadata.skills.map((skill, index) => (
                      <Badge key={index} variant="secondary">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">No skills recorded</p>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  {editSkills.map((skill, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {skill}
                      <X
                        className="h-3 w-3 cursor-pointer hover:text-red-500"
                        onClick={() => removeSkill(index)}
                      />
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="Add a skill or technique..."
                    className="flex-1 px-3 py-2 border rounded-md"
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        addSkill(e.currentTarget.value);
                        e.currentTarget.value = "";
                      }
                    }}
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Notes */}
        <Card>
          <CardHeader>
            <CardTitle>Practice Notes</CardTitle>
          </CardHeader>
          <CardContent>
            {!isEditing ? (
              <div>
                {session.notes ? (
                  <p className="whitespace-pre-line">{session.notes}</p>
                ) : (
                  <p className="text-muted-foreground">No notes recorded</p>
                )}
              </div>
            ) : (
              <Textarea
                placeholder="What went well? What was challenging? Any breakthroughs?"
                value={editNotes}
                onChange={(e) => setEditNotes(e.target.value)}
                className="resize-none"
                rows={6}
              />
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
