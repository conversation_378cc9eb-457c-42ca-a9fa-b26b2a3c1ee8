"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAppStore } from "@/lib/store";
import { KidsPracticeRecorder } from "@/components/practice/kids-practice-recorder";
import { Loader2 } from "lucide-react";

// Force dynamic rendering to prevent static generation issues
export const dynamic = 'force-dynamic';

export default function KidsPracticePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const authUser = useAppStore((state) => state.authUser);
  const children = useAppStore((state) => state.children) || [];
  const isLoading = useAppStore((state) => state.isLoading);

  // Get the child parameter from URL
  const preSelectedChildUsername = searchParams.get("child");

  const handleSuccess = () => {
    router.push("/children");
  };

  const handleCancel = () => {
    router.push("/children");
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 max-w-4xl">
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading...</span>
        </div>
      </div>
    );
  }

  if (!authUser) {
    return (
      <div className="container mx-auto py-6 max-w-4xl">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Please log in</h1>
          <p>You need to be logged in to record practice sessions.</p>
        </div>
      </div>
    );
  }

  if (children.length === 0) {
    return (
      <div className="container mx-auto py-6 max-w-4xl">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">No children found</h1>
          <p>Please add a child first before recording practice sessions.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <KidsPracticeRecorder
        childrenList={children}
        preSelectedChildUsername={preSelectedChildUsername || undefined}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}
