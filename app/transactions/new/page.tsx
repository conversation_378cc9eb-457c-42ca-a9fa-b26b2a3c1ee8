"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAppStore } from "@/lib/store";

// Force dynamic rendering to prevent static generation issues
export const dynamic = 'force-dynamic';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { SimplifiedTransactionForm } from "@/components/finances/simplified-transaction-form";

export default function AddTransactionPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { currentProfile } = useAppStore();
  const [initialData, setInitialData] = useState<any>(null);

  // Check if we have a returnTo parameter (for when returning from gear/event creation)
  const returnTo = searchParams.get("returnTo");

  useEffect(() => {
    // Check if we have saved form data in localStorage
    const savedFormData = localStorage.getItem("pendingTransactionForm");
    if (savedFormData) {
      try {
        const parsedData = JSON.parse(savedFormData);
        setInitialData(parsedData);
        // Clear the saved data
        localStorage.removeItem("pendingTransactionForm");
      } catch (error) {
        console.error("Error parsing saved form data:", error);
      }
    }
  }, []);

  if (!currentProfile) {
    return (
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold mb-6">Add New Transaction</h1>
        <p className="text-muted-foreground">
          Please log in to add a transaction.
        </p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Add New Transaction</h1>

      <Card>
        <CardHeader>
          <CardTitle>Transaction Details</CardTitle>
          <CardDescription>
            Record a new financial transaction related to your music activities.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SimplifiedTransactionForm
            initialData={initialData}
            onSuccess={() => router.push("/transactions")}
            onCancel={() => router.back()}
          />
        </CardContent>
      </Card>
    </div>
  );
}
