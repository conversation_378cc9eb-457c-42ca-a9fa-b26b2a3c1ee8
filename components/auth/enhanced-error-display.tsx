"use client";

import React from "react";
import {
  <PERSON>ert<PERSON><PERSON>gle,
  AlertCircle,
  Info,
  RefreshCw,
  ExternalLink,
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  analyzeAuthError,
  formatErrorForUser,
} from "@/lib/utils/auth-error-handler";

interface EnhancedErrorDisplayProps {
  error: any;
  onRetry?: () => void;
  onSignInInstead?: () => void;
  onContactSupport?: () => void;
  className?: string;
}

export function EnhancedErrorDisplay({
  error,
  onRetry,
  onSignInInstead,
  onContactSupport,
  className = "",
}: EnhancedErrorDisplayProps) {
  if (!error) return null;

  const analysis = analyzeAuthError(error);
  const formatted = formatErrorForUser(analysis);

  // Special case for email confirmation required
  if (analysis.error.code === "EMAIL_CONFIRMATION_REQUIRED") {
    return <EmailConfirmationDisplay onSignInInstead={onSignInInstead} />;
  }

  const getIcon = () => {
    switch (formatted.type) {
      case "warning":
        return <AlertTriangle className="h-4 w-4" />;
      case "info":
        return <Info className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getVariant = () => {
    switch (formatted.type) {
      case "warning":
        return "default";
      case "info":
        return "default";
      default:
        return "destructive";
    }
  };

  return (
    <Alert variant={getVariant()} className={className}>
      {getIcon()}
      <AlertTitle>{formatted.title}</AlertTitle>
      <AlertDescription className="space-y-3">
        <p>{formatted.message}</p>

        {/* Suggestions */}
        {formatted.actions.length > 0 && (
          <div className="space-y-2">
            <p className="font-medium text-sm">What you can do:</p>
            <ul className="text-sm space-y-1 ml-4">
              {formatted.actions.map((action, index) => (
                <li key={index} className="list-disc">
                  {action}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2 pt-2">
          {analysis.canRetry && onRetry && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-3 w-3" />
              Try Again
            </Button>
          )}

          {analysis.showFallback && onSignInInstead && (
            <Button
              variant="outline"
              size="sm"
              onClick={onSignInInstead}
              className="flex items-center gap-2"
            >
              Sign In Instead
            </Button>
          )}

          {analysis.error.type === "server" && onContactSupport && (
            <Button
              variant="outline"
              size="sm"
              onClick={onContactSupport}
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-3 w-3" />
              Contact Support
            </Button>
          )}
        </div>

        {/* Technical Details (for debugging) */}
        {process.env.NODE_ENV === "development" && (
          <details className="mt-3">
            <summary className="text-xs text-gray-500 cursor-pointer">
              Technical Details (Development Only)
            </summary>
            <div className="mt-2 p-2 bg-gray-100 rounded text-xs font-mono">
              <p>
                <strong>Error Type:</strong> {analysis.error.type}
              </p>
              <p>
                <strong>Error Code:</strong> {analysis.error.code || "N/A"}
              </p>
              <p>
                <strong>Raw Message:</strong> {analysis.error.message}
              </p>
              <p>
                <strong>Retryable:</strong> {analysis.canRetry ? "Yes" : "No"}
              </p>
              <p>
                <strong>Show Fallback:</strong>{" "}
                {analysis.showFallback ? "Yes" : "No"}
              </p>
            </div>
          </details>
        )}
      </AlertDescription>
    </Alert>
  );
}

// Specialized component for email confirmation success
export function EmailConfirmationDisplay({
  onSignInInstead,
}: {
  onSignInInstead?: () => void;
}) {
  return (
    <Alert variant="default" className="border-green-200 bg-green-50">
      <Info className="h-4 w-4 text-green-600" />
      <AlertTitle className="text-green-800">
        Account Created Successfully!
      </AlertTitle>
      <AlertDescription className="text-green-700 space-y-3">
        <p>
          Your account has been created and a confirmation email has been sent to your inbox.
        </p>

        <div className="space-y-2">
          <p className="font-medium text-sm">Next steps:</p>
          <ul className="text-sm space-y-1 ml-4">
            <li className="list-disc">
              Check your email inbox (including spam folder)
            </li>
            <li className="list-disc">Click the confirmation link in the email</li>
            <li className="list-disc">You can then sign in normally</li>
          </ul>
        </div>

        {onSignInInstead && (
          <div className="pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onSignInInstead}
              className="flex items-center gap-2"
            >
              Go to Sign In
            </Button>
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
}

// Specialized component for SMTP configuration errors
export function SMTPErrorDisplay({
  onContactSupport,
}: {
  onContactSupport?: () => void;
}) {
  return (
    <Alert variant="default" className="border-yellow-200 bg-yellow-50">
      <AlertTriangle className="h-4 w-4 text-yellow-600" />
      <AlertTitle className="text-yellow-800">
        Email Service Temporarily Unavailable
      </AlertTitle>
      <AlertDescription className="text-yellow-700 space-y-3">
        <p>
          Our email confirmation service is experiencing technical difficulties.
          This is not an issue with your account details.
        </p>

        <div className="space-y-2">
          <p className="font-medium text-sm">What's happening:</p>
          <ul className="text-sm space-y-1 ml-4">
            <li className="list-disc">
              Our email server configuration needs attention
            </li>
            <li className="list-disc">Your signup information is correct</li>
            <li className="list-disc">We're working to resolve this quickly</li>
          </ul>
        </div>

        <div className="space-y-2">
          <p className="font-medium text-sm">What you can do:</p>
          <ul className="text-sm space-y-1 ml-4">
            <li className="list-disc">Try again in 5-10 minutes</li>
            <li className="list-disc">
              Your account may have been created - try signing in
            </li>
            <li className="list-disc">
              Contact support if you need immediate assistance
            </li>
          </ul>
        </div>

        {onContactSupport && (
          <div className="pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onContactSupport}
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-3 w-3" />
              Contact Support
            </Button>
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
}
