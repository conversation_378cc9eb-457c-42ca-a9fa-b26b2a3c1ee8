"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useAppStore } from "@/lib/store";
import { supabase } from "@/lib/supabase";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2, ChevronDown, ChevronUp } from "lucide-react";
import { format } from "date-fns";
import { calculateEndTime, dateToPostgresTimestamp } from "@/lib/date-utils";
import {
  EVENT_CONSTANTS,
  EventConstants,
} from "@/components/calendar/event-constants";
import { v4 as uuidv4 } from "uuid";

// Import our form tab components
import { EventFormSummaryTab } from "@/components/calendar/event-form-tabs/summary-tab";
import { EventFormMoneyTab } from "@/components/calendar/event-form-tabs/money-tab";
import { EventFormGearTab } from "@/components/calendar/event-form-tabs/gear-tab";
import { EventFormPeopleTab } from "@/components/calendar/event-form-tabs/people-tab";
import { EventFormMusicTab } from "@/components/calendar/event-form-tabs/music-tab";
import { EventFormWrapUpTab } from "@/components/calendar/event-form-tabs/wrap-up-tab";
import { EventFormTravelTab } from "@/components/calendar/event-form-tabs/travel-tab";

interface EventFormProps {
  event?: any;
  isEditing?: boolean;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function EventForm({
  event,
  isEditing = false,
  onSuccess,
  onCancel,
}: EventFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { currentProfile } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [activeTab, setActiveTab] = useState("summary");

  // Initialize form data with default values or existing event data
  const [formData, setFormData] = useState<any>({
    title: event?.title || "",
    description: event?.description || "",
    event_type: event?.event_type || "rehearsal",
    start_date: event?.start_date ? new Date(event.start_date) : new Date(),
    end_date: event?.end_date
      ? new Date(event.end_date)
      : new Date(new Date().setHours(new Date().getHours() + 1)),
    duration: event?.duration || "60", // Default to 1 hour
    all_day: event?.all_day || false,

    // Location details
    location_name: event?.location_name || "",
    location_address: event?.location_address || "",
    location_city: event?.location_city || "",
    location_state: event?.location_state || "",
    location_zip: event?.location_zip || "",
    location_country: event?.location_country || "",

    // Venue details
    venue_id: event?.venue_id || null,

    // Status
    status: event?.status || "pending",
    is_paid: event?.is_paid || false,
    is_confirmed: event?.is_confirmed || false,

    // Event constants fields
    summary: event?.summary || EVENT_CONSTANTS.summary,
    money: event?.money || {
      transactions: [],
      budget: 0,
    },
    gear: event?.gear || EVENT_CONSTANTS.gear,
    people: event?.people || EVENT_CONSTANTS.people,
    music: event?.music || EVENT_CONSTANTS.music,
    wrap_up: event?.wrap_up || EVENT_CONSTANTS.wrapUp,
    travel: event?.travel || EVENT_CONSTANTS.travel,

    // Notes
    notes: event?.notes || "",
  });

  const handleChange = (field: string, value: any) => {
    setFormData((prev: any) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentProfile) {
      toast({
        title: "Error",
        description: "You must be logged in to add an event.",
        variant: "destructive",
      });
      return;
    }

    if (!formData.title || !formData.start_date) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // Calculate end_date based on start_date and duration using our utility function
      if (formData.duration) {
        formData.end_date = calculateEndTime(
          formData.start_date,
          formData.duration,
        );
      }

      // Format the data for submission
      // Remove duration from the data sent to the database - it's only used client-side
      const { duration, ...dataWithoutDuration } = formData;

      // Extract transaction IDs for the money field
      const transactionIds =
        formData.money?.transactions?.map((t: any) => t.id) || [];

      // Get the dates from form data
      const startDate = formData.start_date;
      const endDate = formData.end_date;

      const eventData = {
        ...dataWithoutDuration,
        creator_profile_id: currentProfile.id,
        // Use our utility function to format dates with Australian timezone
        start_date: dateToPostgresTimestamp(startDate),
        end_date: endDate ? dateToPostgresTimestamp(endDate) : null,
        // Store money as JSONB with transaction references
        money: {
          transactionIds,
          budget: formData.money.budget || 0,
        },
        // Store music as JSONB
        music: {
          type: formData.music.type || "setlist",
          setlist: formData.music.setlist || [],
          lessonPlan: formData.music.lessonPlan || {
            songs: [],
            teacherNotes: "",
          },
        },
        // Ensure gear data is properly formatted
        gear: {
          ...formData.gear,
          // Make sure we're storing the gear items with their proper references
          selectedGear: formData.gear.selectedGear.map((item: any) => ({
            id: item.id,
            name: item.name,
            quantity: item.quantity,
            notes: item.notes,
            globalGearId: item.globalGearId, // This is the UUID reference to the gear table
          })),
        },
      };

      console.log("Submitting event with gear:", eventData.gear);
      console.log("Submitting event with money:", eventData.money);
      console.log("Submitting event with music:", eventData.music);
      console.log("Date/time fields with Australian timezone:", {
        start_date: eventData.start_date,
        end_date: eventData.end_date,
      });
      console.log("Event data for RLS check:", {
        creator_profile_id: eventData.creator_profile_id,
        currentProfile: currentProfile,
        authUser: currentProfile ? "exists" : "missing"
      });

      // Save or update the event first
      let eventId: number | null = event?.id ? Number(event.id) : null;

      if (isEditing && event?.id) {
        // Update existing event
        const { error } = await supabase
          .from("events")
          .update(eventData)
          .eq("id", Number(event.id));

        if (error) throw error;

        toast({
          title: "Event Updated",
          description: "Your event has been updated successfully.",
        });
      } else {
        // Insert new event
        const { data, error } = await supabase
          .from("events")
          .insert([eventData])
          .select("id");

        if (error) {
          console.error("Supabase insert error:", error);
          if (error.message.includes("row-level security")) {
            console.error("RLS Policy Error - Debug info:", {
              creator_profile_id: eventData.creator_profile_id,
              currentProfile: currentProfile,
              error: error
            });
          }
          throw error;
        }

        if (data && data.length > 0 && data[0].id) {
          eventId = Number(data[0].id);
        }

        toast({
          title: "Event Added",
          description: "Your event has been added successfully.",
        });
      }

      // Now save the transactions to the money table
      if (eventId) {
        console.log("Saving transactions for event:", eventId);
        console.log(
          "Transactions to save:",
          formData.money?.transactions || [],
        );

        // Process each transaction
        const transactions = formData.money?.transactions || [];
        for (const transaction of transactions) {
          // Prepare transaction data for the money table
          const transactionData = {
            profile_id: currentProfile.id,
            transaction_type: transaction.type,
            amount: transaction.amount,
            description: transaction.description,
            transaction_date: transaction.timestamp,
            event_associated: eventId !== null ? Number(eventId) : null,
            equipment_associated: transaction.equipment_associated || null,
            metadata: {
              category: transaction.category || "Event Transaction",
            },
          };

          // For now, we'll just insert new transactions since we're not tracking them by ID
          try {
            console.log("Inserting transaction:", transactionData);
            const { data, error } = await supabase
              .from("money")
              .insert(transactionData);

            if (error) {
              console.error("Error inserting transaction:", error);
              throw error;
            }

            console.log("Transaction inserted successfully:", data);
          } catch (error) {
            console.error("Failed to insert transaction:", error);
            // Continue with other transactions even if one fails
          }
        }

        // Save event participants
        // Always add the current user as a participant (as the creator/organizer)
        const allParticipants = [...(formData.people?.participants || [])];

        // Check if current user is already in the participants list
        const currentUserAlreadyAdded = allParticipants.some(
          (p) => p.user_id === currentProfile.id,
        );

        // If not, add them
        if (!currentUserAlreadyAdded) {
          allParticipants.push({
            id: uuidv4(),
            user_id: currentProfile.id,
            name: `${currentProfile.first_name || ""} ${currentProfile.last_name || ""}`.trim(),
            role: "Organizer",
            contact: currentProfile.email || "",
            type: "organizer",
          });
        }

        if (allParticipants.length > 0) {
          const participantsToSave = allParticipants.map(
            (p: any) => ({
              ...p,
              event_id: eventId,
              user_id: p.user_id || null,
              role: p.role,
              status: p.status || "pending",
            }),
          );

          // Remove fields not in event_participants from each participant object before upsert
          const cleanedParticipants = participantsToSave.map((p: any) => ({
            id: p.id,
            event_id: p.event_id,
            user_id: p.user_id,
            role: p.role,
            status: p.status,
            name: p.name,
            contact: p.contact,
            type: p.type,
          }));

          if (cleanedParticipants.length > 0) {
            console.log("Upserting participants:", cleanedParticipants);
            const { error: participantsError } = await supabase
              .from("event_participants")
              .upsert(cleanedParticipants, { onConflict: "id" });

            if (participantsError) {
              console.error(
                "Error saving event participants:",
                participantsError,
              );
              toast({
                title: "Error",
                description: "Failed to save event participants.",
                variant: "destructive",
              });
            } else {
              console.log("Event participants saved successfully");
            }
          }
        }
      }

      // Check if we have a returnTo parameter in localStorage
      const returnTo = localStorage.getItem("returnToTransactionForm");

      if (returnTo) {
        // Clear the localStorage item
        localStorage.removeItem("returnToTransactionForm");

        // Save the event ID to localStorage for the transaction form to pick up
        if (eventId) {
          localStorage.setItem("selectedEventId", eventId.toString());
        }

        // Redirect back to the transaction form
        router.push(returnTo);
      } else if (onSuccess) {
        onSuccess();
      } else {
        router.push("/calendar/events");
      }
    } catch (error: any) {
      console.error("Error saving event:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to save event. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.push("/calendar/events");
    }
  };

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">
        {isEditing ? "Edit Event" : "Add New Event"}
      </h1>

      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>{formData.title || "Event Details"}</CardTitle>
            <CardDescription>
              {isEditing
                ? "Update your event details"
                : "Schedule a new event in your music calendar"}
            </CardDescription>
          </CardHeader>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <div className="px-6">
              <TabsList className="mb-4">
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="money">Money</TabsTrigger>
                <TabsTrigger value="gear">Gear</TabsTrigger>
                <TabsTrigger value="people">People</TabsTrigger>
                <TabsTrigger value="music">Music</TabsTrigger>
                <TabsTrigger value="wrapUp">Wrap Up</TabsTrigger>
                <TabsTrigger value="travel">Travel</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="summary">
              <CardContent>
                <EventFormSummaryTab
                  formData={formData}
                  onChange={handleChange}
                  showAdvanced={showAdvanced}
                  setShowAdvanced={setShowAdvanced}
                />
              </CardContent>
            </TabsContent>

            <TabsContent value="money">
              <CardContent>
                <EventFormMoneyTab
                  formData={formData}
                  onChange={handleChange}
                />
              </CardContent>
            </TabsContent>

            <TabsContent value="gear">
              <CardContent>
                <EventFormGearTab formData={formData} onChange={handleChange} />
              </CardContent>
            </TabsContent>

            <TabsContent value="people">
              <CardContent>
                <EventFormPeopleTab
                  formData={formData}
                  onChange={handleChange}
                />
              </CardContent>
            </TabsContent>

            <TabsContent value="music">
              <CardContent>
                <EventFormMusicTab
                  formData={formData}
                  onChange={handleChange}
                />
              </CardContent>
            </TabsContent>

            <TabsContent value="wrapUp">
              <CardContent>
                <EventFormWrapUpTab
                  formData={formData}
                  onChange={handleChange}
                />
              </CardContent>
            </TabsContent>

            <TabsContent value="travel">
              <CardContent>
                <EventFormTravelTab
                  formData={formData}
                  onChange={handleChange}
                />
              </CardContent>
            </TabsContent>
          </Tabs>

          <CardFooter className="flex justify-between mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditing ? "Updating..." : "Saving..."}
                </>
              ) : isEditing ? (
                "Update Event"
              ) : (
                "Add Event"
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
