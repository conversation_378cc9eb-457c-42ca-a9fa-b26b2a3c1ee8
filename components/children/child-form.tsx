"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { ChildFormData, ColorScheme } from "@/lib/types/child";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { useChildrenStore } from "@/lib/stores/childrenStore";
import { useAppStore } from "@/lib/store";
import { CalendarIcon, Loader2, AlertCircle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";
import { AvatarSelector } from "./avatar-selector";
import { ColorSchemeSelector } from "./color-scheme-selector";
import { ChildInstrumentSelector } from "./instrument-selector";
import { UserInstrument } from "@/lib/types/instrument";
import {
  addUserInstrument,
  fetchUserInstruments,
  removeUserInstrument,
  updateUserInstrument,
} from "@/lib/queries/unifiedInstrumentQueries";

interface ChildFormProps {
  initialData?: Partial<ChildFormData>;
  isEditing?: boolean;
  childId?: string;
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
}

export function ChildForm({
  initialData,
  isEditing = false,
  childId,
  onSuccess,
  onCancel,
}: ChildFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const currentProfile = useAppStore((state) => state.currentProfile);
  const { addChild, updateChild, isLoading, checkUsernameUnique } =
    useChildrenStore();

  const [formData, setFormData] = useState<Partial<ChildFormData>>(
    initialData || {
      name: "",
      username: "",
      bio: "",
      avatar_url: "",
      is_student: true,
      is_performer: false,
      is_teacher: false,
      color_scheme: "purple" as ColorScheme,
      login_permission: "none",
      instruments: [],
    },
  );

  const [date, setDate] = useState<Date | undefined>(
    initialData?.dob ? new Date(initialData.dob) : undefined,
  );

  const [childInstruments, setChildInstruments] = useState<UserInstrument[]>(
    [],
  );

  const [isUsernameValid, setIsUsernameValid] = useState<boolean>(true);
  const [isCheckingUsername, setIsCheckingUsername] = useState<boolean>(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);
  const [pendingChildData, setPendingChildData] =
    useState<ChildFormData | null>(null);

  // Check username uniqueness when it changes
  useEffect(() => {
    const checkUsername = async () => {
      if (!formData.username || formData.username.trim() === "") {
        setIsUsernameValid(false);
        return;
      }

      // Skip check if username hasn't changed in edit mode
      if (isEditing && initialData?.username === formData.username) {
        setIsUsernameValid(true);
        return;
      }

      setIsCheckingUsername(true);
      try {
        const isUnique = await checkUsernameUnique(formData.username);
        setIsUsernameValid(isUnique);
      } catch (error) {
        console.error("Error checking username:", error);
      } finally {
        setIsCheckingUsername(false);
      }
    };

    if (formData.username) {
      const timeoutId = setTimeout(checkUsername, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [
    formData.username,
    checkUsernameUnique,
    isEditing,
    initialData?.username,
  ]);

  // Load child instruments if editing
  useEffect(() => {
    if (isEditing && childId) {
      const loadChildInstruments = async () => {
        try {
          const instruments = await fetchUserInstruments(undefined, childId);
          setChildInstruments(instruments);
        } catch (error) {
          console.error("Error loading child instruments:", error);
        }
      };

      loadChildInstruments();
    }
  }, [isEditing, childId]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;

    // Force username to lowercase
    if (name === "username") {
      setFormData((prev) => ({ ...prev, [name]: value.toLowerCase() }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleColorSchemeChange = (colorScheme: ColorScheme) => {
    setFormData((prev) => ({ ...prev, color_scheme: colorScheme }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name?.trim()) {
      toast({
        title: "Error",
        description: "Please enter a name.",
        variant: "destructive",
      });
      return;
    }

    if (!date) {
      toast({
        title: "Error",
        description: "Please select a date of birth.",
        variant: "destructive",
      });
      return;
    }

    if (!formData.username?.trim()) {
      toast({
        title: "Error",
        description: "Username is required.",
        variant: "destructive",
      });
      return;
    }

    if (!isUsernameValid) {
      toast({
        title: "Error",
        description:
          "Username is already taken. Please choose a different username.",
        variant: "destructive",
      });
      return;
    }

    const childData: ChildFormData = {
      name: formData.name!,
      dob: date.toISOString(),
      username: formData.username!,
      bio: formData.bio || undefined,
      avatar_url: formData.avatar_url || undefined,
      is_student: formData.is_student ?? true,
      is_performer: formData.is_performer ?? false,
      is_teacher: formData.is_teacher ?? false,
      color_scheme: formData.color_scheme || "purple",
      login_permission: formData.login_permission || "none",
      // We'll handle instruments separately using the child_instruments table
      instruments: [],
    };

    console.log("Submitting child data:", childData);

    // Check if basic information has changed in edit mode
    if (isEditing && initialData) {
      // Convert dates to ISO strings for comparison
      const initialDob = initialData.dob
        ? new Date(initialData.dob).toISOString()
        : null;
      const newDob = childData.dob
        ? new Date(childData.dob).toISOString()
        : null;

      console.log("Comparing DOB:", {
        initialDob,
        newDob,
        initialName: initialData.name,
        newName: childData.name,
        initialUsername: initialData.username,
        newUsername: childData.username,
      });

      const hasBasicInfoChanged =
        initialData.name !== childData.name ||
        initialDob !== newDob ||
        initialData.username !== childData.username;

      if (hasBasicInfoChanged) {
        // Show confirmation dialog
        setPendingChildData(childData);
        setShowConfirmDialog(true);
        return;
      }
    }

    // If not editing or no basic info changes, proceed with save
    await saveChildData(childData);
  };

  const saveChildData = async (childData: ChildFormData) => {
    try {
      if (isEditing && childId) {
        // Update existing child
        const updatedChild = await updateChild(childId, childData);
        toast({
          title: "Success",
          description: "Child profile updated successfully.",
        });
        if (onSuccess) onSuccess(updatedChild);
        else router.push(`/children/${updatedChild.username}`);
      } else {
        // Create new child using the working API endpoint
        if (!currentProfile) {
          toast({
            title: "Error",
            description: "You must be logged in to add a child.",
            variant: "destructive",
          });
          return;
        }

        // Call the working /api/parent/add-child endpoint
        const response = await fetch("/api/parent/add-child", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: childData.name,
            dob: childData.dob,
            username: childData.username,
            bio: childData.bio,
            avatar: childData.avatar_url,
          }),
          credentials: "include",
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error("API error:", response.status, errorText);
          throw new Error(`API error: ${response.status} ${errorText}`);
        }

        const data = await response.json();

        if (data.success) {
          toast({
            title: "Success",
            description: "Child profile created successfully.",
          });

          // Create a child object for the callback
          const newChild = {
            id: data.childId,
            username: childData.username,
            name: childData.name,
          };

          if (onSuccess) onSuccess(newChild);
          else router.push(`/children/${childData.username}`);
        } else {
          throw new Error(data.error || "Failed to create child profile");
        }
      }
    } catch (error: any) {
      console.error("Error saving child:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to save child profile.",
        variant: "destructive",
      });
    }
  };

  // Handle instrument operations
  const handleAddInstrument = async (
    instrumentId: string,
    skillLevel: string,
  ) => {
    try {
      if (!childId) {
        toast({
          title: "Error",
          description:
            "Please save the child profile first before adding instruments.",
          variant: "destructive",
        });
        return;
      }

      const newInstrument = await addUserInstrument(
        instrumentId,
        skillLevel,
        false,
        {},
        undefined,
        childId,
      );
      setChildInstruments([...childInstruments, newInstrument]);

      toast({
        title: "Success",
        description: "Instrument added successfully.",
      });
    } catch (error: any) {
      console.error("Error adding instrument:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to add instrument.",
        variant: "destructive",
      });
    }
  };

  const handleRemoveInstrument = async (instrumentId: string) => {
    try {
      await removeUserInstrument(instrumentId, undefined, childId);
      setChildInstruments(
        childInstruments.filter((ci) => ci.id !== instrumentId),
      );

      toast({
        title: "Success",
        description: "Instrument removed successfully.",
      });
    } catch (error: any) {
      console.error("Error removing instrument:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to remove instrument.",
        variant: "destructive",
      });
    }
  };

  const handleSetPrimaryInstrument = async (instrumentId: string) => {
    try {
      const instrumentToUpdate = childInstruments.find(
        (ci) => ci.id === instrumentId,
      );
      if (!instrumentToUpdate || !childId) return;

      await updateUserInstrument(
        instrumentId,
        {
          instrument_id: instrumentToUpdate.instrument_id,
          skill_level: instrumentToUpdate.skill_level,
          is_primary: true,
          metadata: instrumentToUpdate.metadata || {},
        },
        undefined,
        childId,
      );

      // Update local state
      setChildInstruments(
        childInstruments.map((ci) => ({
          ...ci,
          is_primary: ci.id === instrumentId,
        })),
      );

      toast({
        title: "Success",
        description: "Primary instrument updated successfully.",
      });
    } catch (error: any) {
      console.error("Error setting primary instrument:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to set primary instrument.",
        variant: "destructive",
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Basic Information</h3>

        <div>
          <Label htmlFor="name">Child's Name</Label>
          <Input
            id="name"
            name="name"
            value={formData.name || ""}
            onChange={handleChange}
            placeholder="Enter child's name"
            required
          />
        </div>

        <div>
          <Label htmlFor="dob">Date of Birth</Label>
          <div className="flex space-x-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "flex-1 justify-start text-left font-normal",
                    !date && "text-muted-foreground",
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "PPP") : "Select date of birth"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  value={date}
                  onChange={setDate}
                  disabled={(date) => date > new Date()}
                  maxDate={new Date()}
                />
              </PopoverContent>
            </Popover>

            <Input
              type="date"
              value={date ? format(date, "yyyy-MM-dd") : ""}
              onChange={(e) => {
                if (e.target.value) {
                  setDate(new Date(e.target.value));
                }
              }}
              className="flex-1"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="username">
            Username <span className="text-destructive">*</span>
          </Label>
          <div className="relative">
            <Input
              id="username"
              name="username"
              value={formData.username || ""}
              onChange={handleChange}
              placeholder="Enter username"
              required
              className={cn(
                isCheckingUsername && "pr-10",
                !isUsernameValid && formData.username && "border-destructive",
              )}
            />
            {isCheckingUsername && (
              <Loader2 className="absolute right-3 top-2.5 h-4 w-4 animate-spin text-muted-foreground" />
            )}
          </div>
          {!isUsernameValid && formData.username && (
            <p className="text-xs text-destructive mt-1">
              Username is already taken. Please choose a different username.
            </p>
          )}
          <p className="text-xs text-muted-foreground mt-1">
            Username is required and must be unique. Usernames are permanent and
            cannot be changed later.
          </p>
          <p className="text-xs text-muted-foreground">
            Usernames will be automatically converted to lowercase.
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Appearance</h3>

        <div>
          <Label className="mb-2 block">Color Scheme</Label>
          <ColorSchemeSelector
            value={(formData.color_scheme as ColorScheme) || "purple"}
            onChange={handleColorSchemeChange}
          />
          <p className="text-xs text-muted-foreground mt-2">
            This color scheme will be used for your child's profile and app
            experience.
          </p>
        </div>

        <div>
          <Label>Avatar</Label>
          <AvatarSelector
            username={formData.username || ""}
            value={formData.avatar_url || ""}
            onChange={(url) => {
              if (url !== formData.avatar_url) {
                setFormData((prev) => ({ ...prev, avatar_url: url }));
              }
            }}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Roles</h3>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_student"
              checked={formData.is_student ?? true}
              onCheckedChange={(checked) =>
                handleCheckboxChange("is_student", checked as boolean)
              }
            />
            <Label htmlFor="is_student" className="cursor-pointer">
              Student
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_performer"
              checked={formData.is_performer ?? false}
              onCheckedChange={(checked) =>
                handleCheckboxChange("is_performer", checked as boolean)
              }
            />
            <Label htmlFor="is_performer" className="cursor-pointer">
              Performer
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_teacher"
              checked={formData.is_teacher ?? false}
              onCheckedChange={(checked) =>
                handleCheckboxChange("is_teacher", checked as boolean)
              }
            />
            <Label htmlFor="is_teacher" className="cursor-pointer">
              Teacher
            </Label>
          </div>
        </div>

        {isEditing && childId ? (
          <ChildInstrumentSelector
            childInstruments={childInstruments}
            onAddInstrument={handleAddInstrument}
            onRemoveInstrument={handleRemoveInstrument}
            onSetPrimary={handleSetPrimaryInstrument}
          />
        ) : (
          <div className="p-4 border rounded-md bg-muted/50">
            <p className="text-sm text-muted-foreground">
              You can add instruments after saving the child profile.
            </p>
          </div>
        )}
      </div>

      <div>
        <Label htmlFor="bio">Bio (Optional)</Label>
        <Textarea
          id="bio"
          name="bio"
          value={formData.bio || ""}
          onChange={handleChange}
          placeholder="Add a bio for your child..."
          rows={4}
        />
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isLoading || isCheckingUsername}>
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isEditing ? "Update Child" : "Add Child"}
        </Button>
      </div>

      {/* Confirmation Dialog for Basic Information Changes */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Confirm Changes to Basic Information
            </DialogTitle>
            <DialogDescription>
              You are changing basic information for this child profile. These
              changes can affect how the child appears in the system.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-3">
            {initialData &&
              pendingChildData &&
              initialData.name !== pendingChildData.name && (
                <div className="space-y-1">
                  <p className="text-sm font-medium">Name</p>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="bg-muted p-2 rounded">
                      <span className="text-muted-foreground">From:</span>{" "}
                      {initialData.name}
                    </div>
                    <div className="bg-muted p-2 rounded">
                      <span className="text-muted-foreground">To:</span>{" "}
                      {pendingChildData.name}
                    </div>
                  </div>
                </div>
              )}

            {initialData &&
              pendingChildData &&
              initialData.dob !== pendingChildData.dob && (
                <div className="space-y-1">
                  <p className="text-sm font-medium">Date of Birth</p>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="bg-muted p-2 rounded">
                      <span className="text-muted-foreground">From:</span>{" "}
                      {initialData.dob
                        ? format(new Date(initialData.dob), "PPP")
                        : "None"}
                    </div>
                    <div className="bg-muted p-2 rounded">
                      <span className="text-muted-foreground">To:</span>{" "}
                      {pendingChildData.dob
                        ? format(new Date(pendingChildData.dob), "PPP")
                        : "None"}
                    </div>
                  </div>
                </div>
              )}

            {initialData &&
              pendingChildData &&
              initialData.username !== pendingChildData.username && (
                <div className="space-y-1">
                  <p className="text-sm font-medium">Username</p>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="bg-muted p-2 rounded">
                      <span className="text-muted-foreground">From:</span> @
                      {initialData.username}
                    </div>
                    <div className="bg-muted p-2 rounded">
                      <span className="text-muted-foreground">To:</span> @
                      {pendingChildData.username}
                    </div>
                  </div>
                </div>
              )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setShowConfirmDialog(false);
                if (pendingChildData) {
                  saveChildData(pendingChildData);
                }
              }}
            >
              Confirm Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </form>
  );
}
