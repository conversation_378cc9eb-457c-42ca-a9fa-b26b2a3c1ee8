"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Laptop, Smartphone, Tablet, Monitor, Clock } from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";
import { supabase } from "@/lib/supabase";

interface DeviceDetailsProps {
  childId: string;
}

interface LoginSession {
  id: string;
  device_type: string;
  browser: string;
  os: string;
  ip_address: string;
  location?: string | null;
  created_at: string;
  last_active_at: string;
}

interface ChildLoginInfo {
  name: string;
  last_login_at: string | null;
  last_login_device: string | null;
}

// Type guard for SelectQueryError
function isSelectQueryError(obj: any): boolean {
  return obj && typeof obj === 'object' && obj.error === true;
}

export function DeviceDetails({ childId }: DeviceDetailsProps) {
  const [sessions, setSessions] = useState<LoginSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [childName, setChildName] = useState<string>("");
  const [lastLogin, setLastLogin] = useState<string | null>(null);
  const [lastDevice, setLastDevice] = useState<string | null>(null);

  useEffect(() => {
    const fetchLoginSessions = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // First, check if the child exists and get their name
        try {
          const { data: childData, error: childError } = await supabase
            .from("children")
            .select("name, user_id")
            .eq("id", childId)
            .single();

          if (childError) {
            console.error("Error fetching child data:", childError.message);
            // setError(`Failed to load child details: ${childError.message}`); // Optionally set user-facing error
            // Fallback to empty/default values if child data can't be fetched
            setChildName("Selected Child"); // Or some other default
            setLastLogin(null);
            setLastDevice(null);
          } else if (childData && !isSelectQueryError(childData)) {
            setChildName(childData.name); // Safe to access

            // Get login information from daily_logins table
            try {
              const { data: loginData, error: loginError } = await supabase
                .from("daily_logins")
                .select("login_date")
                .eq("user_id", childData.user_id)
                .order("login_date", { ascending: false })
                .limit(1)
                .single();

              if (!loginError && loginData) {
                setLastLogin(loginData.login_date);
              } else {
                setLastLogin(null);
              }
            } catch (loginErr) {
              console.warn("Error fetching login data:", loginErr);
              setLastLogin(null);
            }

            // Device information is not available in current schema
            setLastDevice(null);
          } else {
            // childData is null, error, or SelectQueryError
            console.warn("Child not found with ID:", childId);
            setChildName("Child"); // Default name
            // setError("Child profile not found."); // Optionally set user-facing error
          }
        } catch (childErr: any) {
          console.error("Exception fetching child data:", childErr);
          // Continue anyway to try the login sessions table
        }

        // Check if the child_login_sessions table exists
        try {
          // First check if the table exists by querying the information schema
          const { data: tableExists, error: tableError } = await supabase
            .from("information_schema.tables" as any) // Cast to any to bypass strict table name checking for this specific case
            .select("table_name")
            .eq("table_name", "child_login_sessions")
            .eq("table_schema", "public")
            .maybeSingle();

          if (tableError) {
            console.warn("Error checking if table exists:", tableError);
            // Just assume the table doesn't exist
            setSessions([]);
            return;
          }

          // If the table exists, try to fetch login sessions
          if (tableExists) {
            const { data, error } = await supabase
              .from("child_login_sessions")
              .select("*")
              .eq("child_id", childId)
              .order("last_active_at", { ascending: false });

            if (error) {
              console.warn("Error fetching login sessions:", error.message);
              // Set empty sessions but don't show an error to the user
              setSessions([]);
            } else {
              setSessions(data || []); // data can be null, so default to empty array
            }
          } else {
            // Table doesn't exist yet
            console.log("child_login_sessions table doesn't exist yet");
            setSessions([]);
          }
        } catch (sessionErr) {
          console.warn("Exception fetching login sessions:", sessionErr);
          // Set empty sessions but don't show an error to the user
          setSessions([]);
        }

        // Child data is already handled above
      } catch (err: any) {
        console.error("Error in login history component:", err);
        // Don't show database errors to the user
        setSessions([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLoginSessions();
  }, [childId]);

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType.toLowerCase()) {
      case "mobile":
        return <Smartphone className="h-4 w-4" />;
      case "tablet":
        return <Tablet className="h-4 w-4" />;
      case "tv":
        return <Monitor className="h-4 w-4" />;
      default:
        return <Laptop className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, "MMM d, yyyy h:mm a");
    } catch (err) {
      return "Unknown";
    }
  };

  const getTimeAgo = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (err) {
      return "Unknown";
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Laptop className="mr-2 h-5 w-5" />
            Device Login History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-6">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Laptop className="mr-2 h-5 w-5" />
            Device Login History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-destructive text-center">{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center">
          <Laptop className="mr-2 h-5 w-5" />
          Device Login History
        </CardTitle>
      </CardHeader>
      <CardContent>
        {sessions.length === 0 ? (
          <div className="text-center text-muted-foreground py-4">
            {childName ? (
              <div className="flex flex-col items-center gap-2">
                <div className="bg-gray-100 rounded-full p-3 inline-block">
                  <Smartphone className="h-6 w-6 text-gray-400" />
                </div>
                <p className="font-medium text-gray-600">
                  {childName} has never logged into a device
                </p>
                <p className="text-sm">
                  When they log in, device details will appear here
                </p>
              </div>
            ) : (
              <div className="flex flex-col items-center gap-2">
                <div className="bg-gray-100 rounded-full p-3 inline-block">
                  <Smartphone className="h-6 w-6 text-gray-400" />
                </div>
                <p className="font-medium text-gray-600">
                  No login history available
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {sessions.map((session) => (
              <div
                key={session.id}
                className="border rounded-lg p-3 flex items-start gap-3"
              >
                <div className="bg-primary/10 p-2 rounded-full">
                  {getDeviceIcon(session.device_type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium flex items-center gap-2">
                        {session.browser}
                        <Badge variant="outline" className="text-xs">
                          {session.device_type}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {session.os}
                        {session.location && ` • ${session.location}`}
                      </p>
                    </div>
                    <div className="text-xs text-right flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{getTimeAgo(session.last_active_at)}</span>
                    </div>
                  </div>
                  <div className="mt-2 flex justify-between text-xs">
                    <span>IP: {session.ip_address}</span>
                    <span>First login: {formatDate(session.created_at)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
