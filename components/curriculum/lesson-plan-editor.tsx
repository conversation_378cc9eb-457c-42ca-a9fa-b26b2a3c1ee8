"use client";

import type React from "react";

import { useState, useEffect } from "react";
import {
  fetchLessonPlanById,
  fetchLessonPlanSongs,
  updateLessonPlan,
  addSongToLessonPlan,
  updateLessonPlanSong,
  removeLessonPlanSong,
  type LessonPlan,
  type LessonPlanSong,
} from "@/lib/queries/curriculumQueries";
import { fetchSongs, type Song } from "@/lib/queries/songQueries";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import {
  Pencil,
  Save,
  X,
  Plus,
  Music,
  Clock,
  Trash2,
  BookOpen,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";

interface LessonPlanEditorProps {
  lessonPlanId: number;
  onUpdate?: (updatedLessonPlan: LessonPlan) => void;
}

interface LessonPlanSongWithDetails extends LessonPlanSong {
  songDetails?: Song;
}

export function LessonPlanEditor({
  lessonPlanId,
  onUpdate,
}: LessonPlanEditorProps) {
  const [lessonPlan, setLessonPlan] = useState<LessonPlan | null>(null);
  const [lessonPlanSongs, setLessonPlanSongs] = useState<
    LessonPlanSongWithDetails[]
  >([]);
  const [availableSongs, setAvailableSongs] = useState<Song[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [isAddingSong, setIsAddingSong] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    duration_minutes: 0,
  });

  const [selectedSongId, setSelectedSongId] = useState("");
  const [songNote, setSongNote] = useState("");

  useEffect(() => {
    const loadLessonPlanData = async () => {
      try {
        const lessonPlanData = await fetchLessonPlanById(lessonPlanId);
        const lessonPlanSongsData = await fetchLessonPlanSongs(lessonPlanId);
        const songsData = await fetchSongs();

        // Fetch song details for each lesson plan song
        const songsWithDetails = await Promise.all(
          lessonPlanSongsData.map(async (lessonPlanSong) => {
            const songDetails = songsData.find(
              (song) => String(song.id) === lessonPlanSong.song_id,
            );
            return {
              ...lessonPlanSong,
              songDetails,
            };
          }),
        );

        setLessonPlan(lessonPlanData);
        setLessonPlanSongs(songsWithDetails);
        setAvailableSongs(songsData);

        // Extract description and duration from metadata
        const metadata = lessonPlanData.metadata as any || {};
        setFormData({
          title: lessonPlanData.title,
          description: metadata.description || "",
          duration_minutes: metadata.duration_minutes || 0,
        });
      } catch (error) {
        console.error("Failed to fetch lesson plan data:", error);
        toast({
          title: "Error",
          description: "Failed to load lesson plan details. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (lessonPlanId) {
      loadLessonPlanData();
    }
  }, [lessonPlanId, toast]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      const updatedLessonPlan = await updateLessonPlan(lessonPlanId, {
        title: formData.title,
        metadata: {
          ...(lessonPlan.metadata as any || {}),
          description: formData.description,
          duration_minutes: Number.parseInt(formData.duration_minutes.toString()),
        },
      });
      setLessonPlan(updatedLessonPlan);
      setIsEditing(false);

      toast({
        title: "Lesson plan updated",
        description: "Lesson plan details have been updated successfully.",
      });

      if (onUpdate) onUpdate(updatedLessonPlan);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update lesson plan. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleAddSong = async () => {
    if (!selectedSongId) {
      toast({
        title: "Error",
        description: "Please select a song to add.",
        variant: "destructive",
      });
      return;
    }

    try {
      const newLessonPlanSong = await addSongToLessonPlan({
        lesson_plan_id: lessonPlanId,
        song_id: selectedSongId,
        display_order: lessonPlanSongs.length,
        notes: songNote,
      });

      const songDetails = availableSongs.find(
        (song) => String(song.id) === selectedSongId,
      );

      setLessonPlanSongs((prev) => [
        ...prev,
        { ...newLessonPlanSong, songDetails },
      ]);
      setIsAddingSong(false);
      setSelectedSongId("");
      setSongNote("");

      toast({
        title: "Song added",
        description: "Song has been added to the lesson plan.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add song to lesson plan. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleRemoveSong = async (lessonPlanSongId: string) => {
    try {
      await removeLessonPlanSong(lessonPlanSongId);
      setLessonPlanSongs((prev) =>
        prev.filter((song) => song.id !== lessonPlanSongId),
      );

      toast({
        title: "Song removed",
        description: "Song has been removed from the lesson plan.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          "Failed to remove song from lesson plan. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleReorderSongs = async (startIndex: number, endIndex: number) => {
    const reorderedSongs = [...lessonPlanSongs];
    const [removed] = reorderedSongs.splice(startIndex, 1);
    reorderedSongs.splice(endIndex, 0, removed);

    // Update display order
    const updatedSongs = reorderedSongs.map((song, index) => ({
      ...song,
      display_order: index,
    }));

    setLessonPlanSongs(updatedSongs);

    // Update in database
    try {
      await Promise.all(
        updatedSongs.map((song) =>
          updateLessonPlanSong(song.id, { display_order: song.display_order }),
        ),
      );
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update song order. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  if (!lessonPlan) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <p>Lesson plan not found</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <BookOpen className="h-5 w-5" />
            <span>{isEditing ? "Edit Lesson Plan" : lessonPlan.title}</span>
          </CardTitle>
          {!isEditing ? (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsEditing(true)}
            >
              <Pencil className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsEditing(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="duration_minutes">Duration (minutes)</Label>
              <Input
                id="duration_minutes"
                name="duration_minutes"
                type="number"
                value={formData.duration_minutes}
                onChange={handleChange}
                min={1}
              />
            </div>
          </form>
        ) : (
          <div className="space-y-4">
            {((lessonPlan.metadata as any)?.duration_minutes || 0) > 0 && (
              <div className="flex items-center text-sm">
                <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                <span>{(lessonPlan.metadata as any)?.duration_minutes} minutes</span>
              </div>
            )}

            {(lessonPlan.metadata as any)?.description && (
              <div>
                <p className="text-muted-foreground">
                  {(lessonPlan.metadata as any)?.description}
                </p>
              </div>
            )}

            <div className="pt-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-medium">Songs</h3>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button size="sm" onClick={() => setIsAddingSong(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add Song
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add Song to Lesson Plan</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div className="space-y-2">
                        <Label htmlFor="song">Select Song</Label>
                        <select
                          id="song"
                          value={selectedSongId}
                          onChange={(e) => setSelectedSongId(e.target.value)}
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                          <option value="">Select a song</option>
                          {availableSongs.map((song) => (
                            <option key={song.id} value={song.id}>
                              {song.title} -{" "}
                              {song.primary_artist_name || "Unknown Artist"}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="notes">Notes</Label>
                        <Textarea
                          id="notes"
                          value={songNote}
                          onChange={(e) => setSongNote(e.target.value)}
                          placeholder="Add any notes about this song in the lesson plan..."
                          rows={3}
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setIsAddingSong(false)}
                      >
                        Cancel
                      </Button>
                      <Button onClick={handleAddSong}>
                        Add to Lesson Plan
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>

              {lessonPlanSongs.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground border rounded-md">
                  <p>No songs added yet</p>
                  <p className="text-sm">Add songs to build your lesson plan</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {lessonPlanSongs.map((lessonPlanSong, index) => (
                    <div
                      key={lessonPlanSong.id}
                      className="flex items-center space-x-2 p-3 border rounded-md bg-card"
                    >
                      <div className="flex items-center justify-center h-6 w-6 rounded-full bg-muted text-xs font-medium">
                        {index + 1}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center">
                          <Music className="h-4 w-4 mr-2 text-muted-foreground" />
                          <p className="font-medium truncate">
                            {lessonPlanSong.songDetails?.title}
                          </p>
                        </div>
                        <p className="text-sm text-muted-foreground truncate">
                          {lessonPlanSong.songDetails?.primary_artist_name ||
                            "Unknown Artist"}
                        </p>
                        {lessonPlanSong.notes && (
                          <p className="text-xs text-muted-foreground mt-1 italic">
                            {lessonPlanSong.notes}
                          </p>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveSong(lessonPlanSong.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>

      {isEditing && (
        <CardFooter>
          <Button type="submit" onClick={handleSubmit} disabled={isSaving}>
            {isSaving ? (
              "Saving..."
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
