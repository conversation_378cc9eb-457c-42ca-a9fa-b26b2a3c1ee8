"use client";

import { useState } from "react";
import { WizardF<PERSON>ework, useWizardState, WizardStep } from "./wizard/wizard-framework";
import { OrganizationTypeStep } from "./wizard/steps/organization-type-step";
import { FinalSummaryStep } from "./wizard/steps/final-summary-step";
import { 
  BandMembersStep, 
  BandSupportStep, 
  BandManagerStep 
} from "./wizard/steps/band-steps";
import { 
  MusicSchoolTeachersStep, 
  MusicSchoolAdminStep, 
  MusicSchoolSpacesStep 
} from "./wizard/steps/music-school-steps";
import { 
  VenueTypeStep, 
  VenueSpacesStep 
} from "./wizard/steps/venue-steps";
import { 
  AgentClientsStep, 
  AgentSpacesStep, 
  AgentReferralStep 
} from "./wizard/steps/agent-steps";
import { 
  OrganizationType, 
  BandRequirements, 
  MusicSchoolRequirements, 
  VenueRequirements, 
  AgentRequirements,
  EnterprisePricingService 
} from "@/lib/services/enterprise-pricing";

interface RequirementsWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete: (planId: string, requirements: Record<string, any>) => void;
}

export function RequirementsWizard({
  open,
  onOpenChange,
  onComplete,
}: RequirementsWizardProps) {
  const { 
    currentStep, 
    setCurrentStep, 
    isCompleted, 
    isSubmitting, 
    resetWizard, 
    completeWizard, 
    startSubmission 
  } = useWizardState();

  // Organization state
  const [organizationType, setOrganizationType] = useState<OrganizationType | "">("");
  const [organizationName, setOrganizationName] = useState<string>("");

  // Band specific fields
  const [bandMembers, setBandMembers] = useState<number | string>(4);
  const [supportStaff, setSupportStaff] = useState<number | string>(0);
  const [hasManager, setHasManager] = useState<boolean>(false);
  const [managerCount, setManagerCount] = useState<number | string>(0);

  // Music School specific fields
  const [teachers, setTeachers] = useState<number | string>(5);
  const [adminStaff, setAdminStaff] = useState<number | string>(2);
  const [lessonSpaces, setLessonSpaces] = useState<number | string>(3);

  // Venue specific fields
  const [venueType, setVenueType] = useState<"performance" | "rehearsal" | "both">("performance");
  const [performanceSpaces, setPerformanceSpaces] = useState<number | string>(1);
  const [rehearsalSpaces, setRehearsalSpaces] = useState<number | string>(0);

  // Agent/Promoter specific fields
  const [representedBands, setRepresentedBands] = useState<number | string>(3);
  const [managedArtists, setManagedArtists] = useState<number | string>(2);
  const [managedRehearsalSpaces, setManagedRehearsalSpaces] = useState<number | string>(0);
  const [representedPerformanceSpaces, setRepresentedPerformanceSpaces] = useState<number | string>(0);
  const [optIntoReferral, setOptIntoReferral] = useState<boolean>(false);

  // Communication preferences
  const [emailUpdates, setEmailUpdates] = useState<boolean>(true);
  const [smsNotifications, setSmsNotifications] = useState<boolean>(false);
  const [notes, setNotes] = useState<string>("");

  const resetStepSpecificData = () => {
    setBandMembers(4);
    setSupportStaff(0);
    setHasManager(false);
    setManagerCount(0);
    setTeachers(5);
    setAdminStaff(2);
    setLessonSpaces(3);
    setVenueType("performance");
    setPerformanceSpaces(1);
    setRehearsalSpaces(0);
    setRepresentedBands(3);
    setManagedArtists(2);
    setManagedRehearsalSpaces(0);
    setRepresentedPerformanceSpaces(0);
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setTimeout(() => {
        resetWizard();
        setOrganizationType("");
        setOrganizationName("");
        resetStepSpecificData();
        setOptIntoReferral(false);
        setEmailUpdates(true);
        setSmsNotifications(false);
        setNotes("");
      }, 300);
    }
    onOpenChange(open);
  };

  const getCurrentRequirements = () => {
    const baseRequirements = {
      organizationType: organizationType as OrganizationType,
      organizationName,
      step: currentStep,
    };

    switch (organizationType) {
      case "enterprise-band":
        return {
          ...baseRequirements,
          band: {
            bandMembers: Number(bandMembers) || 0,
            supportStaff: Number(supportStaff) || 0,
            hasManager: hasManager || false,
            managerCount: Number(managerCount) || 0,
          } as BandRequirements,
        };
      case "enterprise-music-school":
        return {
          ...baseRequirements,
          musicSchool: {
            teachers: Number(teachers) || 0,
            adminStaff: Number(adminStaff) || 0,
            lessonSpaces: Number(lessonSpaces) || 0,
          } as MusicSchoolRequirements,
        };
      case "enterprise-venue":
        return {
          ...baseRequirements,
          venue: {
            venueType: venueType || "performance",
            performanceSpaces: Number(performanceSpaces) || 0,
            rehearsalSpaces: Number(rehearsalSpaces) || 0,
          } as VenueRequirements,
        };
      case "enterprise-agent":
        return {
          ...baseRequirements,
          agent: {
            representedBands: Number(representedBands) || 0,
            managedArtists: Number(managedArtists) || 0,
            managedRehearsalSpaces: Number(managedRehearsalSpaces) || 0,
            representedPerformanceSpaces: Number(representedPerformanceSpaces) || 0,
          } as AgentRequirements,
        };
      default:
        return baseRequirements;
    }
  };

  const getEstimatedCost = () => {
    if (!organizationType) return 0;
    return EnterprisePricingService.calculateEstimatedCost(getCurrentRequirements());
  };

  const formatCurrency = (amountInCents: number) => {
    return EnterprisePricingService.formatCurrency(amountInCents);
  };

  const generateSteps = (): WizardStep[] => {
    const steps: WizardStep[] = [
      {
        id: "organization-type",
        title: "What type of organisation are you?",
        description: "This helps us tailor the right solution for you",
        component: (
          <OrganizationTypeStep
            value={organizationType}
            onChange={setOrganizationType}
            onReset={resetStepSpecificData}
          />
        ),
        isValid: !!organizationType,
      },
    ];

    if (!organizationType) return steps;

    // Add organization-specific steps
    switch (organizationType) {
      case "enterprise-band":
        steps.push(
          {
            id: "band-members",
            title: "How many members are in your band?",
            description: "Enter the exact number of band members ($5 per member/month)",
            component: (
              <BandMembersStep
                bandMembers={bandMembers}
                setBandMembers={setBandMembers}
                organizationName={organizationName}
                setOrganizationName={setOrganizationName}
              />
            ),
          },
          {
            id: "band-support",
            title: "Do you have support staff?",
            description: "Enter the number of support staff ($4 per staff/month)",
            component: (
              <BandSupportStep
                supportStaff={supportStaff}
                setSupportStaff={setSupportStaff}
              />
            ),
          },
          {
            id: "band-manager",
            title: "Do you have a manager or agent?",
            description: "Do you have managers or agents who don't use Crescender? ($10 per manager/month)",
            component: (
              <BandManagerStep
                hasManager={hasManager}
                setHasManager={setHasManager}
                managerCount={managerCount}
                setManagerCount={setManagerCount}
              />
            ),
          }
        );
        break;

      case "enterprise-music-school":
        steps.push(
          {
            id: "school-teachers",
            title: "How many teachers do you have?",
            description: "Enter the exact number of teachers ($5 per teacher/month)",
            component: (
              <MusicSchoolTeachersStep
                teachers={teachers}
                setTeachers={setTeachers}
                organizationName={organizationName}
                setOrganizationName={setOrganizationName}
              />
            ),
          },
          {
            id: "school-admin",
            title: "How many admin staff do you have?",
            description: "Enter the number of administrative staff ($4 per admin/month)",
            component: (
              <MusicSchoolAdminStep
                adminStaff={adminStaff}
                setAdminStaff={setAdminStaff}
              />
            ),
          },
          {
            id: "school-spaces",
            title: "How many lesson spaces do you need?",
            description: "Enter the number of lesson spaces you need to schedule ($10 per space/month)",
            component: (
              <MusicSchoolSpacesStep
                lessonSpaces={lessonSpaces}
                setLessonSpaces={setLessonSpaces}
              />
            ),
          }
        );
        break;

      case "enterprise-venue":
        steps.push(
          {
            id: "venue-type",
            title: "What type of spaces do you manage?",
            description: "Select the types of spaces you manage",
            component: (
              <VenueTypeStep
                venueType={venueType}
                setVenueType={setVenueType}
                organizationName={organizationName}
                setOrganizationName={setOrganizationName}
              />
            ),
          },
          {
            id: "venue-spaces",
            title: "How many spaces do you manage?",
            description: "Enter the exact number of spaces you manage ($10 per space/month)",
            component: (
              <VenueSpacesStep
                venueType={venueType}
                performanceSpaces={performanceSpaces}
                setPerformanceSpaces={setPerformanceSpaces}
                rehearsalSpaces={rehearsalSpaces}
                setRehearsalSpaces={setRehearsalSpaces}
              />
            ),
          }
        );
        break;

      case "enterprise-agent":
        steps.push(
          {
            id: "agent-clients",
            title: "How many bands do you represent?",
            description: "Enter the number of bands you represent",
            component: (
              <AgentClientsStep
                representedBands={representedBands}
                setRepresentedBands={setRepresentedBands}
                managedArtists={managedArtists}
                setManagedArtists={setManagedArtists}
                organizationName={organizationName}
                setOrganizationName={setOrganizationName}
              />
            ),
          },
          {
            id: "agent-spaces",
            title: "How many spaces do you manage?",
            description: "Enter the number of spaces you manage",
            component: (
              <AgentSpacesStep
                managedRehearsalSpaces={managedRehearsalSpaces}
                setManagedRehearsalSpaces={setManagedRehearsalSpaces}
                representedPerformanceSpaces={representedPerformanceSpaces}
                setRepresentedPerformanceSpaces={setRepresentedPerformanceSpaces}
              />
            ),
          },
          {
            id: "agent-referral",
            title: "Referral Program",
            description: "Opt into our referral program to earn free months of service",
            component: (
              <AgentReferralStep
                optIntoReferral={optIntoReferral}
                setOptIntoReferral={setOptIntoReferral}
              />
            ),
          }
        );
        break;
    }

    // Add final summary step
    steps.push({
      id: "summary",
      title: "Summary",
      description: "Review your selections before completing",
      component: (
        <FinalSummaryStep
          organizationType={organizationType as OrganizationType}
          organizationName={organizationName}
          bandRequirements={organizationType === "enterprise-band" ? {
            bandMembers: Number(bandMembers) || 0,
            supportStaff: Number(supportStaff) || 0,
            hasManager: hasManager || false,
            managerCount: Number(managerCount) || 0,
          } : undefined}
          musicSchoolRequirements={organizationType === "enterprise-music-school" ? {
            teachers: Number(teachers) || 0,
            adminStaff: Number(adminStaff) || 0,
            lessonSpaces: Number(lessonSpaces) || 0,
          } : undefined}
          venueRequirements={organizationType === "enterprise-venue" ? {
            venueType: venueType || "performance",
            performanceSpaces: Number(performanceSpaces) || 0,
            rehearsalSpaces: Number(rehearsalSpaces) || 0,
          } : undefined}
          agentRequirements={organizationType === "enterprise-agent" ? {
            representedBands: Number(representedBands) || 0,
            managedArtists: Number(managedArtists) || 0,
            managedRehearsalSpaces: Number(managedRehearsalSpaces) || 0,
            representedPerformanceSpaces: Number(representedPerformanceSpaces) || 0,
          } : undefined}
          optIntoReferral={optIntoReferral}
          estimatedCost={getEstimatedCost()}
          formatCurrency={formatCurrency}
          notes={notes}
          setNotes={setNotes}
          emailUpdates={emailUpdates}
          setEmailUpdates={setEmailUpdates}
          smsNotifications={smsNotifications}
          setSmsNotifications={setSmsNotifications}
        />
      ),
    });

    return steps;
  };

  const handleComplete = async () => {
    startSubmission();
    
    const wizardInfo = {
      metadata: {
        version: "1.0",
        completed_at: new Date().toISOString(),
        estimated_cost: getEstimatedCost(),
        billing_interval: "monthly",
      },
      organization: {
        type: organizationType,
        name: organizationName || "Not specified",
      },
      requirements: {
        common: {
          total_users: EnterprisePricingService.calculateTotalUsers(getCurrentRequirements()),
        },
        ...getCurrentRequirements(),
      },
      preferences: {
        referral_program: organizationType === "enterprise-agent" ? optIntoReferral : false,
        communication_preferences: {
          email_updates: emailUpdates,
          sms_notifications: smsNotifications,
        },
      },
      notes: notes || "",
    };

    setTimeout(() => {
      completeWizard();
      setTimeout(() => {
        onComplete(organizationType as string, wizardInfo);
      }, 2000);
    }, 2000);
  };

  const steps = generateSteps();
  const canGoNext = currentStep === 1 ? !!organizationType : true;

  return (
    <WizardFramework
      open={open}
      onOpenChange={handleOpenChange}
      config={{
        steps,
        onComplete: handleComplete,
        onCancel: () => handleOpenChange(false),
        isSubmitting,
        completedTitle: "Thank you!",
        completedDescription: "Your enterprise requirements have been submitted. We'll be in touch soon to discuss your custom solution.",
      }}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      canGoNext={canGoNext}
      isCompleted={isCompleted}
    />
  );
}
