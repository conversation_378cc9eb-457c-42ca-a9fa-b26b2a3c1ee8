// This file has been refactored into smaller, more maintainable components.
// The new implementation is in enterprise-plan-selector-refactored.tsx
// This file is kept for backward compatibility and will be removed in a future version.

export { RequirementsWizard } from "./enterprise-plan-selector-refactored";

// Legacy interface - maintained for backward compatibility
export interface RequirementsWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete: (planId: string, requirements: Record<string, any>) => void;
}