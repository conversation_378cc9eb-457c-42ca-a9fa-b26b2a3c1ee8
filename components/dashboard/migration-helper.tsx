"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { supabase } from "@/lib/supabase";
import { Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export function MigrationHelper() {
  const [isLoading, setIsLoading] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const { toast } = useToast();

  const runMigration = async () => {
    setIsLoading(true);
    try {
      // Mock data for practice sessions
      const userId = "40fc64b1-45f4-4813-bf4e-017007137c18"; // Lincoln's user ID
      const currentDate = new Date();

      // Create practice sessions for the last 7 days to establish a streak
      for (let i = 0; i < 7; i++) {
        if (i === 3) continue; // Skip one day to make the streak more realistic

        const practiceDate = new Date(currentDate);
        practiceDate.setDate(practiceDate.getDate() - i);

        const { error } = await supabase.from("practice_sessions").insert({
          profile_id: userId,
          duration: Math.floor(Math.random() * 30 + 15),
          self_rating: Math.floor(Math.random() * 3 + 3),
          focus_rating: Math.floor(Math.random() * 3 + 3),
          mood: ["great", "good", "okay"][Math.floor(Math.random() * 3)],
          notes: "Practice session for streak testing",
          created_at: practiceDate.toISOString(),
        });

        if (error) throw error;
      }

      // Get some song IDs (songs table doesn't have user_id, get any songs)
      const { data: songs, error: songsError } = await supabase
        .from("songs")
        .select("id")
        .limit(5);

      if (songsError) throw songsError;

      // If no songs exist, create some
      let songIds = songs?.map((s) => s.id) || [];
      if (songIds.length === 0) {
        const { data: newSongs, error: createError } = await supabase
          .from("songs")
          .insert([
            {
              title: "Autumn Leaves",
              is_original: false,
              primary_artist_name: "Joseph Kosma",
            },
            {
              title: "Blue Bossa",
              is_original: false,
              primary_artist_name: "Kenny Dorham",
            },
            {
              title: "All of Me",
              is_original: false,
              primary_artist_name: "Gerald Marks",
            },
            {
              title: "Fly Me to the Moon",
              is_original: false,
              primary_artist_name: "Bart Howard",
            },
            {
              title: "My Composition",
              is_original: true,
              primary_artist_name: "Lincoln Maurice",
            },
          ])
          .select("id");

        if (createError) throw createError;
        songIds = newSongs?.map((s) => s.id) || [];
      }

      // Create practice sessions for specific songs
      for (let i = 0; i < songIds.length; i++) {
        const songId = songIds[i];

        // Add multiple practice sessions for the first song to make it the most practiced
        if (i === 0) {
          for (let j = 1; j <= 5; j++) {
            const practiceDate = new Date(currentDate);
            practiceDate.setDate(practiceDate.getDate() - j * 2);

            const { error } = await supabase.from("practice_sessions").insert({
              profile_id: userId,
              songs: [{ id: songId, title: "Practice Song" }],
              duration: Math.floor(Math.random() * 30 + 15),
              notes: "Practice session for most practiced song",
              created_at: practiceDate.toISOString(),
            });

            if (error) throw error;
          }
        } else {
          // Add 1-2 practice sessions for other songs
          for (let j = 1; j <= Math.floor(Math.random() * 2 + 1); j++) {
            const practiceDate = new Date(currentDate);
            practiceDate.setDate(practiceDate.getDate() - (j * 3 + 1));

            const { error } = await supabase.from("practice_sessions").insert({
              profile_id: userId,
              songs: [{ id: songId, title: "Practice Song" }],
              duration: Math.floor(Math.random() * 30 + 15),
              notes: "Practice session for song tracking",
              created_at: practiceDate.toISOString(),
            });

            if (error) throw error;
          }
        }
      }

      // Add mock upcoming events
      const eventTypes = ["lesson", "performance", "rehearsal", "recording"];

      for (let i = 1; i <= 5; i++) {
        const eventDate = new Date(currentDate);
        eventDate.setDate(eventDate.getDate() + i * 2);
        const eventType = eventTypes[i % eventTypes.length];

        let title, locationName;
        switch (eventType) {
          case "lesson":
            title = "Piano Lesson";
            locationName = "Music School";
            break;
          case "performance":
            title = "Cafe Performance";
            locationName = "Harmony Cafe";
            break;
          case "rehearsal":
            title = "Band Rehearsal";
            locationName = "Practice Studio";
            break;
          case "recording":
            title = "Studio Recording";
            locationName = "Sound Studio";
            break;
          default:
            title = "Music Event";
            locationName = "Venue";
        }

        const { error } = await supabase.from("events").insert({
          user_id: userId,
          title,
          description: "Mock event for homescreen testing",
          start_date: eventDate.toISOString().split("T")[0],
          end_date: eventDate.toISOString().split("T")[0],
          start_time: "10:00:00",
          end_time: "11:30:00",
          all_day: false,
          location_name: locationName,
          event_type: eventType,
          status: "confirmed",
        });

        if (error) throw error;
      }

      // Add mock notifications
      // First, clear existing notifications
      await supabase.from("notifications").delete().eq("user_id", userId);

      // Add practice streak notification
      const twoHoursAgo = new Date(currentDate);
      twoHoursAgo.setHours(twoHoursAgo.getHours() - 2);

      await supabase.from("notifications").insert({
        user_id: userId,
        category: "informational",
        type: "practice_streak",
        title: "6 Day Practice Streak! 🔥",
        message:
          "Congratulations! You've practiced for 6 days in a row. Keep up the great work!",
        link: "/practice",
        read: false,
        created_at: twoHoursAgo.toISOString(),
        metadata: { streak: 6 },
      });

      // Add upcoming lesson notification
      const fiveHoursAgo = new Date(currentDate);
      fiveHoursAgo.setHours(fiveHoursAgo.getHours() - 5);

      await supabase.from("notifications").insert({
        user_id: userId,
        category: "informational",
        type: "upcoming_lesson",
        title: "Upcoming Lesson Tomorrow",
        message:
          "Reminder: You have a piano lesson scheduled for tomorrow at 10:00 AM.",
        link: "/calendar/events",
        read: false,
        created_at: fiveHoursAgo.toISOString(),
        metadata: {},
      });

      // Add new feature notification
      const oneDayAgo = new Date(currentDate);
      oneDayAgo.setDate(oneDayAgo.getDate() - 1);

      await supabase.from("notifications").insert({
        user_id: userId,
        category: "informational",
        type: "new_feature",
        title: "New Feature: Homescreen Revamp",
        message:
          "We've updated your homescreen with a new personalized dashboard. Check it out!",
        link: "/",
        read: false,
        created_at: oneDayAgo.toISOString(),
        metadata: {},
      });

      setIsComplete(true);
      toast({
        title: "Migration Complete",
        description: "Mock data has been added successfully.",
      });
    } catch (error: any) {
      console.error("Migration error:", error);
      toast({
        title: "Migration Failed",
        description:
          error.message || "An error occurred while adding mock data.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Homescreen Data Setup</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="mb-4">
          This helper will add mock data for the homescreen revamp, including
          practice sessions, events, and notifications.
        </p>
        <Button
          onClick={runMigration}
          disabled={isLoading || isComplete}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding Mock Data...
            </>
          ) : isComplete ? (
            "Mock Data Added"
          ) : (
            "Add Mock Data"
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
