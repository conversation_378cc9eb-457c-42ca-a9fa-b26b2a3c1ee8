"use client";

import { useState } from "react";
import {
  redirectToCheckout,
  redirectToBillingPortal,
} from "../../app/actions/stripe-actions";
import { STRIPE_PLANS, formatCurrency } from "@/lib/config/stripe-config";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, CreditCard, Loader2 } from "lucide-react";
import type { SubscriptionWithComputed } from "@/lib/services/subscription-service";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RequirementsWizard } from "./enterprise-plan-selector";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";

interface SubscriptionManagerProps {
  user: {
    id: string;
    email: string;
    name: string;
  };
  subscription?: SubscriptionWithComputed | null;
}

export function SubscriptionManager({
  user,
  subscription,
}: SubscriptionManagerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showEnterpriseModal, setShowEnterpriseModal] = useState(false);
  const [requirementsWizardOpen, setRequirementsWizardOpen] = useState(false);
  const [requirementsWizardData, setRequirementsWizardData] =
    useState<any>(null);

  const handleSubscribe = async (
    planType: "individual-pro" | "enterprise",
    interval: "monthly" | "yearly" = "monthly",
    metadata: Record<string, string> = {},
  ) => {
    setIsLoading(true);
    const plan = STRIPE_PLANS[planType];
    const price = plan.prices[interval];
    const formData = new FormData();
    formData.append("userId", user.id);
    formData.append("priceId", price.priceId);
    formData.append("planType", planType);
    formData.append("customerEmail", user.email);
    formData.append("customerName", user.name);
    for (const key in metadata) {
      formData.append(key, metadata[key]);
    }
    await redirectToCheckout(formData);
    setIsLoading(false);
  };

  const handleManageBilling = async () => {
    if (!subscription?.stripe_subscription_id) return;
    setIsLoading(true);
    const formData = new FormData();
    formData.append("customerId", subscription.stripe_subscription_id);
    await redirectToBillingPortal(formData);
    setIsLoading(false);
  };

  if (subscription && subscription.status !== "canceled") {
    return (
      <div className="max-w-3xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Your Subscription</CardTitle>
            <CardDescription>
              Manage your subscription and billing information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium">Current Plan</p>
                <p className="text-2xl font-bold">
                  {subscription.plan_type === "individual-pro"
                    ? "Individual Pro"
                    : subscription.plan_type === "enterprise"
                      ? "Enterprise"
                      : "Free"}
                </p>
              </div>
              <Badge
                variant={
                  subscription.status === "active" ||
                  subscription.status === "trialing"
                    ? "default"
                    : "destructive"
                }
              >
                {subscription.status === "active"
                  ? "Active"
                  : subscription.status === "trialing"
                    ? "Trial"
                    : subscription.status}
              </Badge>
            </div>
            {subscription.current_period_end && (
              <div>
                <p className="text-sm font-medium">Current Period</p>
                <p>
                  {new Date(
                    subscription.current_period_start || "",
                  ).toLocaleDateString()}{" "}
                  -{" "}
                  {new Date(
                    subscription.current_period_end,
                  ).toLocaleDateString()}
                </p>
                {subscription.cancel_at && (
                  <p className="text-sm text-destructive mt-1">
                    Your subscription will cancel on{" "}
                    {new Date(subscription.cancel_at).toLocaleDateString()}
                  </p>
                )}
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleManageBilling}
              disabled={isLoading || !subscription.stripe_subscription_id}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Please wait
                </>
              ) : (
                <>
                  <CreditCard className="mr-2 h-4 w-4" /> Manage Billing
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Three-column layout for plans
  return (
    <div className="max-w-6xl mx-auto">
      <div className="flex flex-col items-center mb-8 text-center">
        <h2 className="text-3xl font-bold">Choose a Plan</h2>
        <p className="text-muted-foreground mt-2">
          Select the plan that best fits your needs
        </p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Free Plan */}
        <Card className="flex flex-col justify-between">
          <CardHeader>
            <CardTitle>Free</CardTitle>
            <CardDescription>
              For individuals and families starting out
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              <li>Manage your music career</li>
              <li>Manage your children's music practices</li>
              <li>Build a music and setlist library</li>
              <li>Manage up to 10 instruments and gear</li>
              <li>Simple calendar and event management</li>
              <li>Simple money management</li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button className="w-full" variant="outline" disabled>
              Current Plan
            </Button>
          </CardFooter>
        </Card>
        {/* Individual Pro Plan */}
        <Card className="flex flex-col justify-between">
          <CardHeader>
            <CardTitle>Individual Pro</CardTitle>
            <CardDescription>
              For professionals and individual creators
            </CardDescription>
            <div className="mt-4">
              <span className="text-3xl font-bold">
                {formatCurrency(
                  STRIPE_PLANS["individual-pro"].prices["monthly"].amount,
                )}
              </span>
              <span className="text-muted-foreground ml-1">/month</span>
            </div>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              <li>All Free capabilities</li>
              <li>Ad-free experience</li>
              <li>Manage up to 100 items with up to 10 sub-items each</li>
              <li>Spotify playlist-to-setlist integration</li>
              <li>A range of advanced features</li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button
              className="w-full"
              onClick={() => handleSubscribe("individual-pro")}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Please wait
                </>
              ) : (
                "Upgrade"
              )}
            </Button>
          </CardFooter>
        </Card>
        {/* Enterprise Plan */}
        <Card className="flex flex-col justify-between">
          <CardHeader>
            <CardTitle>Enterprise</CardTitle>
            <CardDescription>
              Solutions for Bands, Studios, Venues and more
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              <li>Solutions for Bands, Studios, Venues and more</li>
              <li>Manage members and staff</li>
              <li>Schedule across multiple spaces and locations</li>
              <li>Coordinate curriculum, lessons and assessments</li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button
              className="w-full"
              variant="secondary"
              onClick={() => setRequirementsWizardOpen(true)}
            >
              Configure
            </Button>
          </CardFooter>
        </Card>
      </div>
      {/* Enterprise Requirements Wizard */}
      <RequirementsWizard
        open={requirementsWizardOpen}
        onOpenChange={setRequirementsWizardOpen}
        onComplete={(planId, requirements) => {
          setRequirementsWizardOpen(false);
          if (planId === "individual-pro" || planId === "enterprise") {
            handleSubscribe(planId, "monthly", requirements.metadata);
          }
        }}
      />
    </div>
  );
}
