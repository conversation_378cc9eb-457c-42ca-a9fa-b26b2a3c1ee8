// Export the main wizard framework
export { WizardFramework, useW<PERSON>rdState } from "./wizard-framework";
export type { WizardStep, WizardConfig } from "./wizard-framework";

// Export step components
export { OrganizationTypeStep } from "./steps/organization-type-step";
export { FinalSummaryStep } from "./steps/final-summary-step";

// Band steps
export { 
  BandMembersStep, 
  BandSupportStep, 
  BandManagerStep,
  BandSummaryStep 
} from "./steps/band-steps";

// Music school steps
export { 
  MusicSchoolTeachersStep, 
  MusicSchoolAdminStep, 
  MusicSchoolSpacesStep,
  MusicSchoolSummaryStep 
} from "./steps/music-school-steps";

// Venue steps
export { 
  VenueTypeStep, 
  VenueSpacesStep,
  VenueSummaryStep 
} from "./steps/venue-steps";

// Agent steps
export { 
  AgentClientsStep, 
  AgentSpacesStep, 
  AgentReferralStep,
  AgentSummaryStep 
} from "./steps/agent-steps";
