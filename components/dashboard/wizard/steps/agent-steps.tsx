"use client";

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { validateNumberInput, validateNumberOnBlur } from "../wizard-framework";
import { AgentRequirements } from "@/lib/services/enterprise-pricing";

interface AgentClientsStepProps {
  representedBands: number | string;
  setRepresentedBands: (value: number | string) => void;
  managedArtists: number | string;
  setManagedArtists: (value: number | string) => void;
  organizationName: string;
  setOrganizationName: (value: string) => void;
}

export function AgentClientsStep({
  representedBands,
  setRepresentedBands,
  managedArtists,
  setManagedArtists,
  organizationName,
  setOrganizationName,
}: AgentClientsStepProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="organizationName">
          Agency Name (Optional)
        </Label>
        <Input
          id="organizationName"
          value={organizationName}
          onChange={(e) => setOrganizationName(e.target.value)}
          placeholder="e.g., Premier Music Management, Star Promotions"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="representedBands">Bands Represented</Label>
        <Input
          id="representedBands"
          type="text"
          value={representedBands}
          onChange={(e) =>
            validateNumberInput(e.target.value, setRepresentedBands, 0)
          }
          onBlur={() =>
            validateNumberOnBlur(representedBands, setRepresentedBands, 0)
          }
          min="0"
          placeholder="Number of bands represented"
        />
        <p className="text-xs text-muted-foreground">
          First band included, additional bands $3/month each
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="managedArtists">Artists Managed (Solo/Duos)</Label>
        <Input
          id="managedArtists"
          type="text"
          value={managedArtists}
          onChange={(e) =>
            validateNumberInput(e.target.value, setManagedArtists, 0)
          }
          onBlur={() =>
            validateNumberOnBlur(managedArtists, setManagedArtists, 0)
          }
          min="0"
          placeholder="Number of solo artists/duos"
        />
        <p className="text-xs text-muted-foreground">
          Solo artists/duos $3/month each
        </p>
      </div>
    </div>
  );
}

interface AgentSpacesStepProps {
  managedRehearsalSpaces: number | string;
  setManagedRehearsalSpaces: (value: number | string) => void;
  representedPerformanceSpaces: number | string;
  setRepresentedPerformanceSpaces: (value: number | string) => void;
}

export function AgentSpacesStep({
  managedRehearsalSpaces,
  setManagedRehearsalSpaces,
  representedPerformanceSpaces,
  setRepresentedPerformanceSpaces,
}: AgentSpacesStepProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="managedRehearsalSpaces">
          Rehearsal Spaces Managed
        </Label>
        <Input
          id="managedRehearsalSpaces"
          type="text"
          value={managedRehearsalSpaces}
          onChange={(e) =>
            validateNumberInput(e.target.value, setManagedRehearsalSpaces, 0)
          }
          onBlur={() =>
            validateNumberOnBlur(managedRehearsalSpaces, setManagedRehearsalSpaces, 0)
          }
          min="0"
          placeholder="Number of rehearsal spaces"
        />
        <p className="text-xs text-muted-foreground">
          Rehearsal spaces $10/month each
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="representedPerformanceSpaces">
          Performance Spaces Represented
        </Label>
        <Input
          id="representedPerformanceSpaces"
          type="text"
          value={representedPerformanceSpaces}
          onChange={(e) =>
            validateNumberInput(e.target.value, setRepresentedPerformanceSpaces, 0)
          }
          onBlur={() =>
            validateNumberOnBlur(representedPerformanceSpaces, setRepresentedPerformanceSpaces, 0)
          }
          min="0"
          placeholder="Number of performance spaces"
        />
        <p className="text-xs text-muted-foreground">
          Performance spaces $10/month each
        </p>
      </div>
    </div>
  );
}

interface AgentReferralStepProps {
  optIntoReferral: boolean;
  setOptIntoReferral: (value: boolean) => void;
}

export function AgentReferralStep({
  optIntoReferral,
  setOptIntoReferral,
}: AgentReferralStepProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Checkbox
          id="optIntoReferral"
          checked={optIntoReferral}
          onCheckedChange={(checked) => setOptIntoReferral(Boolean(checked))}
        />
        <Label htmlFor="optIntoReferral" className="font-normal">
          Opt into our referral program (earn free months of service)
        </Label>
      </div>
      
      {optIntoReferral && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-md">
          <p className="text-sm text-green-800">
            Great! You'll earn one free month of service for every successful referral.
          </p>
        </div>
      )}
    </div>
  );
}

interface AgentSummaryStepProps {
  requirements: AgentRequirements;
  organizationName?: string;
  optIntoReferral: boolean;
  estimatedCost: number;
  formatCurrency: (amount: number) => string;
}

export function AgentSummaryStep({
  requirements,
  organizationName,
  optIntoReferral,
  estimatedCost,
  formatCurrency,
}: AgentSummaryStepProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Agent/Promoter Configuration Summary</h3>
      
      <div className="p-4 border rounded-md space-y-2 bg-muted/50">
        <p><strong>Organization Type:</strong> Agent/Promoter</p>
        {organizationName && (
          <p><strong>Agency Name:</strong> {organizationName}</p>
        )}
        <p><strong>Bands Represented:</strong> {requirements.representedBands}</p>
        <p><strong>Artists Managed:</strong> {requirements.managedArtists}</p>
        <p><strong>Rehearsal Spaces Managed:</strong> {requirements.managedRehearsalSpaces}</p>
        <p><strong>Performance Spaces Represented:</strong> {requirements.representedPerformanceSpaces}</p>
        
        {optIntoReferral && (
          <p className="text-green-600">✓ Opted into Referral Program</p>
        )}
        
        <p className="text-lg font-semibold mt-2">
          Estimated Monthly Cost: {formatCurrency(estimatedCost)}
        </p>
        <p className="text-xs text-muted-foreground">
          Final pricing will be confirmed after submission.
        </p>
      </div>
    </div>
  );
}
