"use client";

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { validateNumberInput, validateNumberOnBlur } from "../wizard-framework";
import { BandRequirements } from "@/lib/services/enterprise-pricing";

interface BandMembersStepProps {
  bandMembers: number | string;
  setBandMembers: (value: number | string) => void;
  organizationName: string;
  setOrganizationName: (value: string) => void;
}

export function BandMembersStep({
  bandMembers,
  setBandMembers,
  organizationName,
  setOrganizationName,
}: BandMembersStepProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="organizationName">
          Band Name (Optional)
        </Label>
        <Input
          id="organizationName"
          value={organizationName}
          onChange={(e) => setOrganizationName(e.target.value)}
          placeholder="e.g., The Rockers, Electric Dreams"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="bandMembers">Band Members</Label>
        <Input
          id="bandMembers"
          type="text"
          value={bandMembers}
          onChange={(e) =>
            validateNumberInput(e.target.value, setBandMembers, 1)
          }
          onBlur={() =>
            validateNumberOnBlur(bandMembers, setBandMembers, 1)
          }
          min="1"
          placeholder="Number of band members"
        />
        <p className="text-xs text-muted-foreground">
          First member included in base plan, additional members $5/month each
        </p>
      </div>
    </div>
  );
}

interface BandSupportStepProps {
  supportStaff: number | string;
  setSupportStaff: (value: number | string) => void;
}

export function BandSupportStep({
  supportStaff,
  setSupportStaff,
}: BandSupportStepProps) {
  return (
    <div className="space-y-2">
      <Label htmlFor="supportStaff">
        Support Staff (e.g., roadies, techs)
      </Label>
      <Input
        id="supportStaff"
        type="text"
        value={supportStaff}
        onChange={(e) =>
          validateNumberInput(e.target.value, setSupportStaff, 0)
        }
        onBlur={() =>
          validateNumberOnBlur(supportStaff, setSupportStaff, 0)
        }
        min="0"
        placeholder="Number of support staff"
      />
      <p className="text-xs text-muted-foreground">
        Support staff $4/month each
      </p>
    </div>
  );
}

interface BandManagerStepProps {
  hasManager: boolean;
  setHasManager: (value: boolean) => void;
  managerCount: number | string;
  setManagerCount: (value: number | string) => void;
}

export function BandManagerStep({
  hasManager,
  setHasManager,
  managerCount,
  setManagerCount,
}: BandManagerStepProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Checkbox
          id="hasManager"
          checked={hasManager}
          onCheckedChange={(checked) => setHasManager(Boolean(checked))}
        />
        <Label htmlFor="hasManager" className="font-normal">
          We have a manager/agent (not using Crescender)
        </Label>
      </div>
      
      {hasManager && (
        <div className="space-y-2">
          <Label htmlFor="managerCount">
            Number of Managers/Agents
          </Label>
          <Input
            id="managerCount"
            type="text"
            value={managerCount}
            onChange={(e) =>
              validateNumberInput(e.target.value, setManagerCount, 1)
            }
            onBlur={() =>
              validateNumberOnBlur(managerCount, setManagerCount, 1)
            }
            min="1"
            disabled={!hasManager}
            placeholder="Number of external managers"
          />
          <p className="text-xs text-muted-foreground">
            External managers $10/month each
          </p>
        </div>
      )}
    </div>
  );
}

interface BandSummaryStepProps {
  requirements: BandRequirements;
  organizationName?: string;
  estimatedCost: number;
  formatCurrency: (amount: number) => string;
}

export function BandSummaryStep({
  requirements,
  organizationName,
  estimatedCost,
  formatCurrency,
}: BandSummaryStepProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Band Configuration Summary</h3>
      
      <div className="p-4 border rounded-md space-y-2 bg-muted/50">
        <p><strong>Organization Type:</strong> Band</p>
        {organizationName && (
          <p><strong>Band Name:</strong> {organizationName}</p>
        )}
        <p><strong>Band Members:</strong> {requirements.bandMembers}</p>
        <p><strong>Support Staff:</strong> {requirements.supportStaff}</p>
        {requirements.hasManager && (
          <p><strong>External Managers/Agents:</strong> {requirements.managerCount}</p>
        )}
        
        <p className="text-lg font-semibold mt-2">
          Estimated Monthly Cost: {formatCurrency(estimatedCost)}
        </p>
        <p className="text-xs text-muted-foreground">
          Final pricing will be confirmed after submission.
        </p>
      </div>
    </div>
  );
}
