"use client";

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { STRIPE_PLANS } from "@/lib/config/stripe-config";
import { 
  OrganizationType, 
  BandRequirements, 
  MusicSchoolRequirements, 
  VenueRequirements, 
  AgentRequirements 
} from "@/lib/services/enterprise-pricing";

interface FinalSummaryStepProps {
  organizationType: OrganizationType;
  organizationName?: string;
  bandRequirements?: BandRequirements;
  musicSchoolRequirements?: MusicSchoolRequirements;
  venueRequirements?: VenueRequirements;
  agentRequirements?: AgentRequirements;
  optIntoReferral?: boolean;
  estimatedCost: number;
  formatCurrency: (amount: number) => string;
  notes: string;
  setNotes: (value: string) => void;
  emailUpdates: boolean;
  setEmailUpdates: (value: boolean) => void;
  smsNotifications: boolean;
  setSmsNotifications: (value: boolean) => void;
}

export function FinalSummaryStep({
  organizationType,
  organizationName,
  bandRequirements,
  musicSchoolRequirements,
  venueRequirements,
  agentRequirements,
  optIntoReferral,
  estimatedCost,
  formatCurrency,
  notes,
  setNotes,
  emailUpdates,
  setEmailUpdates,
  smsNotifications,
  setSmsNotifications,
}: FinalSummaryStepProps) {
  const planName = STRIPE_PLANS[organizationType]?.name || organizationType;

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Summary</h3>
        <p className="text-sm text-muted-foreground">
          Please review your selections. You are configuring the{" "}
          <strong>{planName}</strong> plan.
        </p>
        
        <div className="p-4 border rounded-md space-y-2 bg-muted/50">
          <p><strong>Organisation Type:</strong> {planName}</p>
          {organizationName && (
            <p><strong>Organisation Name:</strong> {organizationName}</p>
          )}

          {/* Band-specific summary */}
          {organizationType === "enterprise-band" && bandRequirements && (
            <>
              <p><strong>Band Members:</strong> {bandRequirements.bandMembers}</p>
              <p><strong>Support Staff:</strong> {bandRequirements.supportStaff}</p>
              {bandRequirements.hasManager && (
                <p><strong>External Managers/Agents:</strong> {bandRequirements.managerCount}</p>
              )}
            </>
          )}

          {/* Music School-specific summary */}
          {organizationType === "enterprise-music-school" && musicSchoolRequirements && (
            <>
              <p><strong>Teachers:</strong> {musicSchoolRequirements.teachers}</p>
              <p><strong>Admin Staff:</strong> {musicSchoolRequirements.adminStaff}</p>
              <p><strong>Lesson Spaces:</strong> {musicSchoolRequirements.lessonSpaces}</p>
            </>
          )}

          {/* Venue-specific summary */}
          {organizationType === "enterprise-venue" && venueRequirements && (
            <>
              <p><strong>Venue Type:</strong> {venueRequirements.venueType}</p>
              {(venueRequirements.venueType === "performance" || venueRequirements.venueType === "both") && (
                <p><strong>Performance Spaces:</strong> {venueRequirements.performanceSpaces}</p>
              )}
              {(venueRequirements.venueType === "rehearsal" || venueRequirements.venueType === "both") && (
                <p><strong>Rehearsal Spaces:</strong> {venueRequirements.rehearsalSpaces}</p>
              )}
            </>
          )}

          {/* Agent-specific summary */}
          {organizationType === "enterprise-agent" && agentRequirements && (
            <>
              <p><strong>Bands Represented:</strong> {agentRequirements.representedBands}</p>
              <p><strong>Artists Managed:</strong> {agentRequirements.managedArtists}</p>
              <p><strong>Rehearsal Spaces Managed:</strong> {agentRequirements.managedRehearsalSpaces}</p>
              <p><strong>Performance Spaces Represented:</strong> {agentRequirements.representedPerformanceSpaces}</p>
              {optIntoReferral && (
                <p className="text-green-600">✓ Opted into Referral Program</p>
              )}
            </>
          )}

          <p className="text-lg font-semibold mt-2">
            Estimated Monthly Cost: {formatCurrency(estimatedCost)}
          </p>
          <p className="text-xs text-muted-foreground">
            Final pricing will be confirmed after submission.
          </p>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">
          Additional Notes or Questions (Optional)
        </Label>
        <Input
          id="notes"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="Any specific needs or questions?"
        />
      </div>

      <div className="space-y-2">
        <Label>Communication Preferences</Label>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="emailUpdates"
              checked={emailUpdates}
              onCheckedChange={(checked) => setEmailUpdates(Boolean(checked))}
            />
            <Label htmlFor="emailUpdates" className="font-normal">
              Receive email updates about your account and new features.
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="smsNotifications"
              checked={smsNotifications}
              onCheckedChange={(checked) => setSmsNotifications(Boolean(checked))}
            />
            <Label htmlFor="smsNotifications" className="font-normal">
              Receive critical SMS notifications (requires phone number on file).
            </Label>
          </div>
        </div>
      </div>
    </div>
  );
}
