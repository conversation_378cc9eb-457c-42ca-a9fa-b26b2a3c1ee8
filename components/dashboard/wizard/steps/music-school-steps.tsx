"use client";

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { validateNumberInput, validateNumberOnBlur } from "../wizard-framework";
import { MusicSchoolRequirements } from "@/lib/services/enterprise-pricing";

interface MusicSchoolTeachersStepProps {
  teachers: number | string;
  setTeachers: (value: number | string) => void;
  organizationName: string;
  setOrganizationName: (value: string) => void;
}

export function MusicSchoolTeachersStep({
  teachers,
  setTeachers,
  organizationName,
  setOrganizationName,
}: MusicSchoolTeachersStepProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="organizationName">
          School Name (Optional)
        </Label>
        <Input
          id="organizationName"
          value={organizationName}
          onChange={(e) => setOrganizationName(e.target.value)}
          placeholder="e.g., Elite Music Academy, Harmony School"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="teachers">Teachers</Label>
        <Input
          id="teachers"
          type="text"
          value={teachers}
          onChange={(e) => validateNumberInput(e.target.value, setTeachers, 1)}
          onBlur={() => validateNumberOnBlur(teachers, setTeachers, 1)}
          min="1"
          placeholder="Number of teachers"
        />
        <p className="text-xs text-muted-foreground">
          First teacher included in base plan, additional teachers $5/month each
        </p>
      </div>
    </div>
  );
}

interface MusicSchoolAdminStepProps {
  adminStaff: number | string;
  setAdminStaff: (value: number | string) => void;
}

export function MusicSchoolAdminStep({
  adminStaff,
  setAdminStaff,
}: MusicSchoolAdminStepProps) {
  return (
    <div className="space-y-2">
      <Label htmlFor="adminStaff">
        Administrative Staff
      </Label>
      <Input
        id="adminStaff"
        type="text"
        value={adminStaff}
        onChange={(e) =>
          validateNumberInput(e.target.value, setAdminStaff, 0)
        }
        onBlur={() => validateNumberOnBlur(adminStaff, setAdminStaff, 0)}
        min="0"
        placeholder="Number of admin staff"
      />
      <p className="text-xs text-muted-foreground">
        Administrative staff $4/month each
      </p>
    </div>
  );
}

interface MusicSchoolSpacesStepProps {
  lessonSpaces: number | string;
  setLessonSpaces: (value: number | string) => void;
}

export function MusicSchoolSpacesStep({
  lessonSpaces,
  setLessonSpaces,
}: MusicSchoolSpacesStepProps) {
  return (
    <div className="space-y-2">
      <Label htmlFor="lessonSpaces">
        Lesson Spaces to Schedule
      </Label>
      <Input
        id="lessonSpaces"
        type="text"
        value={lessonSpaces}
        onChange={(e) =>
          validateNumberInput(e.target.value, setLessonSpaces, 0)
        }
        onBlur={() =>
          validateNumberOnBlur(lessonSpaces, setLessonSpaces, 0)
        }
        min="0"
        placeholder="Number of lesson spaces"
      />
      <p className="text-xs text-muted-foreground">
        First lesson space included, additional spaces $10/month each
      </p>
    </div>
  );
}

interface MusicSchoolSummaryStepProps {
  requirements: MusicSchoolRequirements;
  organizationName?: string;
  estimatedCost: number;
  formatCurrency: (amount: number) => string;
}

export function MusicSchoolSummaryStep({
  requirements,
  organizationName,
  estimatedCost,
  formatCurrency,
}: MusicSchoolSummaryStepProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Music School Configuration Summary</h3>
      
      <div className="p-4 border rounded-md space-y-2 bg-muted/50">
        <p><strong>Organization Type:</strong> Music School</p>
        {organizationName && (
          <p><strong>School Name:</strong> {organizationName}</p>
        )}
        <p><strong>Teachers:</strong> {requirements.teachers}</p>
        <p><strong>Admin Staff:</strong> {requirements.adminStaff}</p>
        <p><strong>Lesson Spaces:</strong> {requirements.lessonSpaces}</p>
        
        <p className="text-lg font-semibold mt-2">
          Estimated Monthly Cost: {formatCurrency(estimatedCost)}
        </p>
        <p className="text-xs text-muted-foreground">
          Final pricing will be confirmed after submission.
        </p>
      </div>
    </div>
  );
}
