"use client";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Music, School, Store, Users } from "lucide-react";
import { OrganizationType } from "@/lib/services/enterprise-pricing";

interface OrganizationTypeStepProps {
  value: OrganizationType | "";
  onChange: (value: OrganizationType) => void;
  onReset?: () => void;
}

const organizationTypes = [
  { 
    id: "enterprise-band" as OrganizationType, 
    label: "Band", 
    icon: Music,
    description: "Professional bands and musical groups"
  },
  {
    id: "enterprise-music-school" as OrganizationType,
    label: "Music School",
    icon: School,
    description: "Music education institutions and academies"
  },
  { 
    id: "enterprise-venue" as OrganizationType, 
    label: "Venue", 
    icon: Store,
    description: "Performance venues and rehearsal spaces"
  },
  {
    id: "enterprise-agent" as OrganizationType,
    label: "Agent/Promoter",
    icon: Users,
    description: "Artist management and event promotion"
  },
];

export function OrganizationTypeStep({ 
  value, 
  onChange, 
  onReset 
}: OrganizationTypeStepProps) {
  const handleValueChange = (newValue: string) => {
    const organizationType = newValue as OrganizationType;
    onChange(organizationType);
    
    // Reset step-specific data when type changes
    if (onReset) {
      onReset();
    }
  };

  return (
    <RadioGroup
      value={value}
      onValueChange={handleValueChange}
      className="grid grid-cols-2 gap-4"
    >
      {organizationTypes.map((item) => (
        <Label
          key={item.id}
          htmlFor={item.id}
          className={`flex flex-col items-center justify-center rounded-md border-2 p-4 hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors ${
            value === item.id
              ? "border-primary bg-primary/10"
              : "border-muted"
          }`}
        >
          <RadioGroupItem
            value={item.id}
            id={item.id}
            className="sr-only"
          />
          <item.icon className="mb-3 h-8 w-8" />
          <span className="font-medium">{item.label}</span>
          <span className="text-xs text-muted-foreground text-center mt-1">
            {item.description}
          </span>
        </Label>
      ))}
    </RadioGroup>
  );
}
