"use client";

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { validateNumberInput, validateNumberOnBlur } from "../wizard-framework";
import { VenueRequirements } from "@/lib/services/enterprise-pricing";

interface VenueTypeStepProps {
  venueType: "performance" | "rehearsal" | "both";
  setVenueType: (value: "performance" | "rehearsal" | "both") => void;
  organizationName: string;
  setOrganizationName: (value: string) => void;
}

export function VenueTypeStep({
  venueType,
  setVenueType,
  organizationName,
  setOrganizationName,
}: VenueTypeStepProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="organizationName">
          Venue Name (Optional)
        </Label>
        <Input
          id="organizationName"
          value={organizationName}
          onChange={(e) => setOrganizationName(e.target.value)}
          placeholder="e.g., Grand Theater, Studio Complex"
        />
      </div>

      <div className="space-y-2">
        <Label>Venue Type</Label>
        <RadioGroup
          value={venueType}
          onValueChange={(value) => setVenueType(value as "performance" | "rehearsal" | "both")}
          className="flex space-x-4"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="performance" id="venuePerformance" />
            <Label htmlFor="venuePerformance" className="font-normal">
              Performance
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="rehearsal" id="venueRehearsal" />
            <Label htmlFor="venueRehearsal" className="font-normal">
              Rehearsal
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="both" id="venueBoth" />
            <Label htmlFor="venueBoth" className="font-normal">
              Both
            </Label>
          </div>
        </RadioGroup>
      </div>
    </div>
  );
}

interface VenueSpacesStepProps {
  venueType: "performance" | "rehearsal" | "both";
  performanceSpaces: number | string;
  setPerformanceSpaces: (value: number | string) => void;
  rehearsalSpaces: number | string;
  setRehearsalSpaces: (value: number | string) => void;
}

export function VenueSpacesStep({
  venueType,
  performanceSpaces,
  setPerformanceSpaces,
  rehearsalSpaces,
  setRehearsalSpaces,
}: VenueSpacesStepProps) {
  return (
    <div className="space-y-4">
      {(venueType === "performance" || venueType === "both") && (
        <div className="space-y-2">
          <Label htmlFor="performanceSpaces">Performance Spaces</Label>
          <Input
            id="performanceSpaces"
            type="text"
            value={performanceSpaces}
            onChange={(e) =>
              validateNumberInput(
                e.target.value,
                setPerformanceSpaces,
                venueType === "performance" ? 1 : 0
              )
            }
            onBlur={() =>
              validateNumberOnBlur(
                performanceSpaces,
                setPerformanceSpaces,
                venueType === "performance" ? 1 : 0
              )
            }
            min={venueType === "performance" ? "1" : "0"}
            placeholder="Number of performance spaces"
          />
          <p className="text-xs text-muted-foreground">
            Performance spaces $10/month each
          </p>
        </div>
      )}
      
      {(venueType === "rehearsal" || venueType === "both") && (
        <div className="space-y-2">
          <Label htmlFor="rehearsalSpaces">Rehearsal Spaces</Label>
          <Input
            id="rehearsalSpaces"
            type="text"
            value={rehearsalSpaces}
            onChange={(e) =>
              validateNumberInput(
                e.target.value,
                setRehearsalSpaces,
                venueType === "rehearsal" ? 1 : 0
              )
            }
            onBlur={() =>
              validateNumberOnBlur(
                rehearsalSpaces,
                setRehearsalSpaces,
                venueType === "rehearsal" ? 1 : 0
              )
            }
            min={venueType === "rehearsal" ? "1" : "0"}
            placeholder="Number of rehearsal spaces"
          />
          <p className="text-xs text-muted-foreground">
            Rehearsal spaces $10/month each
          </p>
        </div>
      )}
    </div>
  );
}

interface VenueSummaryStepProps {
  requirements: VenueRequirements;
  organizationName?: string;
  estimatedCost: number;
  formatCurrency: (amount: number) => string;
}

export function VenueSummaryStep({
  requirements,
  organizationName,
  estimatedCost,
  formatCurrency,
}: VenueSummaryStepProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Venue Configuration Summary</h3>
      
      <div className="p-4 border rounded-md space-y-2 bg-muted/50">
        <p><strong>Organization Type:</strong> Venue</p>
        {organizationName && (
          <p><strong>Venue Name:</strong> {organizationName}</p>
        )}
        <p><strong>Venue Type:</strong> {requirements.venueType}</p>
        
        {(requirements.venueType === "performance" || requirements.venueType === "both") && (
          <p><strong>Performance Spaces:</strong> {requirements.performanceSpaces}</p>
        )}
        
        {(requirements.venueType === "rehearsal" || requirements.venueType === "both") && (
          <p><strong>Rehearsal Spaces:</strong> {requirements.rehearsalSpaces}</p>
        )}
        
        <p className="text-lg font-semibold mt-2">
          Estimated Monthly Cost: {formatCurrency(estimatedCost)}
        </p>
        <p className="text-xs text-muted-foreground">
          Final pricing will be confirmed after submission.
        </p>
      </div>
    </div>
  );
}
