"use client";

import { useState, ReactNode } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { MoveRight, CheckCircle2 } from "lucide-react";

export interface WizardStep {
  id: string;
  title: string;
  description: string;
  component: ReactNode;
  isValid?: boolean;
}

export interface WizardConfig {
  steps: WizardStep[];
  onComplete: (data: any) => void;
  onCancel?: () => void;
  isSubmitting?: boolean;
  completedTitle?: string;
  completedDescription?: string;
}

interface WizardFrameworkProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  config: WizardConfig;
  currentStep: number;
  onStepChange: (step: number) => void;
  canGoNext?: boolean;
  canGoBack?: boolean;
  isCompleted?: boolean;
}

export function WizardFramework({
  open,
  onOpenChange,
  config,
  currentStep,
  onStepChange,
  canGoNext = true,
  canGoBack = true,
  isCompleted = false,
}: WizardFrameworkProps) {
  const { steps, onComplete, isSubmitting = false } = config;
  const maxSteps = steps.length;
  const currentStepData = steps[currentStep - 1];

  const handleNext = () => {
    if (currentStep < maxSteps) {
      onStepChange(currentStep + 1);
    } else {
      onComplete({});
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      onStepChange(currentStep - 1);
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open && config.onCancel) {
      config.onCancel();
    }
    onOpenChange(open);
  };

  if (!currentStepData && !isCompleted) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        {!isCompleted ? (
          <>
            <DialogHeader>
              <DialogTitle>{currentStepData?.title}</DialogTitle>
              <DialogDescription>{currentStepData?.description}</DialogDescription>
            </DialogHeader>
            
            <div className="py-4 space-y-6">
              {currentStepData?.component}
            </div>

            <DialogFooter className="mt-6">
              {currentStep > 1 && canGoBack && (
                <Button
                  variant="outline"
                  onClick={handleBack}
                  disabled={isSubmitting}
                >
                  Back
                </Button>
              )}
              <Button
                onClick={handleNext}
                disabled={!canGoNext || isSubmitting}
                className="min-w-[100px]"
              >
                {isSubmitting ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current"></div>
                ) : currentStep === maxSteps ? (
                  "Submit Requirements"
                ) : (
                  <>
                    Next <MoveRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </DialogFooter>
          </>
        ) : (
          <div className="py-8 text-center space-y-4">
            <div className="flex justify-center">
              <CheckCircle2 className="h-16 w-16 text-green-500" />
            </div>
            <DialogTitle className="text-xl">
              {config.completedTitle || "Thank you!"}
            </DialogTitle>
            <DialogDescription>
              {config.completedDescription || "Your submission has been completed successfully."}
            </DialogDescription>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

// Utility hook for managing wizard state
export function useWizardState(initialStep: number = 1) {
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [isCompleted, setIsCompleted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const resetWizard = () => {
    setCurrentStep(1);
    setIsCompleted(false);
    setIsSubmitting(false);
  };

  const completeWizard = () => {
    setIsCompleted(true);
    setIsSubmitting(false);
  };

  const startSubmission = () => {
    setIsSubmitting(true);
  };

  return {
    currentStep,
    setCurrentStep,
    isCompleted,
    isSubmitting,
    resetWizard,
    completeWizard,
    startSubmission,
  };
}

// Utility functions for form validation
export const validateNumberInput = (
  value: string,
  setter: (value: number | string) => void,
  min: number = 0
) => {
  if (value === "") {
    setter("");
    return;
  }
  if (!/^\d*$/.test(value)) {
    return;
  }
  setter(value);
};

export const validateNumberOnBlur = (
  value: number | string,
  setter: (value: number) => void,
  min: number = 0
) => {
  const numValue = typeof value === "string" ? Number.parseInt(value) || 0 : value;
  setter(Math.max(min, numValue));
};
