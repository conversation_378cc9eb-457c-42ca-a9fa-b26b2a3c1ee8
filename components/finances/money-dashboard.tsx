"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  DollarSign,
  PlusCircle,
  MoreVertical,
  Edit,
  Trash,
  TrendingUp,
  TrendingDown,
  Target,
} from "lucide-react";
import { supabase } from "@/lib/supabase";
import { useAppStore } from "@/lib/store";
import { useToast } from "@/hooks/use-toast";
import { fetchGearItems } from "@/lib/queries/gearQueries";
import {
  fetchSavingsGoals,
  createSavingsGoal,
  updateSavingsGoal,
  deleteSavingsGoal,
  type SavingsGoal,
} from "@/lib/queries/savingsGoalQueries";

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
};

// SavingsGoal type is imported from savingsGoalQueries

export function MoneyDashboard() {
  const { currentProfile } = useAppStore();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [incomeTotal, setIncomeTotal] = useState(0);
  const [expenseTotal, setExpenseTotal] = useState(0);
  const [savingsGoals, setSavingsGoals] = useState<SavingsGoal[]>([]);
  const [gearItems, setGearItems] = useState<any[]>([]);
  const [isAddingGoal, setIsAddingGoal] = useState(false);
  const [editingGoal, setEditingGoal] = useState<SavingsGoal | null>(null);
  const [goalForm, setGoalForm] = useState({
    name: "",
    target_amount: "",
    current_amount: "",
    gear_id: "",
    notes: "",
    priority: "medium" as "low" | "medium" | "high",
    target_date: "",
    color: "#6366f1", // Default color (indigo)
  });

  // Load financial summary data
  const loadFinancialData = useCallback(async () => {
    try {
      setIsLoading(true);

      if (!currentProfile) throw new Error("No profile loaded");
      const { data: incomeData, error: incomeError } = await supabase
        .from("money")
        .select("amount")
        .eq("profile_id", currentProfile.id)
        .eq("transaction_type", "income");

      if (incomeError) throw incomeError;

      // Get expense total
      const { data: expenseData, error: expenseError } = await supabase
        .from("money")
        .select("amount")
        .eq("profile_id", currentProfile.id)
        .eq("transaction_type", "expense");

      if (expenseError) throw expenseError;

      // Calculate totals
      const incomeSum = incomeData.reduce(
        (sum, record) => sum + record.amount,
        0,
      );
      const expenseSum = expenseData.reduce(
        (sum, record) => sum + record.amount,
        0,
      );

      setIncomeTotal(incomeSum);
      setExpenseTotal(expenseSum);
    } catch (error) {
      console.error("Error loading financial data:", error);
      toast({
        title: "Error",
        description: "Failed to load financial data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentProfile?.id, toast]);

  // Load savings goals
  const loadSavingsGoals = useCallback(async () => {
    try {
      if (!currentProfile) throw new Error("No profile loaded");
      const data = await fetchSavingsGoals(currentProfile.id);
      setSavingsGoals(data || []);
    } catch (error) {
      console.error("Error loading savings goals:", error);
      toast({
        title: "Error",
        description: "Failed to load savings goals. Please try again.",
        variant: "destructive",
      });
    }
  }, [currentProfile?.id, toast]);

  // Load gear items for selection
  const loadGearItems = useCallback(async () => {
    try {
      if (!currentProfile) throw new Error("No profile loaded");
      const items = await fetchGearItems(currentProfile.id);
      setGearItems(items);
    } catch (error) {
      console.error("Error loading gear items:", error);
    }
  }, [currentProfile?.id]);

  // Load financial data and savings goals
  useEffect(() => {
    if (currentProfile) {
      loadFinancialData();
      loadSavingsGoals();
      loadGearItems();
    }
  }, [currentProfile, loadFinancialData, loadSavingsGoals, loadGearItems]);

  // Handle form input changes
  const handleFormChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >,
  ) => {
    const { name, value } = e.target;
    setGoalForm((prev) => ({ ...prev, [name]: value }));
  };

  // Reset form to default values
  const resetForm = () => {
    setGoalForm({
      name: "",
      target_amount: "",
      current_amount: "",
      gear_id: "",
      notes: "",
      priority: "medium",
      target_date: "",
      color: "#6366f1",
    });
    setEditingGoal(null);
  };

  // Open edit dialog with goal data
  const handleEditGoal = (goal: SavingsGoal) => {
    setEditingGoal(goal);
    // Safely parse metadata as it could be any JSON type
    const metadata = typeof goal.metadata === 'object' && goal.metadata !== null ? goal.metadata as any : {};
    setGoalForm({
      name: goal.name,
      target_amount: goal.target_amount.toString(),
      current_amount: goal.current_amount.toString(),
      gear_id: goal.gear_id || "",
      notes: metadata.notes || "",
      priority: metadata.priority || "medium",
      target_date: metadata.target_date || "",
      color: metadata.color || "#6366f1",
    });
    setIsAddingGoal(true);
  };

  // Handle saving a new or edited goal
  const handleSaveGoal = async () => {
    try {
      // Validate form
      if (!goalForm.name || !goalForm.target_amount) {
        toast({
          title: "Error",
          description: "Please fill in all required fields.",
          variant: "destructive",
        });
        return;
      }

      if (!currentProfile) throw new Error("No profile loaded");
      const goalData = {
        profile_id: currentProfile.id,
        name: goalForm.name,
        target_amount: parseFloat(goalForm.target_amount),
        current_amount: parseFloat(goalForm.current_amount) || 0,
        gear_id: goalForm.gear_id || null, // Keep as string to match database schema
        metadata: {
          notes: goalForm.notes,
          priority: goalForm.priority,
          target_date: goalForm.target_date,
          color: goalForm.color,
        },
      };

      if (editingGoal) {
        // Update existing goal
        await updateSavingsGoal(editingGoal.id, { ...goalData, profile_id: currentProfile.id });

        toast({
          title: "Goal Updated",
          description: "Your savings goal has been updated successfully.",
        });
      } else {
        // Create new goal
        await createSavingsGoal({ ...goalData, profile_id: currentProfile.id });

        toast({
          title: "Goal Added",
          description: "Your savings goal has been added successfully.",
        });
      }

      // Reset form and reload goals
      resetForm();
      setIsAddingGoal(false);
      loadSavingsGoals();
    } catch (error) {
      console.error("Error saving goal:", error);
      toast({
        title: "Error",
        description: "Failed to save savings goal. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle deleting a goal
  const handleDeleteGoal = async (goalId: string) => {
    try {
      await deleteSavingsGoal(goalId);

      toast({
        title: "Goal Deleted",
        description: "Your savings goal has been deleted successfully.",
      });

      // Reload goals
      loadSavingsGoals();
    } catch (error) {
      console.error("Error deleting goal:", error);
      toast({
        title: "Error",
        description: "Failed to delete savings goal. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Calculate net balance
  const netBalance = incomeTotal - expenseTotal;

  return (
    <div className="space-y-6">
      {/* Financial Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <TrendingUp className="mr-2 h-5 w-5 text-green-500" />
              Income
            </CardTitle>
            <CardDescription>Total income received</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">
              {formatCurrency(incomeTotal)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <TrendingDown className="mr-2 h-5 w-5 text-red-500" />
              Expenses
            </CardTitle>
            <CardDescription>Total expenses paid</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">
              {formatCurrency(expenseTotal)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <DollarSign className="mr-2 h-5 w-5" />
              Net Balance
            </CardTitle>
            <CardDescription>Current financial position</CardDescription>
          </CardHeader>
          <CardContent>
            <div
              className={`text-2xl font-bold ${netBalance >= 0 ? "text-green-500" : "text-red-500"}`}
            >
              {formatCurrency(netBalance)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Savings Goals */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center">
              <Target className="mr-2 h-5 w-5" />
              Savings Goals
            </CardTitle>
            <Dialog open={isAddingGoal} onOpenChange={setIsAddingGoal}>
              <DialogTrigger asChild>
                <Button
                  size="sm"
                  onClick={() => {
                    resetForm();
                    setIsAddingGoal(true);
                  }}
                >
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Goal
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>
                    {editingGoal ? "Edit Savings Goal" : "Add Savings Goal"}
                  </DialogTitle>
                  <DialogDescription>
                    {editingGoal
                      ? "Update your savings goal details."
                      : "Create a new savings goal to track your progress."}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Goal Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={goalForm.name}
                      onChange={handleFormChange}
                      placeholder="e.g., New Guitar, Studio Equipment, etc."
                      required
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="target_amount">Target Amount</Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2">
                          $
                        </span>
                        <Input
                          id="target_amount"
                          name="target_amount"
                          type="number"
                          step="0.01"
                          min="0"
                          value={goalForm.target_amount}
                          onChange={handleFormChange}
                          className="pl-7"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="current_amount">Current Savings</Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2">
                          $
                        </span>
                        <Input
                          id="current_amount"
                          name="current_amount"
                          type="number"
                          step="0.01"
                          min="0"
                          value={goalForm.current_amount}
                          onChange={handleFormChange}
                          className="pl-7"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="gear_id">
                      Link to Gear Item (Optional)
                    </Label>
                    <select
                      id="gear_id"
                      name="gear_id"
                      value={goalForm.gear_id}
                      onChange={handleFormChange}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="">None</option>
                      {gearItems.map((gear) => (
                        <option key={gear.id} value={gear.id}>
                          {gear.name} ({gear.brand} {gear.model})
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="priority">Priority</Label>
                      <select
                        id="priority"
                        name="priority"
                        value={goalForm.priority}
                        onChange={handleFormChange}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="target_date">
                        Target Date (Optional)
                      </Label>
                      <Input
                        id="target_date"
                        name="target_date"
                        type="date"
                        value={goalForm.target_date}
                        onChange={handleFormChange}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="color">Color</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        id="color"
                        name="color"
                        type="color"
                        value={goalForm.color}
                        onChange={handleFormChange}
                        className="w-12 h-10 p-1"
                      />
                      <div
                        className="w-full h-10 rounded-md border border-input"
                        style={{ backgroundColor: goalForm.color }}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes (Optional)</Label>
                    <Input
                      id="notes"
                      name="notes"
                      value={goalForm.notes}
                      onChange={handleFormChange}
                      placeholder="Any additional details about this goal..."
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => {
                      resetForm();
                      setIsAddingGoal(false);
                    }}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleSaveGoal}>
                    {editingGoal ? "Update Goal" : "Save Goal"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
          <CardDescription>
            Track your progress towards financial goals
          </CardDescription>
        </CardHeader>
        <CardContent>
          {savingsGoals.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              <p>You don't have any savings goals yet.</p>
              <p className="mt-2">
                <Button
                  variant="link"
                  onClick={() => {
                    resetForm();
                    setIsAddingGoal(true);
                  }}
                >
                  Create your first goal
                </Button>
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {savingsGoals.map((goal) => {
                const progress = Math.min(
                  Math.round((goal.current_amount / goal.target_amount) * 100),
                  100,
                );

                return (
                  <div key={goal.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div
                          className="w-4 h-4 rounded-full mr-2"
                          style={{
                            backgroundColor: (typeof goal.metadata === 'object' && goal.metadata !== null ? (goal.metadata as any).color : null) || "#6366f1",
                          }}
                        />
                        <h3 className="font-medium">{goal.name}</h3>
                        {(typeof goal.metadata === 'object' && goal.metadata !== null ? (goal.metadata as any).priority : null) === "high" && (
                          <span className="ml-2 text-xs bg-red-100 text-red-800 rounded-full px-2 py-0.5">
                            High Priority
                          </span>
                        )}
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleEditGoal(goal)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDeleteGoal(goal.id)}
                          >
                            <Trash className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    <Progress
                      value={progress}
                      className="h-2"
                      style={
                        {
                          "--progress-background":
                            (typeof goal.metadata === 'object' && goal.metadata !== null ? (goal.metadata as any).color : null) || "#6366f1",
                        } as React.CSSProperties
                      }
                    />

                    <div className="flex justify-between text-sm">
                      <span>
                        {formatCurrency(goal.current_amount)} of{" "}
                        {formatCurrency(goal.target_amount)}
                      </span>
                      <span className="font-medium">{progress}%</span>
                    </div>

                    {(typeof goal.metadata === 'object' && goal.metadata !== null ? (goal.metadata as any).target_date : null) && (
                      <div className="text-xs text-muted-foreground">
                        Target date:{" "}
                        {new Date(
                          (goal.metadata as any).target_date,
                        ).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
