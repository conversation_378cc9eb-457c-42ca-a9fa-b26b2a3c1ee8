"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  GearItem,
  GearSubItem,
  GearImage,
  GearFormData,
  GearPurpose,
} from "@/lib/types/gear";
import { checkSubitemLimits } from "@/lib/queries/gearQueries";
import { useToast } from "@/hooks/use-toast";
import { useAppStore } from "@/lib/store";
import { patchWithAuth } from "@/lib/utils/api";
import { Package, Guitar, Mic, Speaker, Truck, Laptop } from "lucide-react";
import { GearDetailContent } from "./gear-detail-content";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

interface GearDetailProps {
  gearId: string;
}

export function GearDetail({ gearId }: GearDetailProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { currentProfile } = useAppStore();
  const [gearItem, setGearItem] = useState<GearItem | null>(null);
  const [subitems, setSubitems] = useState<GearSubItem[]>([]);
  const [images, setImages] = useState<GearImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showAddSubitemDialog, setShowAddSubitemDialog] = useState(false);
  const [showAddImageDialog, setShowAddImageDialog] = useState(false);
  const [canAddSubitems, setCanAddSubitems] = useState(false);
  const [subitemLimits, setSubitemLimits] = useState({
    subitemCount: 0,
    maxSubitems: 0,
    isPaidUser: false,
  });

  // In-place editing states
  const [isEditing, setIsEditing] = useState(false);
  const [editSection, setEditSection] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [editFormData, setEditFormData] = useState<Partial<GearFormData>>({});

  // Helper function to format currency
  const formatCurrency = (amount: number | undefined) => {
    if (!amount) return "-";
    return new Intl.NumberFormat("en-AU", {
      style: "currency",
      currency: "AUD",
    }).format(amount);
  };

  // Helper function to get category icon
  const getCategoryIcon = (categoryId?: number) => {
    if (!categoryId) return <Package className="h-4 w-4" />;

    // Map category IDs to icons
    switch (categoryId) {
      case 1: // Instrument
        return <Guitar className="h-4 w-4" />;
      case 2: // Microphone
        return <Mic className="h-4 w-4" />;
      case 3: // Speaker
        return <Speaker className="h-4 w-4" />;
      case 4: // Transport
        return <Truck className="h-4 w-4" />;
      case 5: // Computer/Recording
        return <Laptop className="h-4 w-4" />;
      case 6: // Accessories
        return <Package className="h-4 w-4" />;
      case 7: // Transportation
        return <Truck className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  useEffect(() => {
    const loadGearData = async () => {
      try {
        setIsLoading(true);
        console.log(`Loading gear data for ID: ${gearId}`);

        // Fetch gear item details via the secured API route (enforces auth & RLS)
        try {
          let apiUrl = `/api/gear/${String(gearId)}`;
          if (typeof window !== "undefined") {
            apiUrl = `${window.location.origin}/api/gear/${String(gearId)}`;
          }

          console.log(`[GearDetail] Fetching gear item from ${apiUrl}`);
          const response = await fetch(apiUrl);

          if (!response.ok) {
            const errText = await response.text();
            throw new Error(
              `Failed to fetch gear item: ${response.status} ${errText}`,
            );
          }

          const gearData: GearItem = await response.json();
          console.log("Gear data loaded:", gearData);
          setGearItem(gearData);

          // Only proceed with other fetches if we have the main gear item
          try {
            // Fetch subitems via API
            let subUrl = `/api/gear/${String(gearId)}/subitems`;
            if (typeof window !== "undefined") {
              subUrl = `${window.location.origin}/api/gear/${String(gearId)}/subitems`;
            }
            const subRes = await fetch(subUrl);
            if (subRes.ok) {
              const subitemData = await subRes.json();
              setSubitems(subitemData);
            } else {
              console.warn(
                "[GearDetail] Unable to fetch subitems",
                await subRes.text(),
              );
              setSubitems([]);
            }
          } catch (subitemError) {
            console.error("Error fetching subitems:", subitemError);
            setSubitems([]);
          }

          try {
            // Fetch images via API
            let imgUrl = `/api/gear/${String(gearId)}/images`;
            if (typeof window !== "undefined") {
              imgUrl = `${window.location.origin}/api/gear/${String(gearId)}/images`;
            }
            const imgRes = await fetch(imgUrl);
            if (imgRes.ok) {
              const imageData = await imgRes.json();
              setImages(imageData);
            } else {
              console.warn(
                "[GearDetail] Unable to fetch images",
                await imgRes.text(),
              );
              setImages([]);
            }
          } catch (imageError) {
            console.error("Error fetching images:", imageError);
            setImages([]);
          }

          // Check subitem limits
          if (currentProfile) {
            try {
              const limits = await checkSubitemLimits(
                String(gearId),
                currentProfile.id,
              );
              setSubitemLimits({
                subitemCount: limits.subitemCount,
                maxSubitems: limits.maxSubitems,
                isPaidUser: limits.isPaidUser,
              });
              setCanAddSubitems(limits.canAddMoreSubitems && limits.isPaidUser);
            } catch (limitsError) {
              console.error("Error checking subitem limits:", limitsError);
              // Continue with default limits
            }
          }
        } catch (mainError) {
          console.error("Error fetching main gear item:", mainError);
          toast({
            title: "Error",
            description: "Failed to load gear details. Please try again.",
            variant: "destructive",
          });
          // Navigate back to gear list on critical error
          router.push("/gear");
        }
      } catch (error: any) {
        console.error("Failed to fetch gear data:", error);
        toast({
          title: "Error",
          description: "Failed to load gear details. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadGearData();
  }, [gearId, toast, currentProfile, router]);

  // Start editing a specific section
  const handleStartEditing = (section: string) => {
    if (!gearItem) return;

    // Initialize form data with current values
    setEditFormData({
      name: gearItem.name,
      brand: gearItem.brand || "",
      model: gearItem.model || "",
      description: gearItem.description || "",
      purchase_date: gearItem.purchase_date,
      purchase_price: gearItem.purchase_price,
      current_value: gearItem.current_value,
      ownership_status: gearItem.ownership_status,
      purpose: gearItem.purpose || [],
      storage_location: gearItem.storage_location || "",
      serial_number: gearItem.serial_number || "",
      notes: gearItem.notes || "",
    });

    setEditSection(section);
    setIsEditing(true);
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditSection(null);
    setIsEditing(false);
    setEditFormData({});
  };

  // Handle form field changes
  const handleEditChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setEditFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: any) => {
    setEditFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle date changes
  const handleDateChange = (date: Date | null, fieldName: string) => {
    setEditFormData((prev) => ({
      ...prev,
      [fieldName]: date ? date.toISOString() : undefined,
    }));
  };

  // Handle brand changes
  const handleBrandChange = (value: string) => {
    setEditFormData((prev) => ({ ...prev, brand: value }));
  };

  // Handle purpose changes (multi-select)
  const handlePurposeChange = (purpose: GearPurpose, checked: boolean) => {
    setEditFormData((prev) => {
      const currentPurposes = prev.purpose || [];
      if (checked) {
        return { ...prev, purpose: [...currentPurposes, purpose] };
      } else {
        return {
          ...prev,
          purpose: currentPurposes.filter((p) => p !== purpose),
        };
      }
    });
  };

  // Save changes
  const handleSaveChanges = async () => {
    if (!gearItem || !editSection) return;

    setIsSaving(true);

    try {
      // Use API route to update the gear item
      const apiUrl = `/api/gear/${String(gearId)}`;

      const response = await patchWithAuth(apiUrl, editFormData);

      if (!response.ok) {
        const errText = await response.text();
        throw new Error(
          `Failed to update gear item: ${response.status} ${errText}`,
        );
      }

      const updatedGear = await response.json();

      // Update the local state
      setGearItem(updatedGear);

      toast({
        title: "Success",
        description: "Gear item updated successfully.",
      });

      // Exit edit mode
      setIsEditing(false);
      setEditSection(null);
      setEditFormData({});
    } catch (error: any) {
      console.error("Error updating gear item:", error);
      toast({
        title: "Error",
        description:
          error.message || "Failed to update gear item. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteGear = async () => {
    if (!gearItem) return;

    setIsDeleting(true);

    try {
      // Use API route to delete the gear item (handles auth/RLS)
      let apiUrl = `/api/gear/${String(gearId)}`;
      if (typeof window !== "undefined") {
        apiUrl = `${window.location.origin}/api/gear/${String(gearId)}`;
      }

      const response = await fetch(apiUrl, { method: "DELETE" });

      if (!response.ok) {
        const errText = await response.text();
        throw new Error(
          `Failed to delete gear item: ${response.status} ${errText}`,
        );
      }

      toast({
        title: "Success",
        description: "Gear item deleted successfully.",
      });
      router.push("/gear");
    } catch (error: any) {
      console.error("Error deleting gear item:", error);
      toast({
        title: "Error",
        description:
          error.message || "Failed to delete gear item. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  const handleSubitemAdded = async (newSubitem: GearSubItem) => {
    setSubitems((prev) => [...prev, newSubitem]);
    setShowAddSubitemDialog(false);

    // Update subitem limits
    if (currentProfile) {
      const limits = await checkSubitemLimits(String(gearId), currentProfile.id);
      setSubitemLimits({
        subitemCount: limits.subitemCount,
        maxSubitems: limits.maxSubitems,
        isPaidUser: limits.isPaidUser,
      });
      setCanAddSubitems(limits.canAddMoreSubitems && limits.isPaidUser);
    }

    toast({
      title: "Success",
      description: "Subitem added successfully.",
    });
  };

  const handleImageAdded = (newImage: GearImage) => {
    setImages((prev) => [newImage, ...prev]);
    setShowAddImageDialog(false);

    toast({
      title: "Success",
      description: "Image uploaded successfully.",
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!gearItem) {
    return (
      <div className="text-center py-12">
        <Package className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-medium">Gear item not found</h3>
        <p className="mt-2 text-sm text-muted-foreground">
          The gear item you're looking for doesn't exist or you don't have
          permission to view it.
        </p>
        <Button className="mt-4" onClick={() => router.push("/gear")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Gear
        </Button>
      </div>
    );
  }

  return (
    <GearDetailContent
      gearItem={gearItem}
      subitems={subitems}
      images={images}
      isEditing={isEditing}
      editSection={editSection}
      editFormData={editFormData}
      isSaving={isSaving}
      canAddSubitems={canAddSubitems}
      subitemLimits={subitemLimits}
      showDeleteDialog={showDeleteDialog}
      showAddSubitemDialog={showAddSubitemDialog}
      showAddImageDialog={showAddImageDialog}
      isDeleting={isDeleting}
      onStartEditing={handleStartEditing}
      onCancelEdit={handleCancelEdit}
      onSaveChanges={handleSaveChanges}
      onDeleteGear={handleDeleteGear}
      onEditChange={handleEditChange}
      onSelectChange={handleSelectChange}
      onDateChange={handleDateChange}
      onBrandChange={handleBrandChange}
      onPurposeChange={handlePurposeChange}
      onShowDeleteDialog={setShowDeleteDialog}
      onShowAddSubitemDialog={setShowAddSubitemDialog}
      onShowAddImageDialog={setShowAddImageDialog}
      onSubitemAdded={handleSubitemAdded}
      onImageAdded={handleImageAdded}
      formatCurrency={formatCurrency}
      getCategoryIcon={getCategoryIcon}
    />
  );
}
