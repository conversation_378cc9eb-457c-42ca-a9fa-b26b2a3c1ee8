"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  GearFormData,
  GearCategory,
  GearOwnershipStatus,
  GearPurpose,
} from "@/lib/types/gear";
import {
  fetchGearCategories,
  createGearItem,
  updateGearItem,
  incrementBrandUsage,
} from "@/lib/queries/gearQueries";
import { BrandAutocomplete } from "@/components/gear/brand-autocomplete";
import { CategorySelector } from "@/components/gear/category-selector";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2, Save, ArrowLeft, ChevronRight } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useToast } from "@/hooks/use-toast";
import { useAppStore } from "@/lib/store";

interface GearFormProps {
  initialData?: GearFormData;
  gearId?: number;
  isEditing?: boolean;
}

export function GearForm({
  initialData,
  gearId,
  isEditing = false,
}: GearFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { currentUser } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [categories, setCategories] = useState<GearCategory[]>([]);

  const [formData, setFormData] = useState<GearFormData>({
    name: "",
    brand: "",
    model: "",
    description: "",
    category_id: undefined,
    subcategory_id: undefined,
    purchase_date: undefined,
    purchase_price: undefined,
    current_value: undefined,
    ownership_status: "owned",
    purpose: [],
    storage_location: "",
    serial_number: "",
    color: "",
    color_hex: "",
    size: "",
    manufactured_year: undefined,
    insurer: "",
    insured_amount: undefined,
    policy_number: "",
    tags: [],
    is_active: true,
    ...initialData,
  });

  // Track the selected subcategory name for auto-generating the item name
  const [selectedSubcategoryName, setSelectedSubcategoryName] =
    useState<string>("");

  // Track the selected brand ID separately (not part of the form data sent to the server)
  const [selectedBrandId, setSelectedBrandId] = useState<number | undefined>();

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const data = await fetchGearCategories();
        setCategories(data);
      } catch (error) {
        console.error("Failed to fetch gear categories:", error);
        toast({
          title: "Error",
          description: "Failed to load gear categories. Please try again.",
          variant: "destructive",
        });
      }
    };

    loadCategories();
  }, [toast]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Auto-generate name if color or model changes
    if (name === "color" || name === "model") {
      setTimeout(generateItemName, 0); // Use setTimeout to ensure formData is updated
    }
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value ? parseFloat(value) : undefined,
    }));
  };

  const handleSelectChange = (name: string, value: any) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleDateChange = (date: Date | null) => {
    setFormData((prev) => ({
      ...prev,
      purchase_date: date ? date.toISOString() : undefined,
    }));
  };

  const handleBrandChange = (brandName: string, brandId?: number) => {
    setFormData((prev) => ({ ...prev, brand: brandName }));
    setSelectedBrandId(brandId);

    // Auto-generate name when brand changes
    setTimeout(generateItemName, 0); // Use setTimeout to ensure formData is updated
  };

  const handleCategoryChange = (categoryId: number, subcategoryId?: number) => {
    // Find the subcategory name for auto-generating the item name
    let subcategoryName = "";
    if (subcategoryId) {
      const category = categories.find((c) => c.id === categoryId);
      if (category) {
        const subcategory = category.subcategories.find(
          (s) => s.id === subcategoryId,
        );
        if (subcategory) {
          subcategoryName = subcategory.name;
        }
      }
    }

    setSelectedSubcategoryName(subcategoryName);

    setFormData((prev) => ({
      ...prev,
      category_id: categoryId,
      subcategory_id: subcategoryId,
    }));

    // Auto-generate name if fields are filled
    generateItemName();
  };

  // Generate item name based on subcategory, color, brand, and model
  const generateItemName = () => {
    const parts = [];

    // Add subcategory
    if (selectedSubcategoryName) {
      parts.push(selectedSubcategoryName);
    }

    // Add color if it exists
    if (formData.color && formData.color.trim() !== "") {
      parts.push(formData.color.trim());
    }

    // Add brand if it exists
    if (formData.brand && formData.brand.trim() !== "") {
      parts.push(formData.brand.trim());
    }

    // Add model if it exists
    if (formData.model && formData.model.trim() !== "") {
      parts.push(formData.model.trim());
    }

    // Only update if we have at least one part
    if (parts.length > 0) {
      const generatedName = parts.join(" ");

      // Only update if the name hasn't been manually changed
      // or if it's empty or matches a previously generated name
      if (
        !formData.name ||
        formData.name.trim() === "" ||
        formData.name === formData._generatedName
      ) {
        setFormData((prev) => ({
          ...prev,
          name: generatedName,
          _generatedName: generatedName, // Track the generated name
        }));
      }

      return generatedName;
    }

    return "";
  };

  const handlePurposeChange = (purpose: GearPurpose, checked: boolean) => {
    setFormData((prev) => {
      const currentPurposes = [...prev.purpose];

      if (checked) {
        if (!currentPurposes.includes(purpose)) {
          currentPurposes.push(purpose);
        }
      } else {
        const index = currentPurposes.indexOf(purpose);
        if (index !== -1) {
          currentPurposes.splice(index, 1);
        }
      }

      return { ...prev, purpose: currentPurposes };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name) {
      toast({
        title: "Error",
        description: "Please provide a name for your gear item.",
        variant: "destructive",
      });
      return;
    }

    if (!currentUser) {
      toast({
        title: "Error",
        description: "You must be logged in to save gear items.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // If a brand was selected, increment its usage count
      if (selectedBrandId) {
        try {
          await incrementBrandUsage(selectedBrandId);
        } catch (error) {
          // Non-critical error, just log it
          console.error("Failed to increment brand usage count:", error);
        }
      }

      if (isEditing && gearId) {
        // Update existing gear item
        await updateGearItem(gearId, formData);
        toast({
          title: "Success",
          description: "Gear item updated successfully.",
        });
        router.push(`/gear/${gearId}`);
      } else {
        // Create new gear item
        const newGearItem = await createGearItem(formData, currentUser.id);
        toast({
          title: "Success",
          description: "Gear item created successfully.",
        });
        router.push(`/gear/${newGearItem.id}`);
      }
    } catch (error: any) {
      console.error("Error saving gear item:", error);

      // Check if it's a limit reached error
      if (error.message?.includes("limit reached")) {
        toast({
          title: "Limit Reached",
          description:
            "You've reached your gear item limit. Please upgrade to add more items.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description:
            error.message || "Failed to save gear item. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-6">
        <Button variant="ghost" onClick={() => router.back()} className="mb-2">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <h1 className="text-3xl font-bold">
          {isEditing ? "Edit Gear Item" : "Add New Gear Item"}
        </h1>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Gear Details</CardTitle>
            <CardDescription>
              Enter the details of your gear item. Fields marked with * are
              required.
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="e.g., Fender Stratocaster"
                  />
                </div>

                <CategorySelector
                  selectedCategoryId={formData.category_id}
                  selectedSubcategoryId={formData.subcategory_id}
                  brandId={selectedBrandId}
                  onCategoryChange={handleCategoryChange}
                  className="space-y-2"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="brand">Brand</Label>
                  <BrandAutocomplete
                    value={formData.brand || ""}
                    onChange={handleBrandChange}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="model">Model</Label>
                  <Input
                    id="model"
                    name="model"
                    value={formData.model || ""}
                    onChange={handleChange}
                    placeholder="e.g., American Professional II"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="color">Color</Label>
                  <Input
                    id="color"
                    name="color"
                    value={formData.color || ""}
                    onChange={handleChange}
                    placeholder="e.g., Sunburst, Black, Natural"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="purchase_date">Purchase Date</Label>
                  <DatePicker
                    date={
                      formData.purchase_date
                        ? new Date(formData.purchase_date)
                        : undefined
                    }
                    setDate={handleDateChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ownership_status">Ownership Status</Label>
                  <Select
                    value={formData.ownership_status}
                    onValueChange={(value) =>
                      handleSelectChange(
                        "ownership_status",
                        value as GearOwnershipStatus,
                      )
                    }
                  >
                    <SelectTrigger id="ownership_status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="owned">Owned</SelectItem>
                      <SelectItem value="leased">Leased</SelectItem>
                      <SelectItem value="financed">Financed</SelectItem>
                      <SelectItem value="borrowed">Borrowed</SelectItem>
                      <SelectItem value="rented">Rented</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="purchase_price">Purchase Price</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2">
                      $
                    </span>
                    <Input
                      id="purchase_price"
                      name="purchase_price"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.purchase_price || ""}
                      onChange={handleNumberChange}
                      className="pl-7"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="current_value">Current Value</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2">
                      $
                    </span>
                    <Input
                      id="current_value"
                      name="current_value"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.current_value || ""}
                      onChange={handleNumberChange}
                      className="pl-7"
                    />
                  </div>
                </div>
              </div>

              {/* Advanced Section */}
              <div className="mt-8">
                <Collapsible>
                  <CollapsibleTrigger asChild>
                    <div className="flex items-center space-x-2 cursor-pointer">
                      <div className="h-px flex-1 bg-border"></div>
                      <Button variant="ghost" className="gap-1">
                        <ChevronRight className="h-4 w-4" />
                        Advanced Options
                      </Button>
                      <div className="h-px flex-1 bg-border"></div>
                    </div>
                  </CollapsibleTrigger>

                  <CollapsibleContent className="mt-4 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="serial_number">Serial Number</Label>
                        <Input
                          id="serial_number"
                          name="serial_number"
                          value={formData.serial_number || ""}
                          onChange={handleChange}
                          placeholder="e.g., **********"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="storage_location">
                          Storage Location
                        </Label>
                        <Input
                          id="storage_location"
                          name="storage_location"
                          value={formData.storage_location || ""}
                          onChange={handleChange}
                          placeholder="e.g., Home Studio, Rehearsal Space"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        value={formData.description || ""}
                        onChange={handleChange}
                        placeholder="Describe your gear item..."
                        rows={3}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="notes">Notes</Label>
                      <Textarea
                        id="notes"
                        name="notes"
                        value={formData.notes || ""}
                        onChange={handleChange}
                        placeholder="Additional notes about your gear..."
                        rows={3}
                      />
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>

              <div className="space-y-2">
                <Label>Purpose</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="purpose_learning"
                      checked={formData.purpose.includes("learning")}
                      onCheckedChange={(checked) =>
                        handlePurposeChange("learning", checked as boolean)
                      }
                    />
                    <Label
                      htmlFor="purpose_learning"
                      className="cursor-pointer"
                    >
                      Learning
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="purpose_teaching"
                      checked={formData.purpose.includes("teaching")}
                      onCheckedChange={(checked) =>
                        handlePurposeChange("teaching", checked as boolean)
                      }
                    />
                    <Label
                      htmlFor="purpose_teaching"
                      className="cursor-pointer"
                    >
                      Teaching
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="purpose_performing_live"
                      checked={formData.purpose.includes("performing_live")}
                      onCheckedChange={(checked) =>
                        handlePurposeChange(
                          "performing_live",
                          checked as boolean,
                        )
                      }
                    />
                    <Label
                      htmlFor="purpose_performing_live"
                      className="cursor-pointer"
                    >
                      Live Performance
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="purpose_performing_studio"
                      checked={formData.purpose.includes("performing_studio")}
                      onCheckedChange={(checked) =>
                        handlePurposeChange(
                          "performing_studio",
                          checked as boolean,
                        )
                      }
                    />
                    <Label
                      htmlFor="purpose_performing_studio"
                      className="cursor-pointer"
                    >
                      Studio Recording
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="purpose_performing_all"
                      checked={formData.purpose.includes("performing_all")}
                      onCheckedChange={(checked) =>
                        handlePurposeChange(
                          "performing_all",
                          checked as boolean,
                        )
                      }
                    />
                    <Label
                      htmlFor="purpose_performing_all"
                      className="cursor-pointer"
                    >
                      All Performance
                    </Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes || ""}
                  onChange={handleChange}
                  placeholder="Additional notes about this gear item..."
                  rows={3}
                />
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {isEditing ? "Update" : "Save"}
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}
