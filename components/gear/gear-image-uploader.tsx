"use client";

import { useState, useRef } from "react";
import Image from "next/image";
import { GearImage, GearImageUpload } from "@/lib/types/gear";
import { uploadGearImage, checkImageLimits } from "@/lib/queries/gearQueries";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { ImageIcon, Upload, X, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAppStore } from "@/lib/store";

interface GearImageUploaderProps {
  gearId?: string;
  subitemId?: number;
  onSuccess: (image: GearImage) => void;
  onCancel: () => void;
}

export function GearImageUploader({
  gearId,
  subitemId,
  onSuccess,
  onCancel,
}: GearImageUploaderProps) {
  const { toast } = useToast();
  const { currentUser } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [canAddImage, setCanAddImage] = useState(true);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [imageUpload, setImageUpload] = useState<GearImageUpload>({
    file: null as unknown as File,
    description: "",
    is_manufacturer_image: false,
    tags: {},
  });

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];

    // Check if file is an image
    if (!file.type.startsWith("image/")) {
      toast({
        title: "Invalid File",
        description: "Please select an image file (JPEG, PNG, etc.).",
        variant: "destructive",
      });
      return;
    }

    // Check file size (limit to 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File Too Large",
        description: "Please select an image smaller than 5MB.",
        variant: "destructive",
      });
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = () => {
      setImagePreview(reader.result as string);
    };
    reader.readAsDataURL(file);

    // Update state
    setImageUpload((prev) => ({ ...prev, file }));

    // Check if user can add more images
    if (currentUser) {
      setIsChecking(true);
      try {
        const limits = await checkImageLimits(
          gearId,
          subitemId,
          currentUser.id,
        );
        setCanAddImage(limits.canAddMoreImages);

        if (!limits.canAddMoreImages) {
          toast({
            title: "Image Limit Reached",
            description: `You've reached your limit of ${limits.maxImages} images for this ${gearId ? "gear item" : "subitem"}.`,
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error checking image limits:", error);
      } finally {
        setIsChecking(false);
      }
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setImageUpload((prev) => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setImageUpload((prev) => ({ ...prev, is_manufacturer_image: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log("[Uploader] handleSubmit", {
      currentUser,
      gearId,
      canAddImage,
    });

    if (!imageUpload.file) {
      toast({
        title: "Error",
        description: "Please select an image to upload.",
        variant: "destructive",
      });
      return;
    }

    if (!currentUser) {
      console.warn(
        "[Uploader] No currentUser - must be logged in to upload images",
      );
      toast({
        title: "Error",
        description: "You must be logged in to upload images.",
        variant: "destructive",
      });
      return;
    }

    if (!canAddImage) {
      toast({
        title: "Image Limit Reached",
        description:
          "You've reached your image limit. Please upgrade to add more images.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // Build FormData for API route
      const formData = new FormData();
      formData.append("file", imageUpload.file);
      formData.append("description", imageUpload.description || "");
      formData.append(
        "is_manufacturer_image",
        String(imageUpload.is_manufacturer_image),
      );
      formData.append("tags", JSON.stringify(imageUpload.tags || {}));

      // POST to API route (not direct to Supabase)
      const resp = await fetch(`/api/gear/${gearId}/images`, {
        method: "POST",
        body: formData,
      });

      if (!resp.ok) {
        const errorData = await resp
          .json()
          .catch(() => ({ error: "Failed to upload image" }));
        throw new Error(errorData.error || "Failed to upload image");
      }

      const uploadedImage = await resp.json();
      onSuccess(uploadedImage);
    } catch (error: any) {
      console.error("Error uploading image via API:", error);
      toast({
        title: "Error",
        description:
          error.message || "Failed to upload image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const clearSelectedFile = () => {
    setImagePreview(null);
    setImageUpload((prev) => ({ ...prev, file: null as unknown as File }));
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {!imagePreview ? (
        <Card className="border-dashed border-2 cursor-pointer hover:bg-muted/50 transition-colors">
          <CardContent
            className="flex flex-col items-center justify-center py-6"
            onClick={() => fileInputRef.current?.click()}
          >
            <ImageIcon className="h-10 w-10 text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground text-center">
              Click to select an image, or drag and drop
              <br />
              PNG, JPG or GIF up to 5MB
            </p>
            <Input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleFileChange}
            />
          </CardContent>
        </Card>
      ) : (
        <div className="relative">
          <div className="aspect-video relative rounded-md overflow-hidden">
            <Image
              src={imagePreview}
              alt="Preview"
              fill
              className="object-contain"
            />
          </div>
          <Button
            type="button"
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full"
            onClick={clearSelectedFile}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          value={imageUpload.description || ""}
          onChange={handleChange}
          placeholder="Describe this image..."
          rows={2}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="is_manufacturer_image"
          checked={imageUpload.is_manufacturer_image}
          onCheckedChange={handleCheckboxChange}
        />
        <Label htmlFor="is_manufacturer_image" className="cursor-pointer">
          This is a manufacturer/official image (not my own photo)
        </Label>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading || isChecking}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={
            isLoading || isChecking || !imageUpload.file || !canAddImage
          }
        >
          {isLoading || isChecking ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {isChecking ? "Checking..." : "Uploading..."}
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Upload Image
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
