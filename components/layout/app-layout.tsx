"use client";

import React from "react";
import { Sidebar } from "./sidebar";
import { usePathname } from "next/navigation";
import { AdminStatusBar } from "./admin-status-bar";

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  const pathname = usePathname();
  const isAuthPage =
    pathname?.startsWith("/auth") ||
    pathname?.startsWith("/signup-clean") ||
    pathname === "/new-profile" ||
    false;
  const isPublicProfilePage = pathname?.startsWith("/u/") || false;

  // For public profile pages, render a simple layout without any auth components
  if (isPublicProfilePage) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-amber-50">
        <main className="w-full">{children}</main>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      <AdminStatusBar />
      <div className="flex flex-1">
        {!isAuthPage && <Sidebar />}
        <main
          className={`flex-1 ${isAuthPage ? "p-0 md:p-0" : "p-4 md:p-6"}${!isAuthPage ? " md:ml-64" : ""}`}
        >
          {children}
        </main>
      </div>
    </div>
  );
}
