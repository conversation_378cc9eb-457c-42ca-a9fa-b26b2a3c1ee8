"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import {
  AssignmentType,
  RecurrencePattern,
} from "@/lib/types/practice-assignment";
import { SongSelector } from "@/components/practice/song-selector";
import { SimpleScaleSelector } from "@/components/practice/simple-scale-selector";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  duration_minutes: z
    .number()
    .min(1, "Duration must be at least 1 minute")
    .optional(),
  due_date: z.date().optional(),
  type: z.enum(["practice", "song", "scales", "rudiments", "other"]),
  song_id: z.number().optional(),
  recurring: z.boolean().default(false),
  recurrence_pattern: z
    .enum(["daily", "weekly", "weekdays", "weekends"])
    .optional(),
  assigned_by_type: z.enum(["parent", "teacher"]).default("parent"),
});

type FormValues = z.infer<typeof formSchema>;

interface PracticeAssignmentFormProps {
  childId: string;
  childUsername: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  defaultValues?: Partial<FormValues>;
  isEditing?: boolean;
  assignmentId?: string;
}

export function PracticeAssignmentForm({
  childId,
  childUsername,
  onSuccess,
  onCancel,
  defaultValues,
  isEditing = false,
  assignmentId,
}: PracticeAssignmentFormProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedType, setSelectedType] = useState<AssignmentType>(
    defaultValues?.type || "practice",
  );
  const [selectedSongId, setSelectedSongId] = useState<number | undefined>(
    defaultValues?.song_id,
  );
  const [selectedScales, setSelectedScales] = useState<any[]>([]);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: defaultValues?.title || "",
      description: defaultValues?.description || "",
      duration_minutes: defaultValues?.duration_minutes || 15,
      due_date: defaultValues?.due_date
        ? new Date(defaultValues.due_date)
        : undefined,
      type: defaultValues?.type || "practice",
      song_id: defaultValues?.song_id,
      recurring: defaultValues?.recurring || false,
      recurrence_pattern: defaultValues?.recurrence_pattern,
      assigned_by_type: defaultValues?.assigned_by_type || "parent",
    },
  });

  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);

      // Prepare the metadata based on the assignment type
      let metadata: Record<string, any> = {};

      if (values.type === "scales" && selectedScales.length > 0) {
        metadata.scales = selectedScales;
      }

      // Prepare the API endpoint and method
      const method = isEditing ? "PUT" : "POST";
      const endpoint = isEditing
        ? `/api/children/${childUsername}/practice/assignments/${assignmentId}`
        : `/api/children/${childUsername}/practice/assignments`;

      // Prepare the request body
      const requestBody: any = {
        title: values.title,
        description: values.description,
        duration_minutes: values.duration_minutes,
        due_date: values.due_date
          ? format(values.due_date, "yyyy-MM-dd")
          : undefined,
        type: values.type,
        recurring: values.recurring,
        recurrence_pattern: values.recurring
          ? values.recurrence_pattern
          : undefined,
        assigned_by_type: values.assigned_by_type,
        metadata,
      };

      // Add song_id if the type is song
      if (values.type === "song" && selectedSongId) {
        requestBody.song_id = selectedSongId;
      }

      // Make the API request
      // Use relative URL to ensure it works regardless of port
      console.log("Submitting practice assignment to:", endpoint);
      console.log("Request data:", requestBody);

      const response = await fetch(endpoint, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      let data;
      try {
        const text = await response.text();
        try {
          data = JSON.parse(text);
        } catch (parseError) {
          console.error("Error parsing response as JSON:", parseError);
          console.error("Response text:", text);
          throw new Error(
            `Server returned invalid JSON: ${text.substring(0, 100)}...`,
          );
        }
      } catch (error) {
        console.error("Error reading response:", error);
        throw new Error("Failed to read server response");
      }

      if (!response.ok) {
        throw new Error(data.error || "Failed to save assignment");
      }

      toast({
        title: isEditing ? "Assignment Updated" : "Assignment Created",
        description: isEditing
          ? "The practice assignment has been updated successfully."
          : "A new practice assignment has been created.",
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error("Error saving practice assignment:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to save assignment",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle assignment type change
  const handleTypeChange = (value: string) => {
    setSelectedType(value as AssignmentType);
    form.setValue("type", value as AssignmentType);
  };

  // Handle song selection
  const handleSongSelect = (songId: number) => {
    setSelectedSongId(songId);
    form.setValue("song_id", songId);
  };

  // Handle scales selection
  const handleScalesChange = (scales: any[]) => {
    setSelectedScales(scales);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input placeholder="Practice assignment title" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Provide details about this assignment"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Assignment Type</FormLabel>
              <Select
                onValueChange={handleTypeChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select assignment type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="practice">General Practice</SelectItem>
                  <SelectItem value="song">Specific Song</SelectItem>
                  <SelectItem value="scales">Scales</SelectItem>
                  <SelectItem value="rudiments">Rudiments</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {selectedType === "practice" && (
          <FormField
            control={form.control}
            name="duration_minutes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Duration (minutes)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min={1}
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {selectedType === "song" && (
          <div className="space-y-4">
            <FormLabel>Select Song</FormLabel>
            <SongSelector
              songs={[]}
              isLoading={false}
              selectedSongId={selectedSongId ?? null}
              setSelectedSongId={setSelectedSongId}
              selectedSongParts={[]}
              setSelectedSongParts={() => {}}
              setSongs={() => {}}
              showSongParts={false}
            />
            {!selectedSongId && (
              <p className="text-sm text-red-500">Please select a song</p>
            )}
          </div>
        )}

        {selectedType === "scales" && (
          <div className="space-y-4">
            <FormLabel>Select Scales</FormLabel>
            <SimpleScaleSelector onScalesChange={handleScalesChange} />
          </div>
        )}

        <FormField
          control={form.control}
          name="due_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Due Date</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground",
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    value={field.value}
                    onChange={field.onChange}
                    disabled={(date) => date < new Date()}
                    minDate={new Date()}
                    showNavigation={true}
                    showNeighboringMonth={true}
                    view="month"
                    className="border-0"
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="recurring"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Recurring Assignment</FormLabel>
                <FormDescription>
                  This assignment will repeat according to the selected pattern
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        {form.watch("recurring") && (
          <FormField
            control={form.control}
            name="recurrence_pattern"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Recurrence Pattern</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select recurrence pattern" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="weekdays">Weekdays</SelectItem>
                    <SelectItem value="weekends">Weekends</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <div className="flex justify-end space-x-2">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? "Saving..."
              : isEditing
                ? "Update Assignment"
                : "Create Assignment"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
