import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import {
  Profile,
  ProfileType,
  CreateProfileParams,
  UpdateProfileParams,
  InstrumentCapability,
} from "@/lib/types/profile";
import {
  createProfile,
  updateProfile,
} from "@/lib/queries/profileBasedQueries";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Pop<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "@/components/ui/use-toast";

// Schema for profile form validation
const profileFormSchema = z.object({
  username: z
    .string()
    .min(3, { message: "Username must be at least 3 characters" })
    .max(30, { message: "Username must be at most 30 characters" })
    .regex(/^[a-z0-9_]+$/, {
      message:
        "Username can only contain lowercase letters, numbers, and underscores",
    }),
  display_name: z
    .string()
    .min(2, { message: "Display name must be at least 2 characters" })
    .max(50, { message: "Display name must be at most 50 characters" }),
  profile_type: z.enum([
    "personal",
    "child",
    "band",
    "studio",
    "teacher",
    "other",
  ] as const),
  dob: z.date().optional(),
  avatar_url: z
    .string()
    .url({ message: "Please enter a valid URL" })
    .optional()
    .or(z.literal("")),
  color_scheme: z.string().optional(),
  bio: z
    .string()
    .max(500, { message: "Bio must be at most 500 characters" })
    .optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

interface ProfileFormProps {
  existingProfile?: Profile;
  onSuccess?: (profile: Profile) => void;
  onCancel?: () => void;
  initialValues?: {
    profile_type?: ProfileType;
    display_name?: string;
    username?: string;
    dob?: Date;
  };
}

const ProfileForm: React.FC<ProfileFormProps> = ({
  existingProfile,
  onSuccess,
  onCancel,
  initialValues,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [instruments, setInstruments] = useState<InstrumentCapability[]>(
    existingProfile?.metadata?.instruments || [],
  );
  const isEditing = !!existingProfile;

  // Initialize form with default values, existing profile data, or initial values
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      username: existingProfile?.username || initialValues?.username || "",
      display_name:
        existingProfile?.display_name || initialValues?.display_name || "",
      profile_type:
        (existingProfile?.profile_type as ProfileType) ||
        initialValues?.profile_type ||
        "personal",
      dob: existingProfile?.dob
        ? new Date(existingProfile.dob)
        : initialValues?.dob,
      avatar_url: existingProfile?.avatar_url || "",
      color_scheme: existingProfile?.color_scheme || "",
      bio: existingProfile?.metadata?.bio || "",
    },
  });

  // Disable username field when editing
  useEffect(() => {
    if (isEditing) {
      form.register("username", { disabled: true });
    }
  }, [form, isEditing]);

  const handleAddInstrument = () => {
    setInstruments([
      ...instruments,
      { instrument: "", proficiency: "beginner" },
    ]);
  };

  const handleRemoveInstrument = (index: number) => {
    setInstruments(instruments.filter((_, i) => i !== index));
  };

  const handleInstrumentChange = (
    index: number,
    field: keyof InstrumentCapability,
    value: any,
  ) => {
    const newInstruments = [...instruments];
    newInstruments[index] = {
      ...newInstruments[index],
      [field]: value,
    };
    setInstruments(newInstruments);
  };

  const onSubmit = async (values: ProfileFormValues) => {
    setIsSubmitting(true);
    try {
      let profile: Profile;

      // Prepare metadata
      const metadata = {
        bio: values.bio,
        instruments: instruments,
        roles: existingProfile?.metadata?.roles || [],
        login_permission: existingProfile?.metadata?.login_permission || "none",
      };

      if (isEditing) {
        // Update existing profile
        const updateData: UpdateProfileParams = {
          display_name: values.display_name,
          avatar_url: values.avatar_url || null,
          color_scheme: values.color_scheme || null,
          dob: values.dob ? format(values.dob, "yyyy-MM-dd") : null,
          metadata: {
            ...existingProfile.metadata,
            ...metadata,
          },
        };

        profile = await updateProfile(existingProfile.id, updateData);
        toast({
          title: "Profile updated",
          description: `Successfully updated profile "${values.display_name}"`,
        });
      } else {
        // Create new profile
        const createData: CreateProfileParams = {
          username: values.username,
          display_name: values.display_name,
          profile_type: values.profile_type,
          avatar_url: values.avatar_url,
          color_scheme: values.color_scheme,
          dob: values.dob ? format(values.dob, "yyyy-MM-dd") : undefined,
          metadata,
        };

        profile = await createProfile(createData);
        toast({
          title: "Profile created",
          description: `Successfully created profile "${values.display_name}"`,
        });
      }

      if (onSuccess) {
        onSuccess(profile);
      }
    } catch (error) {
      console.error("Error saving profile:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to save profile",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Username</FormLabel>
                <FormControl>
                  <Input
                    placeholder="username"
                    {...field}
                    disabled={isEditing}
                  />
                </FormControl>
                <FormDescription>
                  This will be your unique identifier. Cannot be changed later.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="display_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Display Name</FormLabel>
                <FormControl>
                  <Input placeholder="Display Name" {...field} />
                </FormControl>
                <FormDescription>
                  This is how you'll be known in the app.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="profile_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Profile Type</FormLabel>
                <Select
                  disabled={isEditing}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a profile type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="personal">Personal</SelectItem>
                    <SelectItem value="child">Child</SelectItem>
                    <SelectItem value="band">Band</SelectItem>
                    <SelectItem value="studio">Studio</SelectItem>
                    <SelectItem value="teacher">Teacher</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  This determines how your profile will be categorized.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="dob"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Date of Birth</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground",
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      value={field.value}
                      onChange={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                      minDate={new Date("1900-01-01")}
                      maxDate={new Date()}
                    />
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  Required for child profiles. Optional for others.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="avatar_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Avatar URL</FormLabel>
                <FormControl>
                  <Input
                    placeholder="https://example.com/avatar.jpg"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  URL to your profile picture. Leave blank to use default.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="color_scheme"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Color Scheme</FormLabel>
                <FormControl>
                  <Input placeholder="purple" {...field} />
                </FormControl>
                <FormDescription>
                  Your preferred color theme. Leave blank for default.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="bio"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Bio</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Tell us a bit about yourself or this profile"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                A short description for this profile. Max 500 characters.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Instrument Capabilities Section */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Instrument Capabilities</h3>
            <Button
              type="button"
              variant="outline"
              onClick={handleAddInstrument}
            >
              Add Instrument
            </Button>
          </div>

          {instruments.map((instrument, index) => (
            <div
              key={index}
              className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg"
            >
              <FormItem>
                <FormLabel>Instrument</FormLabel>
                <FormControl>
                  <Input
                    value={instrument.instrument}
                    onChange={(e) =>
                      handleInstrumentChange(
                        index,
                        "instrument",
                        e.target.value,
                      )
                    }
                    placeholder="e.g., Guitar, Piano"
                  />
                </FormControl>
              </FormItem>

              <FormItem>
                <FormLabel>Proficiency</FormLabel>
                <Select
                  value={instrument.proficiency}
                  onValueChange={(value) =>
                    handleInstrumentChange(index, "proficiency", value)
                  }
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select proficiency" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                    <SelectItem value="expert">Expert</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>

              <div className="flex items-end">
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => handleRemoveInstrument(index)}
                >
                  Remove
                </Button>
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-end space-x-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? "Saving..."
              : isEditing
                ? "Update Profile"
                : "Create Profile"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default ProfileForm;
