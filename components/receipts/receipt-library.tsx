"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { format } from "date-fns";
import {
  Receipt as ReceiptIcon,
  Plus,
  Search,
  Filter,
  Calendar,
  Tag,
  Loader2,
  X,
  ChevronRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { fetchReceipts } from "@/lib/queries/receiptQueries";
import { useAppStore } from "@/lib/store";
import type { Receipt } from "@/lib/types/receipt";

export function ReceiptLibrary() {
  const router = useRouter();
  const { toast } = useToast();
  const { currentProfile } = useAppStore();
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [filteredReceipts, setFilteredReceipts] = useState<Receipt[]>([]);

  // Load receipts
  useEffect(() => {
    const loadReceipts = async () => {
      if (!currentProfile) {
        console.log("[ReceiptLibrary] No current profile, skipping receipt fetch");
        return;
      }

      try {
        setIsLoading(true);
        console.log("[ReceiptLibrary] Fetching receipts for profile:", currentProfile.id);
        const data = await fetchReceipts();
        console.log("[ReceiptLibrary] Fetched receipts:", data.length);
        setReceipts(data);
        setFilteredReceipts(data);
      } catch (error: any) {
        console.error("Failed to fetch receipts:", error);
        const errorMessage = error.message || "Failed to load receipts. Please try again.";
        toast({
          title: "Error Loading Receipts",
          description: errorMessage,
          variant: "destructive",
        });
        // Set empty arrays to show empty state instead of loading forever
        setReceipts([]);
        setFilteredReceipts([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadReceipts();
  }, [currentProfile, toast]);

  // Filter receipts when search query or categories change
  useEffect(() => {
    let filtered = receipts;

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (receipt) =>
          receipt.merchant.toLowerCase().includes(query) ||
          receipt.category.toLowerCase().includes(query),
      );
    }

    // Filter by categories
    if (selectedCategories.length > 0) {
      filtered = filtered.filter((receipt) =>
        selectedCategories.includes(receipt.category),
      );
    }

    setFilteredReceipts(filtered);
  }, [receipts, searchQuery, selectedCategories]);

  // Get unique categories
  const categories = [...new Set(receipts.map((receipt) => receipt.category))];

  // Toggle category selection
  const toggleCategory = (category: string) => {
    setSelectedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category],
    );
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  // Group receipts by month
  const groupReceiptsByMonth = (receipts: Receipt[]) => {
    const grouped: Record<string, Receipt[]> = {};

    receipts.forEach((receipt) => {
      const date = new Date(receipt.date);
      const monthYear = format(date, "MMMM yyyy");

      if (!grouped[monthYear]) {
        grouped[monthYear] = [];
      }

      grouped[monthYear].push(receipt);
    });

    return Object.entries(grouped).map(([monthYear, receipts]) => ({
      monthYear,
      receipts,
    }));
  };

  const groupedReceipts = groupReceiptsByMonth(filteredReceipts);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search receipts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <div className="flex items-center space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
                {selectedCategories.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {selectedCategories.length}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {categories.map((category) => (
                <DropdownMenuCheckboxItem
                  key={category}
                  checked={selectedCategories.includes(category)}
                  onCheckedChange={() => toggleCategory(category)}
                >
                  <span className="capitalize">{category}</span>
                </DropdownMenuCheckboxItem>
              ))}
              {selectedCategories.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full mt-2"
                  onClick={() => setSelectedCategories([])}
                >
                  Clear Filters
                </Button>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={() => router.push("/receipts/capture")}>
            <Plus className="h-4 w-4 mr-2" />
            Capture Receipt
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : filteredReceipts.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12 text-center">
            <ReceiptIcon className="h-12 w-12 text-muted-foreground mb-4" />
            {receipts.length === 0 ? (
              <>
                <h3 className="text-lg font-semibold mb-2">
                  No receipts found
                </h3>
                <p className="text-muted-foreground mb-4">
                  You haven't captured any receipts yet.
                </p>
                <Button onClick={() => router.push("/receipts/capture")}>
                  <Plus className="h-4 w-4 mr-2" />
                  Capture Your First Receipt
                </Button>
              </>
            ) : (
              <>
                <h3 className="text-lg font-semibold mb-2">
                  No matching receipts
                </h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your search or filters.
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery("");
                    setSelectedCategories([]);
                  }}
                >
                  <X className="h-4 w-4 mr-2" />
                  Clear Filters
                </Button>
              </>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-8">
          {groupedReceipts.map((group) => (
            <div key={group.monthYear}>
              <h3 className="text-lg font-medium mb-4 flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-muted-foreground" />
                {group.monthYear}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {group.receipts.map((receipt) => (
                  <Card
                    key={receipt.id}
                    className="overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => router.push(`/receipts/${receipt.id}`)}
                  >
                    <div className="relative">
                      {receipt.image_url ? (
                        <div className="h-40 overflow-hidden bg-muted">
                          <Image
                            src={receipt.image_url}
                            alt={receipt.merchant}
                            width={400}
                            height={160}
                            className="w-full h-full object-cover"
                            style={{ objectFit: 'cover' }}
                          />
                        </div>
                      ) : (
                        <div className="h-40 flex items-center justify-center bg-muted">
                          <ReceiptIcon className="h-12 w-12 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium truncate">
                            {receipt.merchant}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {format(new Date(receipt.date), "MMM d, yyyy")}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">
                            {formatCurrency(receipt.total)}
                          </p>
                          <Badge variant="outline" className="capitalize mt-1">
                            {receipt.category}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="p-4 pt-0 flex justify-end">
                      <Button variant="ghost" size="sm" className="text-xs">
                        View Details
                        <ChevronRight className="h-3 w-3 ml-1" />
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
