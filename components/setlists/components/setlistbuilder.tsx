"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Music, ListMusic } from "lucide-react";
import { formatDurationMs, calculateTotalDuration } from "@/lib/date-utils";
import { SetlistEntry } from "./SetlistEntry";
import { AddItemDialog } from "./AddItemDialog";
import type { SetlistItem } from "@/lib/types/setlist";
import type { Song } from "@/lib/queries/songQueries";

interface SetlistBuilderProps {
  entries: SetlistItem[];
  onAddBreak: (title: string, duration: number) => void;
  onRemoveEntry: (index: number) => void;
  onReorderEntries: (startIndex: number, endIndex: number) => void;
  onEditEntry: (index: number) => void;
  onEnrichWithSpotify: () => void;
  isEnrichingSpotify: boolean;
  // Song search props
  songQuery: string;
  onSongQueryChange: (query: string) => void;
  onSearchSongs: (query: string) => void;
  isSearching: boolean;
  availableSongs: Song[];
  onSelectSong: (song: Song) => void;
  onAddManualSong: (title: string, artist: string, duration: number) => void;
}

export function SetlistBuilder({
  entries,
  onAddBreak,
  onRemoveEntry,
  onReorderEntries,
  onEditEntry,
  onEnrichWithSpotify,
  isEnrichingSpotify,
  songQuery,
  onSongQueryChange,
  onSearchSongs,
  isSearching,
  availableSongs,
  onSelectSong,
  onAddManualSong,
}: SetlistBuilderProps) {
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [draggedIdx, setDraggedIdx] = useState<number | null>(null);

  const durationCompatibleEntries = entries.map((entry) => {
    if (entry.type === "song") {
      // Try multiple sources for duration
      const durationMs = entry.song?.duration_ms ||
                        (entry.song?.duration ? entry.song.duration * 1000 : 0) ||
                        (entry.duration ? entry.duration * 1000 : 0);
      return { duration_ms: durationMs };
    } else if (entry.type !== "song" && entry.duration) {
      return { duration_ms: entry.duration * 1000 };
    }
    return { duration_ms: 0 };
  });

  return (
    <Card className="mt-6">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            <ListMusic className="h-5 w-5" />
            Setlist Builder
          </CardTitle>
          <div className="flex gap-2">
            <span className="text-sm text-muted-foreground">
              Total: {formatDurationMs(calculateTotalDuration(durationCompatibleEntries))}
            </span>
            <Button
              onClick={onEnrichWithSpotify}
              size="sm"
              variant="outline"
              className="flex items-center gap-2"
              disabled={isEnrichingSpotify}
            >
              {isEnrichingSpotify ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              ) : (
                <Music className="h-4 w-4" />
              )}
              {isEnrichingSpotify ? "Enriching..." : "Enrich with Spotify"}
            </Button>
            <Button size="sm" onClick={() => setIsAddingItem(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {entries.length === 0 ? (
          <div className="text-center py-8">
            <Music className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground mb-4">
              No items in this setlist yet. Add songs or breaks to get started.
            </p>
            <Button onClick={() => setIsAddingItem(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Item
            </Button>
          </div>
        ) : (
          <div className="space-y-2">
            {entries.map((entry, index) => (
              <SetlistEntry
                key={`${entry.type}-${entry.order}-${index}`}
                entry={entry}
                index={index}
                onEdit={() => onEditEntry(index)}
                onDelete={() => onRemoveEntry(index)}
                onDragStart={() => setDraggedIdx(index)}
                onDragOver={(e) => {
                  e.preventDefault();
                }}
                onDrop={(e) => {
                  e.preventDefault();
                  if (draggedIdx !== null && draggedIdx !== index) {
                    onReorderEntries(draggedIdx, index);
                  }
                  setDraggedIdx(null);
                }}
              />
            ))}
          </div>
        )}
      </CardContent>

      <AddItemDialog
        open={isAddingItem}
        onOpenChange={setIsAddingItem}
        songQuery={songQuery}
        onSongQueryChange={onSongQueryChange}
        onSearch={onSearchSongs}
        isSearching={isSearching}
        availableSongs={availableSongs}
        onSelectSong={onSelectSong}
        onAddManualSong={onAddManualSong}
        onAddBreak={onAddBreak}
      />
    </Card>
  );
}