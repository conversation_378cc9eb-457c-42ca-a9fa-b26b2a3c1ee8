"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import {
  ChevronDown,
  ChevronUp,
  Coffee,
  Mic,
  Plus,
  Trash2,
  Music,
  GripVertical,
} from "lucide-react";
// import { SetlistFlowChart } from "@/components/setlist-flow-chart" // This will be in the same directory
import type {
  SetlistItem,
  NonPerformanceType,
} from "@/lib/types/setlist-wizard"; // Will point to new types file
import type { Song } from "@/lib/types/setlist-wizard";
import { formatDuration } from "@/lib/utils/format-duration"; // Use existing util

interface SetlistBuilderProps {
  setlist: SetlistItem[];
  onAddNonPerformanceItem: (
    type: NonPerformanceType,
    duration?: number,
  ) => void;
  onUpdateItem: (id: string, updates: Partial<SetlistItem>) => void;
  onRemoveItem: (id: string) => void;
  onReorderSetlist: (newSetlist: SetlistItem[]) => void;
}

export function SetlistBuilder({
  setlist,
  onAddNonPerformanceItem,
  onUpdateItem,
  onRemoveItem,
  onReorderSetlist,
}: SetlistBuilderProps) {
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
    {},
  );

  const toggleExpand = (id: string) => {
    setExpandedItems((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(setlist);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    onReorderSetlist(items);
  };

  const totalDuration = setlist.reduce((total, item) => {
    if (item.type === "song" && item.song) {
      // For songs, prioritize song.duration_ms, then song.duration, then item.duration
      const durationMs = item.song.duration_ms || (item.song.duration ? item.song.duration * 1000 : 0) || (item.duration ? item.duration * 1000 : 0);
      return total + Math.round(durationMs / 1000); // Convert to seconds
    } else {
      // For non-song items, use item.duration directly (should be in seconds)
      return total + (item.duration || 0);
    }
  }, 0);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Setlist Builder</CardTitle>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              Total: {formatDuration(totalDuration)}
            </span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-1" /> Add Item
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem
                  onClick={() => onAddNonPerformanceItem("break", 300)}
                >
                  <Coffee className="h-4 w-4 mr-2" /> Break
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onAddNonPerformanceItem("speech", 180)}
                >
                  <Mic className="h-4 w-4 mr-2" /> Speech
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onAddNonPerformanceItem("other", 120)}
                >
                  <Plus className="h-4 w-4 mr-2" /> Other
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          {setlist.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              Your setlist is empty. Add songs from the library or use the
              generator.
            </div>
          ) : (
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="setlist">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="space-y-2"
                  >
                    {setlist.map((item, index) => (
                      <Draggable
                        key={item.id}
                        draggableId={item.id}
                        index={index}
                      >
                        {(provided) => (
                          <Collapsible
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            open={expandedItems[item.id]}
                            className="border rounded-md overflow-hidden"
                          >
                            <div
                              className={`flex items-center p-3 bg-card ${item.type !== "song" ? "py-2 bg-muted/20" : ""}`}
                            >
                              <div
                                {...provided.dragHandleProps}
                                className="mr-2 cursor-grab"
                              >
                                <GripVertical className="h-5 w-5 text-muted-foreground" />
                              </div>

                              {item.type === "song" && item.song ? (
                                <div className="flex-1">
                                  <div className="font-medium flex items-center">
                                    <Music className="h-4 w-4 mr-2 text-muted-foreground" />
                                    {item.song.title}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {item.song.artist}
                                  </div>
                                </div>
                              ) : (
                                <div className="flex-1">
                                  <div className="font-medium flex items-center text-muted-foreground">
                                    {item.type === "break" && (
                                      <Coffee className="h-4 w-4 mr-2" />
                                    )}
                                    {item.type === "speech" && (
                                      <Mic className="h-4 w-4 mr-2" />
                                    )}
                                    {item.type === "other" && (
                                      <Plus className="h-4 w-4 mr-2" />
                                    )}
                                    {item.title}
                                  </div>
                                </div>
                              )}

                              <div className="text-sm text-muted-foreground mr-4">
                                {item.type === "song" && item.song
                                  ? formatDuration(Math.round((item.song.duration_ms || (item.song.duration ? item.song.duration * 1000 : 0) || 0) / 1000))
                                  : formatDuration(item.duration || 0)
                                }
                              </div>

                              <CollapsibleTrigger
                                asChild
                                onClick={() => toggleExpand(item.id)}
                              >
                                <Button variant="ghost" size="sm">
                                  {expandedItems[item.id] ? (
                                    <ChevronUp className="h-4 w-4" />
                                  ) : (
                                    <ChevronDown className="h-4 w-4" />
                                  )}
                                </Button>
                              </CollapsibleTrigger>

                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onRemoveItem(item.id)}
                              >
                                <Trash2 className="h-4 w-4 text-destructive" />
                              </Button>
                            </div>

                            <CollapsibleContent>
                              <div className="p-4 pt-0 border-t bg-muted/30 space-y-3">
                                {item.type === "song" && item.song ? (
                                  <>
                                    <div className="grid grid-cols-2 gap-4">
                                      <div>
                                        <Label htmlFor={`duration-${item.id}`}>
                                          Duration (seconds)
                                        </Label>
                                        <Input
                                          id={`duration-${item.id}`}
                                          type="number"
                                          value={Math.round((item.duration || (item.song?.duration_ms ? item.song.duration_ms / 1000 : 0) || (item.song?.duration || 0)) / 1000)}
                                          onChange={(e) => {
                                            const durationSeconds = Number.parseInt(e.target.value) || 0;
                                            const durationMs = durationSeconds * 1000;
                                            onUpdateItem(item.id, {
                                              duration: durationMs,
                                              song: item.song ? {
                                                ...item.song,
                                                duration_ms: durationMs,
                                                duration: durationSeconds
                                              } : undefined
                                            });
                                          }}
                                          className="mt-1"
                                          placeholder="Duration in seconds"
                                        />
                                      </div>
                                      <div>
                                        <Label htmlFor={`key-${item.id}`}>
                                          Key
                                        </Label>
                                        <Input
                                          id={`key-${item.id}`}
                                          value={item.song.key?.toString() || ""}
                                          onChange={(e) =>
                                            onUpdateItem(item.id, {
                                              song: {
                                                ...item.song!,
                                                key: Number.parseInt(e.target.value) || 0,
                                              } as Song,
                                            })
                                          }
                                          className="mt-1"
                                        />
                                      </div>
                                    </div>
                                    <div>
                                      <Label htmlFor={`lineup-${item.id}`}>
                                        Lineup
                                      </Label>
                                      <Input
                                        id={`lineup-${item.id}`}
                                        value={item.lineup || ""}
                                        onChange={(e) =>
                                          onUpdateItem(item.id, {
                                            lineup: e.target.value,
                                          })
                                        }
                                        className="mt-1"
                                        placeholder="Who's playing on this song"
                                      />
                                    </div>
                                  </>
                                ) : (
                                  <>
                                    <div>
                                      <Label htmlFor={`title-${item.id}`}>
                                        Title
                                      </Label>
                                      <Input
                                        id={`title-${item.id}`}
                                        value={item.title || ""}
                                        onChange={(e) =>
                                          onUpdateItem(item.id, {
                                            title: e.target.value,
                                          })
                                        }
                                        className="mt-1"
                                      />
                                    </div>
                                    <div>
                                      <Label htmlFor={`duration-${item.id}`}>
                                        Duration (seconds)
                                      </Label>
                                      <Input
                                        id={`duration-${item.id}`}
                                        type="number"
                                        value={item.duration}
                                        onChange={(e) =>
                                          onUpdateItem(item.id, {
                                            duration:
                                              Number.parseInt(e.target.value) ||
                                              0,
                                          })
                                        }
                                        className="mt-1"
                                      />
                                    </div>
                                  </>
                                )}
                                <div>
                                  <Label htmlFor={`notes-${item.id}`}>
                                    Notes
                                  </Label>
                                  <Textarea
                                    id={`notes-${item.id}`}
                                    value={item.notes || ""}
                                    onChange={(e) =>
                                      onUpdateItem(item.id, {
                                        notes: e.target.value,
                                      })
                                    }
                                    className="mt-1"
                                    placeholder="Add any notes or reminders"
                                  />
                                </div>
                                {item.type === "song" && (
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <Label htmlFor={`purpose-${item.id}`}>
                                        Purpose
                                      </Label>
                                      <Input
                                        id={`purpose-${item.id}`}
                                        value={item.purpose || ""}
                                        onChange={(e) =>
                                          onUpdateItem(item.id, {
                                            purpose: e.target.value,
                                          })
                                        }
                                        className="mt-1"
                                        placeholder="e.g. Opener, Closer, Energy boost"
                                      />
                                    </div>
                                    <div>
                                      <Label htmlFor={`tags-${item.id}`}>
                                        Tags
                                      </Label>
                                      <Input
                                        id={`tags-${item.id}`}
                                        // value={item.tags ? (Array.isArray(item.tags) ? item.tags.join(", ") : item.tags) : ""} // Handle string or array
                                        value={
                                          Array.isArray(item.tags)
                                            ? item.tags.join(", ")
                                            : item.tags || ""
                                        }
                                        onChange={(e) =>
                                          onUpdateItem(item.id, {
                                            tags: e.target.value,
                                          })
                                        }
                                        className="mt-1"
                                        placeholder="Comma separated tags"
                                      />
                                    </div>
                                  </div>
                                )}
                              </div>
                            </CollapsibleContent>
                          </Collapsible>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          )}
        </CardContent>
      </Card>

      {/* <SetlistFlowChart setlist={setlist} /> */}
      {/* SetlistFlowChart will be added later once its file is created */}
    </div>
  );
}
