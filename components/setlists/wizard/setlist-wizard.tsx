"use client";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import { DragDropContext, type OnDragEndResponder } from "@hello-pangea/dnd";
import { SongLibrary } from "@/components/setlists/wizard/song-library";
import { SetlistGenerator } from "@/components/setlists/wizard/setlist-generator";
import { SetlistBuilder } from "@/components/setlists/wizard/setlist-builder";
// import { SetlistFlowChart } from '@/components/setlists/wizard/setlist-flow-chart'; // Import when ready
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  saveSetlist,
  fetchSongsForSetlistWizard,
} from "@/lib/actions/setlist-wizard-actions"; // To be created
import type {
  Song,
  SetlistItem as SetlistItemWizard,
  Setlist as SetlistWizardType,
} from "@/lib/types/setlist-wizard"; // To be created
import { useAppStore } from "@/lib/store"; // Corrected path
import { useRouter, usePathname } from "next/navigation";
import { toast } from "sonner"; // Or your preferred toast library
import { calculateTotalDuration } from "@/lib/date-utils"; // For name generation

export function SetlistWizard() {
  const router = useRouter();
  const pathname = usePathname();
  const profile = useAppStore((state) => state.currentProfile);
  const storeAuthIsLoading = useAppStore((state) => state.isLoading);

  const [allSongs, setAllSongs] = useState<Song[]>([]);
  const [selectedLibSongs, setSelectedLibSongs] = useState<Song[]>([]);
  const [currentSetlistItems, setCurrentSetlistItems] = useState<
    SetlistItemWizard[]
  >([]);
  const [setlistName, setSetlistName] = useState("");
  const [isLoadingSongs, setIsLoadingSongs] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [initialFetchDone, setInitialFetchDone] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [activeTab, setActiveTab] = useState("library");
  const [showUnsavedChangesDialog, setShowUnsavedChangesDialog] =
    useState(false);
  const [nextTab, setNextTab] = useState<string | null>(null);
  const [showNameSuggestionModal, setShowNameSuggestionModal] = useState(false);
  const [suggestedName, setSuggestedName] = useState("");

  useEffect(() => {
    const userId = profile?.id;
    console.log(
      "[SetlistWizard useEffect] Store Auth Loading:",
      storeAuthIsLoading,
      "Profile:",
      profile,
      "User ID:",
      userId,
      "Initial Fetch Done:",
      initialFetchDone,
    );

    if (!storeAuthIsLoading && userId && !initialFetchDone) {
      console.log(
        `[SetlistWizard useEffect] Auth loaded, user ID present (${userId}), initial fetch not done. Fetching songs...`,
      );
      setIsLoadingSongs(true);
      setInitialFetchDone(true);

      fetchSongsForSetlistWizard(userId)
        .then((songsData) => {
          console.log(
            "[SetlistWizard useEffect] Songs data received from action:",
            songsData,
          );
          if (songsData && Array.isArray(songsData)) {
            setAllSongs(songsData);
            console.log(
              `[SetlistWizard useEffect] ${songsData.length} songs set to state.`,
            );
          }
        })
        .catch((error) => {
          console.error(
            "[SetlistWizard useEffect] Error fetching songs:",
            error,
          );
          setAllSongs([]);
        })
        .finally(() => {
          setIsLoadingSongs(false);
          console.log(
            "[SetlistWizard useEffect] Finished fetching songs, isLoadingSongs set to false.",
          );
        });
    } else if (!storeAuthIsLoading && !userId && !initialFetchDone) {
      console.warn(
        "[SetlistWizard useEffect] Auth loaded but no user ID found. Cannot fetch songs. Marking initial fetch attempt as done to prevent loops.",
      );
      setIsLoadingSongs(false);
      setAllSongs([]);
      setInitialFetchDone(true);
    } else if (initialFetchDone) {
      console.log(
        "[SetlistWizard useEffect] Initial fetch attempt already made. Skipping.",
      );
    } else {
      console.log(
        "[SetlistWizard useEffect] Waiting for auth to load or user ID to be present for initial fetch.",
      );
    }
  }, [storeAuthIsLoading, profile, initialFetchDone]);

  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (isDirty) {
        event.preventDefault();
        event.returnValue = "";
      }
    };
    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [isDirty]);

  const handleSetlistNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSetlistName(e.target.value);
    setIsDirty(true);
  };

  const handleSongSelect = (song: Song) => {
    setSelectedLibSongs((prev) =>
      prev.find((s) => s.id === song.id) ? prev : [...prev, song],
    );
  };

  const handleSongDeselect = (songId: string) => {
    setSelectedLibSongs((prev) => prev.filter((s) => s.id !== songId));
  };

  const handleAddSelectedToSetlist = () => {
    const newItems: SetlistItemWizard[] = selectedLibSongs.map((song) => ({
      id: `setlist-item-${Date.now()}-${song.id}`,
      type: "song",
      song: {
        ...song,
        // Ensure we have a reference to the live song data
        song_id: song.id, // Link to the actual song record
        title: song.title,
        artist: song.artist,
        duration_ms: song.duration_ms || (song.duration ? song.duration * 1000 : 0),
        // Include Spotify metadata if available
        valence: song.valence,
        danceability: song.danceability,
        energy: song.energy,
        bpm: song.bpm,
        key: song.key,
        mode: song.mode,
        popularity: song.popularity,
        spotify_id: song.spotify_id,
      },
      title: song.title, // Keep for display but reference song.title for live updates
      artist: song.artist, // Keep for display but reference song.artist for live updates
      duration: song.duration_ms || (song.duration ? song.duration * 1000 : 0),
      notes: "",
    }));
    setCurrentSetlistItems((prev) => [...prev, ...newItems]);
    setSelectedLibSongs([]);
    toast.success(`${newItems.length} song(s) added to builder.`);
    setIsDirty(true);
  };

  const handleGeneratedSetlist = (setlist: SetlistItemWizard[]) => {
    setCurrentSetlistItems(setlist);
    toast.success("Setlist generated and added to builder!");
    setIsDirty(true);
  };

  const onDragEndSetlistBuilder: OnDragEndResponder = (result) => {
    const { source, destination } = result;
    if (!destination) return;

    if (
      source.droppableId === "setlist-builder-droppable" &&
      destination.droppableId === "setlist-builder-droppable"
    ) {
      const items = Array.from(currentSetlistItems);
      const [reorderedItem] = items.splice(source.index, 1);
      items.splice(destination.index, 0, reorderedItem);
      setCurrentSetlistItems(items);
      toast.info("Setlist order updated.");
      setIsDirty(true);
    }
  };

  const handleAddItemToSetlist = (
    type: SetlistItemWizard["type"],
    duration?: number,
  ) => {
    const newItem: SetlistItemWizard = {
      id: `${type}-${Date.now()}`,
      type: type,
      title: type.charAt(0).toUpperCase() + type.slice(1),
      duration:
        duration ||
        (type === "break" ? 300000 : type === "speech" ? 180000 : 120000),
      notes: "",
    };
    if (type === "song") {
      console.warn(
        "Attempted to add 'song' type item directly through SetlistBuilder add. This is unusual.",
      );
      return;
    }
    setCurrentSetlistItems((prev) => [...prev, newItem]);
    toast.success(`${newItem.title} added to setlist.`);
    setIsDirty(true);
  };

  const handleUpdateSetlistItem = (
    id: string,
    updates: Partial<SetlistItemWizard>,
  ) => {
    setCurrentSetlistItems((prev) =>
      prev.map((item) => (item.id === id ? { ...item, ...updates } : item)),
    );
    setIsDirty(true);
  };

  const handleRemoveSetlistItem = (id: string) => {
    setCurrentSetlistItems((prev) => prev.filter((item) => item.id !== id));
    toast.success("Item removed from setlist.");
    setIsDirty(true);
  };

  const generateSuggestedSetlistName = useCallback(
    (items: SetlistItemWizard[]): string => {
      if (items.length === 0) return "New Setlist";

      let totalDurationMs = 0;
      let songCount = 0;
      const genres: Record<string, number> = {};
      const artists: Record<string, number> = {};

      items.forEach((item) => {
        if (item.duration) {
          totalDurationMs += item.duration;
        }
        if (item.type === "song" && item.song) {
          songCount++;
        }
      });

      const totalMinutes = Math.round(totalDurationMs / 60000);

      let suggestion = "";
      if (totalMinutes > 5) suggestion += `Approx. ${totalMinutes}min `;
      if (songCount > 0) suggestion += `${songCount}-Song `;

      suggestion += "Set";
      if (suggestion.trim() === "Set") return "My Awesome Setlist";

      return suggestion.trim();
    },
    [],
  );

  const proceedWithSave = async (nameToSave: string) => {
    if (!profile?.id) {
      toast.error("You must be logged in to save a setlist.");
      return;
    }
    if (currentSetlistItems.length === 0) {
      toast.warning("Cannot save an empty setlist.");
      return;
    }

    const finalSetlistName =
      nameToSave.trim() === ""
        ? generateSuggestedSetlistName(currentSetlistItems)
        : nameToSave;

    const setlistDataToSave = {
      name: finalSetlistName,
      list_items: currentSetlistItems.map((item, index) => ({
        ...item,
        order: index,
        songId:
          item.type === "song" && item.song?.id
            ? parseInt(item.song.id)
            : undefined,
        title:
          item.title ||
          (item.type === "song" && item.song?.title) ||
          "Untitled Item",
        artist:
          item.type === "song" && item.song?.artist
            ? item.song.artist
            : undefined,
        duration: item.duration,
      })),
      creator_profile_id: profile.id,
    };

    try {
      toast.info(`Saving setlist "${finalSetlistName}"...`);
      const result = await saveSetlist(setlistDataToSave as any);

      if (result && result.id) {
        toast.success(`Setlist "${finalSetlistName}" saved successfully!`);
        setCurrentSetlistItems([]);
        setSetlistName("");
        setIsDirty(false);
        router.push(`/setlists/${result.slug || result.id}`);
      } else if (result && result.error) {
        console.error("Failed to save setlist:", result.error);
        toast.error(`Failed to save setlist: ${result.error}`);
      } else {
        console.error(
          "Failed to save setlist: Unexpected response from server action",
          result,
        );
        toast.error(
          "Failed to save setlist. Please try again. Unexpected response.",
        );
      }
    } catch (error) {
      console.error("Error calling saveSetlist action:", error);
      toast.error("Failed to save setlist due to a network or server error.");
    }
  };

  const handleSaveSetlist = async () => {
    if (setlistName.trim() === "" && currentSetlistItems.length > 0) {
      const autoName = generateSuggestedSetlistName(currentSetlistItems);
      setSuggestedName(autoName);
      setShowNameSuggestionModal(true);
    } else {
      await proceedWithSave(setlistName);
    }
  };

  const handleTabChange = (value: string) => {
    if (isDirty && activeTab === "build" && value !== "build") {
      setNextTab(value);
      setShowUnsavedChangesDialog(true);
    } else {
      setActiveTab(value);
    }
  };

  if (storeAuthIsLoading || isLoadingSongs) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>Loading Setlist Wizard...</p>
      </div>
    );
  }

  if (!profile?.id) {
    return (
      <div className="flex flex-col justify-center items-center h-64 text-center">
        <p className="text-lg font-semibold">User profile not loaded.</p>
        <p className="text-sm text-muted-foreground">
          Please ensure you are logged in to use the Setlist Wizard.
        </p>
      </div>
    );
  }

  return (
    <DragDropContext onDragEnd={onDragEndSetlistBuilder}>
      <div className="container mx-auto p-4 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Setlist Wizard</h1>
          <Button
            onClick={handleSaveSetlist}
            disabled={
              currentSetlistItems.length === 0 && setlistName.trim() === ""
            }
          >
            Save Setlist
          </Button>
        </div>

        <Input
          type="text"
          value={setlistName}
          onChange={handleSetlistNameChange}
          placeholder="My Awesome Gig Name"
          className="text-xl font-semibold p-2 border-b-2 border-primary focus:outline-none focus:border-ring w-full md:w-1/2"
        />

        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-3 mb-4">
            <TabsTrigger value="library">Song Library</TabsTrigger>
            <TabsTrigger value="generate">Auto-Generate</TabsTrigger>
            <TabsTrigger value="build">Build & Order</TabsTrigger>
          </TabsList>

          <TabsContent value="library">
            {error && allSongs.length > 0 && (
              <p className="text-red-500 mb-2">
                Error loading all songs, showing available data: {error}
              </p>
            )}
            <SongLibrary
              allSongs={allSongs}
              selectedSongs={selectedLibSongs}
              onSongSelect={handleSongSelect}
              onSongDeselect={handleSongDeselect}
              onAddSelectedToSetlist={handleAddSelectedToSetlist}
            />
          </TabsContent>

          <TabsContent value="generate">
            <SetlistGenerator
              songs={allSongs}
              onGenerate={handleGeneratedSetlist}
            />
          </TabsContent>

          <TabsContent value="build">
            <SetlistBuilder
              setlist={currentSetlistItems}
              onAddNonPerformanceItem={handleAddItemToSetlist}
              onUpdateItem={handleUpdateSetlistItem}
              onRemoveItem={handleRemoveSetlistItem}
              onReorderSetlist={(newSetlist) => {
                setCurrentSetlistItems(newSetlist);
                toast.info("Setlist order updated.");
                setIsDirty(true);
              }}
            />
          </TabsContent>
        </Tabs>

        <AlertDialog
          open={showUnsavedChangesDialog}
          onOpenChange={setShowUnsavedChangesDialog}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Unsaved Changes</AlertDialogTitle>
              <AlertDialogDescription>
                You have unsaved changes in the "Build & Order" tab. If you
                leave now, these changes will be lost.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setNextTab(null)}>
                Stay on Build Tab
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  if (nextTab) {
                    setActiveTab(nextTab);
                    setNextTab(null);
                    setIsDirty(false);
                  }
                }}
                className="bg-destructive hover:bg-destructive/90 text-destructive-foreground"
              >
                Discard Changes & Leave
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        <AlertDialog
          open={showNameSuggestionModal}
          onOpenChange={setShowNameSuggestionModal}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Name Your Setlist</AlertDialogTitle>
              <AlertDialogDescription>
                We've suggested a name for your setlist:{" "}
                <strong>{suggestedName}</strong>
                <br />
                You can use this name or create your own.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <Input
              type="text"
              value={setlistName || suggestedName}
              onChange={handleSetlistNameChange}
              placeholder="Enter setlist name"
              className="my-4"
            />
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel Save</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => proceedWithSave(setlistName || suggestedName)}
              >
                Save Setlist
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </DragDropContext>
  );
}
