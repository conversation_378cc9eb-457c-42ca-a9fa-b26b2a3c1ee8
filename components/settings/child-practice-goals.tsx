"use client";

import { useState, useEffect } from "react";
import { Profile } from "@/lib/types/profile";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Target, Clock, Calendar, Save, RotateCcw } from "lucide-react";
import { toast } from "sonner";

interface PracticeGoals {
  weeklyDays: number;
  specificDays: string[];
  minutesPerSession: number;
  weeklyMinutes: number;
  useSpecificDays: boolean;
}

interface ChildPracticeGoalsProps {
  profile: Profile;
  onProfileUpdate?: (updatedProfile: Profile) => void;
}

const DAYS_OF_WEEK = [
  { key: "monday", label: "Monday", short: "Mon" },
  { key: "tuesday", label: "Tuesday", short: "Tue" },
  { key: "wednesday", label: "Wednesday", short: "Wed" },
  { key: "thursday", label: "Thursday", short: "Thu" },
  { key: "friday", label: "Friday", short: "Fri" },
  { key: "saturday", label: "Saturday", short: "Sat" },
  { key: "sunday", label: "Sunday", short: "Sun" },
];

export function ChildPracticeGoals({
  profile,
  onProfileUpdate,
}: ChildPracticeGoalsProps) {
  const [goals, setGoals] = useState<PracticeGoals>({
    weeklyDays: 3,
    specificDays: [],
    minutesPerSession: 30,
    weeklyMinutes: 150,
    useSpecificDays: false,
  });
  const [isLoading, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Load existing goals from profile metadata
  useEffect(() => {
    if (profile.metadata?.practiceGoals) {
      const existingGoals = profile.metadata.practiceGoals;
      setGoals({
        weeklyDays: existingGoals.weeklyDays || 3,
        specificDays: existingGoals.specificDays || [],
        minutesPerSession: existingGoals.minutesPerSession || 30,
        weeklyMinutes: existingGoals.weeklyMinutes || 150,
        useSpecificDays: existingGoals.useSpecificDays || false,
      });
    }
  }, [profile.metadata]);

  const handleGoalChange = (field: keyof PracticeGoals, value: any) => {
    setGoals((prev) => {
      const updated = { ...prev, [field]: value };

      // Auto-calculate weekly minutes when days or session length changes
      if (field === "weeklyDays" || field === "minutesPerSession") {
        updated.weeklyMinutes = updated.weeklyDays * updated.minutesPerSession;
      }

      // If switching to specific days, ensure we have the right number selected
      if (field === "useSpecificDays" && value) {
        if (updated.specificDays.length !== updated.weeklyDays) {
          updated.specificDays = updated.specificDays.slice(
            0,
            updated.weeklyDays,
          );
        }
      }

      return updated;
    });
    setHasChanges(true);
  };

  const handleSpecificDayToggle = (dayKey: string) => {
    setGoals((prev) => {
      const isSelected = prev.specificDays.includes(dayKey);
      let newSpecificDays;

      if (isSelected) {
        newSpecificDays = prev.specificDays.filter((d) => d !== dayKey);
      } else {
        if (prev.specificDays.length < prev.weeklyDays) {
          newSpecificDays = [...prev.specificDays, dayKey];
        } else {
          // Replace the first day if we're at the limit
          newSpecificDays = [...prev.specificDays.slice(1), dayKey];
        }
      }

      return { ...prev, specificDays: newSpecificDays };
    });
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // Update the profile metadata with the new goals
      const updatedMetadata = {
        ...profile.metadata,
        practiceGoals: goals,
      };

      const response = await fetch(`/api/profiles/${profile.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          metadata: updatedMetadata,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update practice goals");
      }

      const updatedProfile = await response.json();

      if (onProfileUpdate) {
        onProfileUpdate(updatedProfile);
      }

      setHasChanges(false);
      toast.success("Practice goals updated successfully!");
    } catch (error) {
      console.error("Error updating practice goals:", error);
      toast.error("Failed to update practice goals");
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    setGoals({
      weeklyDays: 3,
      specificDays: [],
      minutesPerSession: 30,
      weeklyMinutes: 150,
      useSpecificDays: false,
    });
    setHasChanges(true);
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} minutes`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0
      ? `${hours}h ${remainingMinutes}m`
      : `${hours} hour${hours > 1 ? "s" : ""}`;
  };

  return (
    <div className="space-y-6">
      {/* Weekly Practice Days */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Weekly Practice Schedule
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="weeklyDays">How many days per week?</Label>
            <div className="flex items-center space-x-4 mt-2">
              <Input
                id="weeklyDays"
                type="number"
                min="1"
                max="7"
                value={goals.weeklyDays}
                onChange={(e) =>
                  handleGoalChange("weeklyDays", parseInt(e.target.value) || 1)
                }
                className="w-20"
              />
              <span className="text-sm text-muted-foreground">
                days per week
              </span>
            </div>
          </div>

          <Separator />

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Switch
                id="useSpecificDays"
                checked={goals.useSpecificDays}
                onCheckedChange={(checked) =>
                  handleGoalChange("useSpecificDays", checked)
                }
              />
              <Label htmlFor="useSpecificDays">
                Set specific days of the week
              </Label>
            </div>

            {goals.useSpecificDays && (
              <div>
                <p className="text-sm text-muted-foreground mb-3">
                  Select {goals.weeklyDays} day{goals.weeklyDays > 1 ? "s" : ""}{" "}
                  for practice:
                </p>
                <div className="grid grid-cols-7 gap-2">
                  {DAYS_OF_WEEK.map((day) => {
                    const isSelected = goals.specificDays.includes(day.key);
                    const canSelect =
                      goals.specificDays.length < goals.weeklyDays ||
                      isSelected;

                    return (
                      <button
                        key={day.key}
                        onClick={() => handleSpecificDayToggle(day.key)}
                        disabled={!canSelect}
                        className={`
                          p-2 text-xs rounded-md border transition-colors
                          ${
                            isSelected
                              ? "bg-blue-500 text-white border-blue-500"
                              : canSelect
                                ? "bg-white border-gray-200 hover:bg-gray-50"
                                : "bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed"
                          }
                        `}
                      >
                        {day.short}
                      </button>
                    );
                  })}
                </div>
                {goals.specificDays.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-muted-foreground">
                      Selected days:
                    </p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {goals.specificDays.map((dayKey) => {
                        const day = DAYS_OF_WEEK.find((d) => d.key === dayKey);
                        return (
                          <Badge key={dayKey} variant="secondary">
                            {day?.label}
                          </Badge>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Practice Duration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            Practice Duration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="minutesPerSession">
              How long should each practice session be?
            </Label>
            <div className="flex items-center space-x-4 mt-2">
              <Input
                id="minutesPerSession"
                type="number"
                min="5"
                max="180"
                step="5"
                value={goals.minutesPerSession}
                onChange={(e) =>
                  handleGoalChange(
                    "minutesPerSession",
                    parseInt(e.target.value) || 30,
                  )
                }
                className="w-24"
              />
              <span className="text-sm text-muted-foreground">
                minutes per session
              </span>
            </div>
          </div>


        </CardContent>
      </Card>

      {/* Weekly Goal Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Weekly Goal Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="space-y-2 text-sm text-blue-800">
              <p className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                {goals.weeklyDays} practice session
                {goals.weeklyDays > 1 ? "s" : ""} per week
              </p>
              <p className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                {formatDuration(goals.minutesPerSession)} per session
              </p>
              <p className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                <strong>
                  {formatDuration(goals.weeklyMinutes)} total per week
                </strong>
              </p>
              {goals.useSpecificDays && goals.specificDays.length > 0 && (
                <p className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  On:{" "}
                  {goals.specificDays
                    .map(
                      (dayKey) =>
                        DAYS_OF_WEEK.find((d) => d.key === dayKey)?.label,
                    )
                    .join(", ")}
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Goal Suggestions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Goal Suggestions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => {
                setGoals({
                  weeklyDays: 3,
                  specificDays: ["monday", "wednesday", "friday"],
                  minutesPerSession: 20,
                  weeklyMinutes: 60,
                  useSpecificDays: true,
                });
                setHasChanges(true);
              }}
              className="p-4 border rounded-lg hover:bg-gray-50 text-left"
            >
              <h4 className="font-medium">Beginner</h4>
              <p className="text-sm text-muted-foreground">
                3 days, 20 min each
              </p>
              <p className="text-xs text-blue-600">1 hour/week</p>
            </button>

            <button
              onClick={() => {
                setGoals({
                  weeklyDays: 4,
                  specificDays: ["monday", "tuesday", "thursday", "friday"],
                  minutesPerSession: 30,
                  weeklyMinutes: 120,
                  useSpecificDays: true,
                });
                setHasChanges(true);
              }}
              className="p-4 border rounded-lg hover:bg-gray-50 text-left"
            >
              <h4 className="font-medium">Intermediate</h4>
              <p className="text-sm text-muted-foreground">
                4 days, 30 min each
              </p>
              <p className="text-xs text-blue-600">2 hours/week</p>
            </button>

            <button
              onClick={() => {
                setGoals({
                  weeklyDays: 5,
                  specificDays: [
                    "monday",
                    "tuesday",
                    "wednesday",
                    "thursday",
                    "friday",
                  ],
                  minutesPerSession: 45,
                  weeklyMinutes: 225,
                  useSpecificDays: true,
                });
                setHasChanges(true);
              }}
              className="p-4 border rounded-lg hover:bg-gray-50 text-left"
            >
              <h4 className="font-medium">Advanced</h4>
              <p className="text-sm text-muted-foreground">
                5 days, 45 min each
              </p>
              <p className="text-xs text-blue-600">3.75 hours/week</p>
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      {hasChanges && (
        <div className="flex justify-between">
          <Button variant="outline" onClick={handleReset} disabled={saving}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>

          <Button onClick={handleSave} disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? "Saving..." : "Save Goals"}
          </Button>
        </div>
      )}
    </div>
  );
}
