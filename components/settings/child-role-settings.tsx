"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { GraduationCap, Mic } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Profile } from "@/lib/types/profile";
import { UserRoleType } from "@/lib/store";
import { updateProfile } from "@/lib/queries/profileBasedQueries";
import { fetchProfileInstruments } from "@/lib/queries/profileBasedInstrumentQueries";
import { RoleConfirmationDialog } from "./role-confirmation-dialog";
import { supabase } from "@/lib/supabase";

interface ChildRoleSettingsProps {
  profile: Profile;
  onProfileUpdate?: (updatedProfile: Profile) => void;
}

export function ChildRoleSettings({
  profile,
  onProfileUpdate,
}: ChildRoleSettingsProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [userInstruments, setUserInstruments] = useState<any[]>([]);
  const [confirmationOpen, setConfirmationOpen] = useState(false);
  const [roleToRemove, setRoleToRemove] = useState<UserRoleType | null>(null);
  const [affectedInstruments, setAffectedInstruments] = useState(0);

  // Child profiles only support student and performer roles
  const roles: {
    id: UserRoleType;
    label: string;
    icon: React.ReactNode;
    description: string;
  }[] = [
    {
      id: "student",
      label: "Student",
      icon: <GraduationCap className="h-5 w-5" />,
      description: "Access to learning materials and practice tools",
    },
    {
      id: "performer",
      label: "Performer",
      icon: <Mic className="h-5 w-5" />,
      description: "Access to setlists, songs, and performance tools",
    },
  ];

  // Load user instruments when component mounts
  useEffect(() => {
    const loadUserInstruments = async () => {
      if (!profile?.id) return;

      try {
        const instruments = await fetchProfileInstruments(profile.id);
        setUserInstruments(instruments);
      } catch (error) {
        console.error("Error fetching profile instruments:", error);
      }
    };

    loadUserInstruments();
  }, [profile?.id]);

  // Count instruments with competencies for a specific role
  const countInstrumentsWithCompetency = (role: UserRoleType) => {
    if (!userInstruments.length) return 0;

    const metadataKey =
      role === "performer"
        ? "performing_skill_level"
        : role === "student"
          ? "student_skill_level"
          : null;

    if (!metadataKey) return 0;

    return userInstruments.filter((instrument) => {
      const metadata = instrument.metadata || {};
      return metadata[metadataKey] && metadata[metadataKey] !== "none";
    }).length;
  };

  const handleRoleChange = async (role: UserRoleType, checked: boolean) => {
    if (!profile) return;

    // If adding a role, proceed directly
    if (checked) {
      await updateRole(role, checked);
      return;
    }

    // If removing a role, check if there are competencies that would be affected
    const affectedCount = countInstrumentsWithCompetency(role);

    if (affectedCount > 0 && (role === "performer" || role === "student")) {
      // Show confirmation dialog
      setRoleToRemove(role);
      setAffectedInstruments(affectedCount);
      setConfirmationOpen(true);
    } else {
      // No competencies affected, proceed with removal
      await updateRole(role, checked);
    }
  };

  const handleConfirmRemoveRole = async () => {
    if (roleToRemove) {
      await updateRole(roleToRemove, false);
      setConfirmationOpen(false);
      setRoleToRemove(null);
    }
  };

  const handleCancelRemoveRole = () => {
    setConfirmationOpen(false);
    setRoleToRemove(null);
  };

  const updateRole = async (role: UserRoleType, checked: boolean) => {
    setIsLoading(true);

    try {
      // Get current roles or empty array
      const currentRoles: UserRoleType[] = profile?.metadata?.roles || [];

      // Create new roles array based on the change
      let newRoles: UserRoleType[];

      if (checked) {
        // Add the role if it's not already in the array
        newRoles = currentRoles.includes(role)
          ? currentRoles
          : [...currentRoles, role];
      } else {
        // Remove the role if it's in the array
        newRoles = currentRoles.filter((r: UserRoleType) => r !== role);
      }

      console.log(`Updating roles for profile ${profile.id}:`, newRoles);
      console.log("Current metadata:", profile.metadata);

      // Update the profile with the new roles in metadata
      const updatedProfile = await updateProfile(profile.id, {
        metadata: {
          ...profile.metadata,
          roles: newRoles,
        },
      });

      console.log("Updated profile:", updatedProfile);

      // Also update the legacy fields in the children table if this is from the children table
      if (profile.metadata?.from_children_table) {
        try {
          // Update the is_student, is_performer, is_teacher fields
          const updateData: any = {};

          if (role === "student") {
            updateData.is_student = checked;
          } else if (role === "performer") {
            updateData.is_performer = checked;
          } else if (role === "teacher") {
            updateData.is_teacher = checked;
          }

          console.log(
            `Updating legacy fields in children table for ${profile.id}:`,
            updateData,
          );

          const { error } = await supabase
            .from("children")
            .update(updateData)
            .eq("id", profile.id);

          if (error) {
            console.error(
              "Error updating legacy fields in children table:",
              error,
            );
          } else {
            console.log("Successfully updated legacy fields in children table");
          }
        } catch (err) {
          console.error("Error updating legacy fields:", err);
          // Don't throw here, as the main update was successful
        }
      }

      if (onProfileUpdate) {
        onProfileUpdate(updatedProfile);
      }

      toast({
        title: checked ? "Role added" : "Role removed",
        description: checked
          ? `${profile.display_name} now has the ${role} role.`
          : `The ${role} role has been removed from ${profile.display_name}.`,
      });
    } catch (error) {
      console.error("Error updating roles:", error);
      toast({
        title: "Error",
        description: "Failed to update roles. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!profile) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <p>No profile selected.</p>
        </CardContent>
      </Card>
    );
  }

  // Get current roles from metadata
  const currentRoles: UserRoleType[] = profile.metadata?.roles || [];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Role Settings</CardTitle>
        <CardDescription>
          Manage roles and permissions for {profile.display_name}. Select
          multiple roles as needed.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {roles.map((role) => (
            <Card
              key={role.id}
              className={currentRoles.includes(role.id) ? "border-primary" : ""}
            >
              <CardHeader className="pb-2 flex flex-row items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2">{role.icon}</div>
                  <CardTitle className="text-sm font-medium">
                    {role.label}
                  </CardTitle>
                </div>
                <Switch
                  checked={currentRoles.includes(role.id) || false}
                  onCheckedChange={(checked) =>
                    handleRoleChange(role.id, checked)
                  }
                  disabled={isLoading}
                />
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {role.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="mt-6">
          <p className="text-sm text-muted-foreground">
            Note: Changing roles will affect the features and tools available to{" "}
            {profile.display_name} in the application.
          </p>
        </div>
      </CardContent>

      {/* Confirmation Dialog */}
      {roleToRemove && (
        <RoleConfirmationDialog
          open={confirmationOpen}
          onOpenChange={setConfirmationOpen}
          role={roleToRemove}
          onConfirm={handleConfirmRemoveRole}
          onCancel={handleCancelRemoveRole}
          affectedInstruments={affectedInstruments}
          profileName={profile.display_name}
        />
      )}
    </Card>
  );
}
