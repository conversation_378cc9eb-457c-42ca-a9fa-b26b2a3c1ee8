"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAppStore } from "@/lib/store";
import { useToast } from "@/hooks/use-toast";
import {
  fetchInstruments,
  fetchUserInstruments,
  addUserInstrument,
  updateUserInstrument,
  removeUserInstrument,
} from "@/lib/queries/instrumentQueries";
import { Instrument, UserInstrument, SkillLevel } from "@/lib/types/instrument";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Loader2,
  Plus,
  Trash2,
  Music,
  Guitar,
  Drum,
  Piano,
  Mic,
} from "lucide-react";
import { INSTRUMENTS } from "@/lib/constants/instruments";

export function InstrumentCompetenciesImproved() {
  const { currentProfile } = useAppStore();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [instruments, setInstruments] = useState<Instrument[]>([]);
  const [userInstruments, setUserInstruments] = useState<UserInstrument[]>([]);
  const [selectedInstrumentId, setSelectedInstrumentId] = useState<string>("");
  const [teachingSkillLevel, setTeachingSkillLevel] =
    useState<SkillLevel>("beginner");
  const [performingSkillLevel, setPerformingSkillLevel] =
    useState<SkillLevel>("beginner");
  const [studentSkillLevel, setStudentSkillLevel] =
    useState<SkillLevel>("beginner");
  const [isAddingInstrument, setIsAddingInstrument] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [popularInstruments, setPopularInstruments] = useState<Instrument[]>(
    [],
  );
  const [newInstrumentName, setNewInstrumentName] = useState<string>("");

  // Check if user has teacher, performer, or student roles
  const isTeacher = currentProfile?.metadata?.roles?.includes("teacher") || false;
  const isPerformer = currentProfile?.metadata?.roles?.includes("performer") || false;
  const isStudent = currentProfile?.metadata?.roles?.includes("student") || false;
  const showCompetencies = isTeacher || isPerformer || isStudent;

  // Load instruments and user instruments
  useEffect(() => {
    const loadData = async () => {
      if (!currentProfile?.id || !showCompetencies) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);

        // Fetch all instruments
        try {
          const instrumentsData = await fetchInstruments();
          setInstruments(instrumentsData);

          // Extract unique categories
          const uniqueCategories = Array.from(
            new Set(instrumentsData.map((instrument) => instrument.category)),
          );
          setCategories(uniqueCategories);

          // Set popular instruments
          const popular = instrumentsData.filter((instrument) =>
            [
              "Acoustic Guitar",
              "Electric Guitar",
              "Piano",
              "Drum Kit",
              "Vocals",
              "Bass Guitar",
              "Ukulele",
              "Violin",
            ].includes(instrument.name),
          );
          setPopularInstruments(popular);

          // If there are no instruments selected yet, set the first one as default
          if (instrumentsData.length > 0 && !selectedInstrumentId) {
            setSelectedInstrumentId(instrumentsData[0].id);
          }
        } catch (instrumentsError) {
          console.error("Failed to load instruments:", instrumentsError);

          // Use fallback from constants
          const fallbackInstruments = INSTRUMENTS.map((instrument) => ({
            id:
              instrument.id ||
              `fallback-${Math.random().toString(36).substring(2, 15)}`,
            name: instrument.name,
            type: instrument.type as any,
            category: instrument.category as any,
            icon: null,
          }));

          setInstruments(fallbackInstruments);

          // Extract unique categories from fallback
          const uniqueCategories = Array.from(
            new Set(
              fallbackInstruments.map((instrument) => instrument.category),
            ),
          );
          setCategories(uniqueCategories);

          // Set popular instruments from fallback
          const popular = fallbackInstruments.filter((instrument) =>
            [
              "Acoustic Guitar",
              "Electric Guitar",
              "Piano",
              "Drum Kit",
              "Vocals",
              "Bass Guitar",
              "Ukulele",
              "Violin",
            ].includes(instrument.name),
          );
          setPopularInstruments(popular);

          // If there are no instruments selected yet, set the first one as default
          if (fallbackInstruments.length > 0 && !selectedInstrumentId) {
            setSelectedInstrumentId(fallbackInstruments[0].id);
          }
        }

        // Fetch user's instruments
        try {
          const userInstrumentsData = await fetchUserInstruments(
            currentProfile.id,
          );
          setUserInstruments(userInstrumentsData);
        } catch (userInstrumentsError) {
          console.error(
            "Failed to load user instruments:",
            userInstrumentsError,
          );
          setUserInstruments([]);
        }
      } catch (error) {
        console.error("Failed to load data:", error);
        toast({
          title: "Error",
          description: "Failed to load instruments. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [currentProfile?.id, showCompetencies, toast, selectedInstrumentId]);

  const handleAddInstrument = async () => {
    if (!currentProfile?.id) return;

    // For custom instruments, create a new ID
    let instrumentId = selectedInstrumentId;
    let instrumentName = "";

    if (selectedInstrumentId === "custom" && newInstrumentName.trim()) {
      instrumentId = `custom-${Math.random().toString(36).substring(2, 15)}`;
      instrumentName = newInstrumentName.trim();
    } else if (selectedInstrumentId === "custom") {
      toast({
        title: "Validation Error",
        description: "Please enter a name for your custom instrument.",
        variant: "destructive",
      });
      return;
    } else {
      // Find the instrument name from the selected ID
      const instrument = instruments.find((i) => i.id === selectedInstrumentId);
      if (!instrument) {
        toast({
          title: "Error",
          description: "Selected instrument not found. Please try again.",
          variant: "destructive",
        });
        return;
      }
      instrumentName = instrument.name;
    }

    setIsSubmitting(true);

    try {
      console.log("Adding instrument:", {
        userId: currentProfile.id,
        instrumentId,
        isCustom: selectedInstrumentId === "custom",
        instrumentName,
      });

      // Prepare metadata
      const metadata: Record<string, any> = {
        instrument_name: instrumentName,
      };

      if (isTeacher) metadata.teaching_skill_level = teachingSkillLevel;
      if (isPerformer) metadata.performing_skill_level = performingSkillLevel;
      if (isStudent) metadata.student_skill_level = studentSkillLevel;

      // For custom instruments, add the custom name to metadata
      if (selectedInstrumentId === "custom") {
        metadata.is_custom = true;
        metadata.custom_name = newInstrumentName.trim();
        // The custom name will be used when creating the instrument in the database
      }

      // Create a new user instrument with all the metadata
      const newUserInstrument = await addUserInstrument(
        currentProfile.id,
        instrumentId,
        undefined, // We'll use metadata for skill levels
        false, // Not primary by default
        metadata, // Pass the metadata directly
      );

      // The metadata is already included in the user instrument
      console.log("User instrument created with metadata:", newUserInstrument);

      // Refresh the user instruments list
      const updatedUserInstruments = await fetchUserInstruments(currentProfile.id);
      setUserInstruments(updatedUserInstruments);

      // Reset form
      setIsAddingInstrument(false);
      setTeachingSkillLevel("beginner");
      setPerformingSkillLevel("beginner");
      setStudentSkillLevel("beginner");
      setNewInstrumentName("");

      toast({
        title: "Instrument added",
        description: "Your instrument competency has been added successfully.",
      });
    } catch (error) {
      console.error("Failed to add instrument:", error);
      toast({
        title: "Error",
        description: "Failed to add instrument. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateSkillLevel = async (
    instrumentId: string,
    role: "teacher" | "performer" | "student",
    skillLevel: SkillLevel,
  ) => {
    if (!currentProfile?.id) return;

    const userInstrument = userInstruments.find(
      (ui) => ui.instrument_id === instrumentId,
    );
    if (!userInstrument) return;

    setIsSubmitting(true);

    try {
      // Update the skill level for the specific role
      await updateUserInstrument(userInstrument.id, currentProfile.id, {
        metadata: {
          ...(userInstrument.metadata || {}),
          [role === "teacher"
            ? "teaching_skill_level"
            : role === "performer"
              ? "performing_skill_level"
              : "student_skill_level"]: skillLevel,
        },
      });

      // Refresh the user instruments list
      const updatedUserInstruments = await fetchUserInstruments(currentProfile.id);
      setUserInstruments(updatedUserInstruments);

      toast({
        title: "Skill level updated",
        description: `Your ${role} skill level has been updated successfully.`,
      });
    } catch (error) {
      console.error("Failed to update skill level:", error);
      toast({
        title: "Error",
        description: "Failed to update skill level. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveInstrument = async (instrumentId: string) => {
    if (!currentProfile?.id) return;

    const userInstrument = userInstruments.find(
      (ui) => ui.instrument_id === instrumentId,
    );
    if (!userInstrument) return;

    setIsSubmitting(true);

    try {
      // Remove the user instrument
      await removeUserInstrument(userInstrument.id, currentUser.id);

      // Refresh the user instruments list
      const updatedUserInstruments = await fetchUserInstruments(currentUser.id);
      setUserInstruments(updatedUserInstruments);

      toast({
        title: "Instrument removed",
        description:
          "Your instrument competency has been removed successfully.",
      });
    } catch (error) {
      console.error("Failed to remove instrument:", error);
      toast({
        title: "Error",
        description: "Failed to remove instrument. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // If user doesn't have teacher or performer roles, don't show this component
  if (!showCompetencies) {
    return null;
  }

  // Get instrument name from ID
  const getInstrumentName = (id: string): string => {
    if (id.startsWith("custom-")) {
      const userInstrument = userInstruments.find(
        (ui) => ui.instrument_id === id,
      );
      return userInstrument?.metadata?.custom_name || "Custom Instrument";
    }

    const instrument = instruments.find((i) => i.id === id);
    return instrument?.name || "Unknown Instrument";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Instrument Competencies</CardTitle>
        <CardDescription>
          Manage your instrument competencies for teaching and performing.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <>
            {/* List of user's instruments */}
            {userInstruments.length > 0 ? (
              <div className="space-y-4">
                {userInstruments.map((userInstrument) => (
                  <Card key={userInstrument.id} className="border-primary/20">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Music className="h-5 w-5 mr-2" />
                          <CardTitle className="text-sm font-medium">
                            {userInstrument.instrument?.name ||
                              userInstrument.metadata?.custom_name ||
                              userInstrument.metadata?.instrument_name ||
                              "Unknown Instrument"}
                          </CardTitle>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() =>
                            handleRemoveInstrument(userInstrument.instrument_id)
                          }
                          disabled={isSubmitting}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Teacher skill level */}
                      {isTeacher && (
                        <div className="space-y-2">
                          <Label htmlFor={`teaching-${userInstrument.id}`}>
                            Teaching Competency
                          </Label>
                          <Select
                            value={
                              userInstrument.metadata?.teaching_skill_level ||
                              userInstrument.skill_level ||
                              "beginner"
                            }
                            onValueChange={(value) =>
                              handleUpdateSkillLevel(
                                userInstrument.instrument_id,
                                "teacher",
                                value as SkillLevel,
                              )
                            }
                            disabled={isSubmitting}
                          >
                            <SelectTrigger id={`teaching-${userInstrument.id}`}>
                              <SelectValue placeholder="Select skill level" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="beginner">Beginner</SelectItem>
                              <SelectItem value="intermediate">
                                Intermediate
                              </SelectItem>
                              <SelectItem value="advanced">Advanced</SelectItem>
                              <SelectItem value="expert">Expert</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      {/* Performer skill level */}
                      {isPerformer && (
                        <div className="space-y-2">
                          <Label htmlFor={`performing-${userInstrument.id}`}>
                            Performance Competency
                          </Label>
                          <Select
                            value={
                              userInstrument.metadata?.performing_skill_level ||
                              userInstrument.skill_level ||
                              "beginner"
                            }
                            onValueChange={(value) =>
                              handleUpdateSkillLevel(
                                userInstrument.instrument_id,
                                "performer",
                                value as SkillLevel,
                              )
                            }
                            disabled={isSubmitting}
                          >
                            <SelectTrigger
                              id={`performing-${userInstrument.id}`}
                            >
                              <SelectValue placeholder="Select skill level" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="beginner">Beginner</SelectItem>
                              <SelectItem value="intermediate">
                                Intermediate
                              </SelectItem>
                              <SelectItem value="advanced">Advanced</SelectItem>
                              <SelectItem value="expert">Expert</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      {/* Student skill level */}
                      {isStudent && (
                        <div className="space-y-2">
                          <Label htmlFor={`student-${userInstrument.id}`}>
                            Student Competency
                          </Label>
                          <Select
                            value={
                              userInstrument.metadata?.student_skill_level ||
                              userInstrument.skill_level ||
                              "beginner"
                            }
                            onValueChange={(value) =>
                              handleUpdateSkillLevel(
                                userInstrument.instrument_id,
                                "student",
                                value as SkillLevel,
                              )
                            }
                            disabled={isSubmitting}
                          >
                            <SelectTrigger id={`student-${userInstrument.id}`}>
                              <SelectValue placeholder="Select skill level" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="beginner">Beginner</SelectItem>
                              <SelectItem value="intermediate">
                                Intermediate
                              </SelectItem>
                              <SelectItem value="advanced">Advanced</SelectItem>
                              <SelectItem value="expert">Expert</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                You haven't added any instruments yet.
              </div>
            )}

            {/* Add new instrument button */}
            {!isAddingInstrument ? (
              <Button
                onClick={() => setIsAddingInstrument(true)}
                className="w-full"
                variant="outline"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Instrument
              </Button>
            ) : (
              <Card className="border-dashed">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Add New Instrument
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Instrument Selection Tabs */}
                  <Tabs defaultValue="popular" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="popular">Popular</TabsTrigger>
                      <TabsTrigger value="all">All Instruments</TabsTrigger>
                      <TabsTrigger value="custom">Custom</TabsTrigger>
                    </TabsList>

                    {/* Popular Instruments Tab */}
                    <TabsContent value="popular" className="space-y-4">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                        {popularInstruments.map((instrument) => {
                          // Choose appropriate icon based on instrument type
                          let Icon = Music;
                          if (
                            instrument.type === "string" &&
                            instrument.name.toLowerCase().includes("guitar")
                          ) {
                            Icon = Guitar;
                          } else if (instrument.type === "percussion") {
                            Icon = Drum;
                          } else if (instrument.type === "keyboard") {
                            Icon = Piano;
                          } else if (instrument.type === "vocal") {
                            Icon = Mic;
                          }

                          return (
                            <Button
                              key={instrument.id}
                              type="button"
                              variant={
                                selectedInstrumentId === instrument.id
                                  ? "default"
                                  : "outline"
                              }
                              className="h-auto py-4 flex flex-col items-center justify-center gap-2"
                              onClick={() =>
                                setSelectedInstrumentId(instrument.id)
                              }
                            >
                              <Icon className="h-8 w-8" />
                              <span className="text-sm">{instrument.name}</span>
                            </Button>
                          );
                        })}
                      </div>
                    </TabsContent>

                    {/* All Instruments Tab */}
                    <TabsContent value="all" className="space-y-4">
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Label htmlFor="category-filter">Category</Label>
                          <Select
                            value={selectedCategory}
                            onValueChange={setSelectedCategory}
                          >
                            <SelectTrigger
                              id="category-filter"
                              className="w-[180px]"
                            >
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">
                                All Categories
                              </SelectItem>
                              {categories.map((category) => (
                                <SelectItem key={category} value={category}>
                                  {category}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          {instruments
                            .filter(
                              (instrument) =>
                                selectedCategory === "all" ||
                                instrument.category === selectedCategory,
                            )
                            .map((instrument) => {
                              // Choose appropriate icon based on instrument type
                              let Icon = Music;
                              if (
                                instrument.type === "string" &&
                                instrument.name.toLowerCase().includes("guitar")
                              ) {
                                Icon = Guitar;
                              } else if (instrument.type === "percussion") {
                                Icon = Drum;
                              } else if (instrument.type === "keyboard") {
                                Icon = Piano;
                              } else if (instrument.type === "vocal") {
                                Icon = Mic;
                              }

                              return (
                                <Button
                                  key={instrument.id}
                                  type="button"
                                  variant={
                                    selectedInstrumentId === instrument.id
                                      ? "default"
                                      : "outline"
                                  }
                                  className="h-auto py-4 flex flex-col items-center justify-center gap-2"
                                  onClick={() =>
                                    setSelectedInstrumentId(instrument.id)
                                  }
                                >
                                  <Icon className="h-8 w-8" />
                                  <span className="text-sm">
                                    {instrument.name}
                                  </span>
                                </Button>
                              );
                            })}
                        </div>
                      </div>
                    </TabsContent>

                    {/* Custom Instrument Tab */}
                    <TabsContent value="custom" className="space-y-4">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="custom-instrument">
                            Custom Instrument Name
                          </Label>
                          <Input
                            id="custom-instrument"
                            placeholder="Enter instrument name"
                            value={newInstrumentName}
                            onChange={(e) =>
                              setNewInstrumentName(e.target.value)
                            }
                          />
                        </div>
                        <Button
                          type="button"
                          variant={
                            selectedInstrumentId === "custom"
                              ? "default"
                              : "outline"
                          }
                          className="w-full"
                          onClick={() => setSelectedInstrumentId("custom")}
                          disabled={!newInstrumentName.trim()}
                        >
                          Use Custom Instrument
                        </Button>
                      </div>
                    </TabsContent>
                  </Tabs>

                  {/* Skill Level Selection */}
                  <div className="space-y-4 mt-4">
                    {/* Teacher skill level */}
                    {isTeacher && (
                      <div className="space-y-2">
                        <Label htmlFor="teaching-skill">
                          Teaching Competency
                        </Label>
                        <Select
                          value={teachingSkillLevel}
                          onValueChange={(value) =>
                            setTeachingSkillLevel(value as SkillLevel)
                          }
                          disabled={isSubmitting}
                        >
                          <SelectTrigger id="teaching-skill">
                            <SelectValue placeholder="Select skill level" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="beginner">Beginner</SelectItem>
                            <SelectItem value="intermediate">
                              Intermediate
                            </SelectItem>
                            <SelectItem value="advanced">Advanced</SelectItem>
                            <SelectItem value="expert">Expert</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {/* Performer skill level */}
                    {isPerformer && (
                      <div className="space-y-2">
                        <Label htmlFor="performing-skill">
                          Performance Competency
                        </Label>
                        <Select
                          value={performingSkillLevel}
                          onValueChange={(value) =>
                            setPerformingSkillLevel(value as SkillLevel)
                          }
                          disabled={isSubmitting}
                        >
                          <SelectTrigger id="performing-skill">
                            <SelectValue placeholder="Select skill level" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="beginner">Beginner</SelectItem>
                            <SelectItem value="intermediate">
                              Intermediate
                            </SelectItem>
                            <SelectItem value="advanced">Advanced</SelectItem>
                            <SelectItem value="expert">Expert</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {/* Student skill level */}
                    {isStudent && (
                      <div className="space-y-2">
                        <Label htmlFor="student-skill">
                          Student Competency
                        </Label>
                        <Select
                          value={studentSkillLevel}
                          onValueChange={(value) =>
                            setStudentSkillLevel(value as SkillLevel)
                          }
                          disabled={isSubmitting}
                        >
                          <SelectTrigger id="student-skill">
                            <SelectValue placeholder="Select skill level" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="beginner">Beginner</SelectItem>
                            <SelectItem value="intermediate">
                              Intermediate
                            </SelectItem>
                            <SelectItem value="advanced">Advanced</SelectItem>
                            <SelectItem value="expert">Expert</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsAddingInstrument(false);
                        setNewInstrumentName("");
                      }}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleAddInstrument}
                      disabled={
                        !selectedInstrumentId ||
                        (selectedInstrumentId === "custom" &&
                          !newInstrumentName.trim()) ||
                        isSubmitting
                      }
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Adding...
                        </>
                      ) : (
                        "Add Instrument"
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
