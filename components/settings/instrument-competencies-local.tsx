"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useAppStore } from "@/lib/store";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Loader2,
  Plus,
  Trash2,
  Music,
  Guitar,
  Drum,
  Piano,
  Mic,
} from "lucide-react";
import { INSTRUMENTS } from "@/lib/constants/instruments";

// Define types locally to avoid database dependencies
type SkillLevel = "beginner" | "intermediate" | "advanced" | "expert";

interface Instrument {
  id: string;
  name: string;
  type: string;
  category: string;
}

interface UserInstrument {
  id: string;
  instrumentId: string;
  instrument: Instrument;
  teachingSkillLevel?: SkillLevel;
  performingSkillLevel?: SkillLevel;
  studentSkillLevel?: SkillLevel;
}

// Use localStorage to persist user instruments
const LOCAL_STORAGE_KEY = "crescender_user_instruments";

export function InstrumentCompetenciesLocal() {
  const { currentProfile: currentUser } = useAppStore();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [instruments, setInstruments] = useState<Instrument[]>([]);
  const [userInstruments, setUserInstruments] = useState<UserInstrument[]>([]);
  const [selectedInstrumentId, setSelectedInstrumentId] = useState<string>("");
  const [teachingSkillLevel, setTeachingSkillLevel] =
    useState<SkillLevel>("beginner");
  const [performingSkillLevel, setPerformingSkillLevel] =
    useState<SkillLevel>("beginner");
  const [studentSkillLevel, setStudentSkillLevel] =
    useState<SkillLevel>("beginner");
  const [isAddingInstrument, setIsAddingInstrument] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [popularInstruments, setPopularInstruments] = useState<Instrument[]>(
    [],
  );
  const [newInstrumentName, setNewInstrumentName] = useState<string>("");

  // Check if user has teacher, performer, or student roles
  const isTeacher = currentUser?.roles?.includes("teacher") || false;
  const isPerformer = currentUser?.roles?.includes("performer") || false;
  const isStudent = currentUser?.roles?.includes("student") || false;
  const showCompetencies = isTeacher || isPerformer || isStudent;

  // Load instruments and user instruments
  useEffect(() => {
    if (!showCompetencies) {
      setIsLoading(false);
      return;
    }

    // Convert constants to Instrument type
    const instrumentsData = INSTRUMENTS.map((instrument) => ({
      id:
        instrument.id ||
        `fallback-${Math.random().toString(36).substring(2, 15)}`,
      name: instrument.name,
      type: instrument.type,
      category: instrument.category,
    }));

    setInstruments(instrumentsData);

    // Extract unique categories
    const uniqueCategories = Array.from(
      new Set(instrumentsData.map((instrument) => instrument.category)),
    );
    setCategories(uniqueCategories);

    // Set popular instruments
    const popular = instrumentsData.filter((instrument) =>
      [
        "Acoustic Guitar",
        "Electric Guitar",
        "Piano",
        "Drum Kit",
        "Vocals",
        "Bass Guitar",
        "Ukulele",
        "Violin",
      ].includes(instrument.name),
    );
    setPopularInstruments(popular);

    // If there are no instruments selected yet, set the first one as default
    if (instrumentsData.length > 0 && !selectedInstrumentId) {
      setSelectedInstrumentId(instrumentsData[0].id);
    }

    // Load user instruments from localStorage
    try {
      const savedInstruments = localStorage.getItem(LOCAL_STORAGE_KEY);
      if (savedInstruments) {
        const parsedInstruments = JSON.parse(
          savedInstruments,
        ) as UserInstrument[];

        // Make sure each user instrument has a valid reference to an instrument
        const validUserInstruments = parsedInstruments.map((ui) => {
          const instrument = instrumentsData.find(
            (i) => i.id === ui.instrumentId,
          );
          return {
            ...ui,
            instrument: instrument || {
              id: ui.instrumentId,
              name: ui.instrument?.name || "Unknown Instrument",
              type: ui.instrument?.type || "other",
              category: ui.instrument?.category || "Other",
            },
          };
        });

        setUserInstruments(validUserInstruments);
      }
    } catch (error) {
      console.error(
        "Failed to load user instruments from localStorage:",
        error,
      );
    }

    setIsLoading(false);
  }, [showCompetencies, selectedInstrumentId]);

  // Save user instruments to localStorage whenever they change
  useEffect(() => {
    if (userInstruments.length > 0) {
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(userInstruments));
    }
  }, [userInstruments]);

  const handleAddInstrument = async () => {
    // For custom instruments, create a new ID
    let instrumentId = selectedInstrumentId;
    let instrumentName = "";

    if (selectedInstrumentId === "custom" && newInstrumentName.trim()) {
      instrumentId = `custom-${Math.random().toString(36).substring(2, 15)}`;
      instrumentName = newInstrumentName.trim();
    } else if (selectedInstrumentId === "custom") {
      toast({
        title: "Validation Error",
        description: "Please enter a name for your custom instrument.",
        variant: "destructive",
      });
      return;
    } else {
      // Find the instrument name from the selected ID
      const instrument = instruments.find((i) => i.id === selectedInstrumentId);
      if (!instrument) {
        toast({
          title: "Error",
          description: "Selected instrument not found. Please try again.",
          variant: "destructive",
        });
        return;
      }
      instrumentName = instrument.name;
    }

    setIsSubmitting(true);

    try {
      // Create a new user instrument
      const newInstrument: Instrument =
        selectedInstrumentId === "custom"
          ? {
              id: instrumentId,
              name: instrumentName,
              type: "other",
              category: "Custom",
            }
          : instruments.find((i) => i.id === selectedInstrumentId) || {
              id: instrumentId,
              name: instrumentName,
              type: "other",
              category: "Other",
            };

      const newUserInstrument: UserInstrument = {
        id: `user-instrument-${Math.random().toString(36).substring(2, 15)}`,
        instrumentId: instrumentId,
        instrument: newInstrument,
        teachingSkillLevel: isTeacher ? teachingSkillLevel : undefined,
        performingSkillLevel: isPerformer ? performingSkillLevel : undefined,
        studentSkillLevel: isStudent ? studentSkillLevel : undefined,
      };

      // Add to the list
      setUserInstruments((prev) => [...prev, newUserInstrument]);

      // Reset form
      setIsAddingInstrument(false);
      setTeachingSkillLevel("beginner");
      setPerformingSkillLevel("beginner");
      setStudentSkillLevel("beginner");
      setNewInstrumentName("");

      toast({
        title: "Instrument added",
        description: "Your instrument competency has been added successfully.",
      });
    } catch (error) {
      console.error("Failed to add instrument:", error);
      toast({
        title: "Error",
        description: "Failed to add instrument. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateSkillLevel = (
    instrumentId: string,
    role: "teacher" | "performer" | "student",
    skillLevel: SkillLevel,
  ) => {
    setUserInstruments((prev) =>
      prev.map((ui) => {
        if (ui.instrumentId === instrumentId) {
          return {
            ...ui,
            ...(role === "teacher" ? { teachingSkillLevel: skillLevel } : {}),
            ...(role === "performer"
              ? { performingSkillLevel: skillLevel }
              : {}),
            ...(role === "student" ? { studentSkillLevel: skillLevel } : {}),
          };
        }
        return ui;
      }),
    );

    toast({
      title: "Skill level updated",
      description: `Your ${role} skill level has been updated successfully.`,
    });
  };

  const handleRemoveInstrument = (instrumentId: string) => {
    setUserInstruments((prev) =>
      prev.filter((ui) => ui.instrumentId !== instrumentId),
    );

    toast({
      title: "Instrument removed",
      description: "Your instrument competency has been removed successfully.",
    });
  };

  // If user doesn't have teacher or performer roles, don't show this component
  if (!showCompetencies) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Instrument Competencies</CardTitle>
        <CardDescription>
          Manage your instrument competencies for teaching, performing, and
          learning.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <>
            {/* List of user's instruments */}
            {userInstruments.length > 0 ? (
              <div className="space-y-4">
                {userInstruments.map((userInstrument) => (
                  <Card key={userInstrument.id} className="border-primary/20">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Music className="h-5 w-5 mr-2" />
                          <CardTitle className="text-sm font-medium">
                            {userInstrument.instrument?.name ||
                              "Unknown Instrument"}
                          </CardTitle>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() =>
                            handleRemoveInstrument(userInstrument.instrumentId)
                          }
                          disabled={isSubmitting}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Teacher skill level */}
                      {isTeacher && (
                        <div className="space-y-2">
                          <Label htmlFor={`teaching-${userInstrument.id}`}>
                            Teaching Competency
                          </Label>
                          <Select
                            value={
                              userInstrument.teachingSkillLevel || "beginner"
                            }
                            onValueChange={(value) =>
                              handleUpdateSkillLevel(
                                userInstrument.instrumentId,
                                "teacher",
                                value as SkillLevel,
                              )
                            }
                            disabled={isSubmitting}
                          >
                            <SelectTrigger id={`teaching-${userInstrument.id}`}>
                              <SelectValue placeholder="Select skill level" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="beginner">Beginner</SelectItem>
                              <SelectItem value="intermediate">
                                Intermediate
                              </SelectItem>
                              <SelectItem value="advanced">Advanced</SelectItem>
                              <SelectItem value="expert">Expert</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      {/* Performer skill level */}
                      {isPerformer && (
                        <div className="space-y-2">
                          <Label htmlFor={`performing-${userInstrument.id}`}>
                            Performance Competency
                          </Label>
                          <Select
                            value={
                              userInstrument.performingSkillLevel || "beginner"
                            }
                            onValueChange={(value) =>
                              handleUpdateSkillLevel(
                                userInstrument.instrumentId,
                                "performer",
                                value as SkillLevel,
                              )
                            }
                            disabled={isSubmitting}
                          >
                            <SelectTrigger
                              id={`performing-${userInstrument.id}`}
                            >
                              <SelectValue placeholder="Select skill level" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="beginner">Beginner</SelectItem>
                              <SelectItem value="intermediate">
                                Intermediate
                              </SelectItem>
                              <SelectItem value="advanced">Advanced</SelectItem>
                              <SelectItem value="expert">Expert</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      {/* Student skill level */}
                      {isStudent && (
                        <div className="space-y-2">
                          <Label htmlFor={`student-${userInstrument.id}`}>
                            Student Competency
                          </Label>
                          <Select
                            value={
                              userInstrument.studentSkillLevel || "beginner"
                            }
                            onValueChange={(value) =>
                              handleUpdateSkillLevel(
                                userInstrument.instrumentId,
                                "student",
                                value as SkillLevel,
                              )
                            }
                            disabled={isSubmitting}
                          >
                            <SelectTrigger id={`student-${userInstrument.id}`}>
                              <SelectValue placeholder="Select skill level" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="beginner">Beginner</SelectItem>
                              <SelectItem value="intermediate">
                                Intermediate
                              </SelectItem>
                              <SelectItem value="advanced">Advanced</SelectItem>
                              <SelectItem value="expert">Expert</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                You haven't added any instruments yet.
              </div>
            )}

            {/* Add new instrument button */}
            {!isAddingInstrument ? (
              <Button
                onClick={() => setIsAddingInstrument(true)}
                className="w-full"
                variant="outline"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Instrument
              </Button>
            ) : (
              <Card className="border-dashed">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Add New Instrument
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Instrument Selection Tabs */}
                  <Tabs defaultValue="popular" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="popular">Popular</TabsTrigger>
                      <TabsTrigger value="all">All Instruments</TabsTrigger>
                      <TabsTrigger value="custom">Custom</TabsTrigger>
                    </TabsList>

                    {/* Popular Instruments Tab */}
                    <TabsContent value="popular" className="space-y-4">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                        {popularInstruments.map((instrument) => {
                          // Choose appropriate icon based on instrument type
                          let Icon = Music;
                          if (
                            instrument.type === "string" &&
                            instrument.name.toLowerCase().includes("guitar")
                          ) {
                            Icon = Guitar;
                          } else if (instrument.type === "percussion") {
                            Icon = Drum;
                          } else if (instrument.type === "keyboard") {
                            Icon = Piano;
                          } else if (instrument.type === "vocal") {
                            Icon = Mic;
                          }

                          return (
                            <Button
                              key={instrument.id}
                              type="button"
                              variant={
                                selectedInstrumentId === instrument.id
                                  ? "default"
                                  : "outline"
                              }
                              className="h-auto py-4 flex flex-col items-center justify-center gap-2"
                              onClick={() =>
                                setSelectedInstrumentId(instrument.id)
                              }
                            >
                              <Icon className="h-8 w-8" />
                              <span className="text-sm">{instrument.name}</span>
                            </Button>
                          );
                        })}
                      </div>
                    </TabsContent>

                    {/* All Instruments Tab */}
                    <TabsContent value="all" className="space-y-4">
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Label htmlFor="category-filter">Category</Label>
                          <Select
                            value={selectedCategory}
                            onValueChange={setSelectedCategory}
                          >
                            <SelectTrigger
                              id="category-filter"
                              className="w-[180px]"
                            >
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">
                                All Categories
                              </SelectItem>
                              {categories.map((category) => (
                                <SelectItem key={category} value={category}>
                                  {category}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          {instruments
                            .filter(
                              (instrument) =>
                                selectedCategory === "all" ||
                                instrument.category === selectedCategory,
                            )
                            .map((instrument) => {
                              // Choose appropriate icon based on instrument type
                              let Icon = Music;
                              if (
                                instrument.type === "string" &&
                                instrument.name.toLowerCase().includes("guitar")
                              ) {
                                Icon = Guitar;
                              } else if (instrument.type === "percussion") {
                                Icon = Drum;
                              } else if (instrument.type === "keyboard") {
                                Icon = Piano;
                              } else if (instrument.type === "vocal") {
                                Icon = Mic;
                              }

                              return (
                                <Button
                                  key={instrument.id}
                                  type="button"
                                  variant={
                                    selectedInstrumentId === instrument.id
                                      ? "default"
                                      : "outline"
                                  }
                                  className="h-auto py-4 flex flex-col items-center justify-center gap-2"
                                  onClick={() =>
                                    setSelectedInstrumentId(instrument.id)
                                  }
                                >
                                  <Icon className="h-8 w-8" />
                                  <span className="text-sm">
                                    {instrument.name}
                                  </span>
                                </Button>
                              );
                            })}
                        </div>
                      </div>
                    </TabsContent>

                    {/* Custom Instrument Tab */}
                    <TabsContent value="custom" className="space-y-4">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="custom-instrument">
                            Custom Instrument Name
                          </Label>
                          <Input
                            id="custom-instrument"
                            placeholder="Enter instrument name"
                            value={newInstrumentName}
                            onChange={(e) =>
                              setNewInstrumentName(e.target.value)
                            }
                          />
                        </div>
                        <Button
                          type="button"
                          variant={
                            selectedInstrumentId === "custom"
                              ? "default"
                              : "outline"
                          }
                          className="w-full"
                          onClick={() => setSelectedInstrumentId("custom")}
                          disabled={!newInstrumentName.trim()}
                        >
                          Use Custom Instrument
                        </Button>
                      </div>
                    </TabsContent>
                  </Tabs>

                  {/* Skill Level Selection */}
                  <div className="space-y-4 mt-4">
                    {/* Teacher skill level */}
                    {isTeacher && (
                      <div className="space-y-2">
                        <Label htmlFor="teaching-skill">
                          Teaching Competency
                        </Label>
                        <Select
                          value={teachingSkillLevel}
                          onValueChange={(value) =>
                            setTeachingSkillLevel(value as SkillLevel)
                          }
                          disabled={isSubmitting}
                        >
                          <SelectTrigger id="teaching-skill">
                            <SelectValue placeholder="Select skill level" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="beginner">Beginner</SelectItem>
                            <SelectItem value="intermediate">
                              Intermediate
                            </SelectItem>
                            <SelectItem value="advanced">Advanced</SelectItem>
                            <SelectItem value="expert">Expert</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {/* Performer skill level */}
                    {isPerformer && (
                      <div className="space-y-2">
                        <Label htmlFor="performing-skill">
                          Performance Competency
                        </Label>
                        <Select
                          value={performingSkillLevel}
                          onValueChange={(value) =>
                            setPerformingSkillLevel(value as SkillLevel)
                          }
                          disabled={isSubmitting}
                        >
                          <SelectTrigger id="performing-skill">
                            <SelectValue placeholder="Select skill level" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="beginner">Beginner</SelectItem>
                            <SelectItem value="intermediate">
                              Intermediate
                            </SelectItem>
                            <SelectItem value="advanced">Advanced</SelectItem>
                            <SelectItem value="expert">Expert</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {/* Student skill level */}
                    {isStudent && (
                      <div className="space-y-2">
                        <Label htmlFor="student-skill">
                          Student Competency
                        </Label>
                        <Select
                          value={studentSkillLevel}
                          onValueChange={(value) =>
                            setStudentSkillLevel(value as SkillLevel)
                          }
                          disabled={isSubmitting}
                        >
                          <SelectTrigger id="student-skill">
                            <SelectValue placeholder="Select skill level" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="beginner">Beginner</SelectItem>
                            <SelectItem value="intermediate">
                              Intermediate
                            </SelectItem>
                            <SelectItem value="advanced">Advanced</SelectItem>
                            <SelectItem value="expert">Expert</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsAddingInstrument(false);
                        setNewInstrumentName("");
                      }}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleAddInstrument}
                      disabled={
                        !selectedInstrumentId ||
                        (selectedInstrumentId === "custom" &&
                          !newInstrumentName.trim()) ||
                        isSubmitting
                      }
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Adding...
                        </>
                      ) : (
                        "Add Instrument"
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
