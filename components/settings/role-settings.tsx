"use client";

import type React from "react";

import { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useAppStore, type UserRoleType } from "@/lib/store";
import type { Tables } from "@/lib/supabase-types";
import { updateProfile } from "@/lib/queries/profileQueries";
import { fetchUserInstruments } from "@/lib/queries/instrumentQueries";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import {
  Mic,
  GraduationCap,
  BookOpen,
  Users,
  RefreshCw,
  Bug,
  Shield,
} from "lucide-react";

import { RoleConfirmationDialog } from "./role-confirmation-dialog";
import { MusicLoadingMessage } from "@/components/auth/MusicLoadingMessage";
import { refreshAuthSession } from "@/lib/utils/authSessionRefresher";
import Link from "next/link";

export function RoleSettings() {
  const currentProfile = useAppStore((state) => state.currentProfile);
  const setCurrentProfile = useAppStore((state) => state.setCurrentProfile);
  const authUser = useAppStore((state) => state.authUser);
  const userRoles = useAppStore((state) => state.roles);
  const setUserRoles = useAppStore((state) => state.setRoles);

  // Debug logging for store state
  console.log("[RoleSettings] Component render - Store state:", {
    hasAuthUser: !!authUser,
    authUserId: authUser?.id,
    hasCurrentProfile: !!currentProfile,
    currentProfileId: currentProfile?.id,
    userRoles: userRoles,
    hasUserRoles: !!userRoles,
  });
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [userInstruments, setUserInstruments] = useState<any[]>([]);
  const [confirmationOpen, setConfirmationOpen] = useState(false);
  const [roleToRemove, setRoleToRemove] = useState<UserRoleType | null>(null);
  const [affectedInstruments, setAffectedInstruments] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingTime, setLoadingTime] = useState(0);

  // Track loading time
  useEffect(() => {
    if (!currentProfile) {
      const timer = setInterval(() => {
        setLoadingTime((prev) => prev + 1);
      }, 1000);

      return () => clearInterval(timer);
    }

    return () => {};
  }, [currentProfile]);

  // Function to refresh auth session
  const handleRefreshSession = useCallback(async () => {
    setRefreshing(true);
    setError(null);

    try {
      const result = await refreshAuthSession();

      if (result.success) {
        toast({
          title: "Session refreshed",
          description: "Your session has been refreshed successfully.",
        });

        // Reload the page to ensure everything is updated
        window.location.reload();
      } else {
        setError(result.error || "Failed to refresh session");
        toast({
          title: "Error",
          description: "Failed to refresh session. Please try again.",
          variant: "destructive",
        });
      }
    } catch (err) {
      setError(
        `Error refreshing session: ${err instanceof Error ? err.message : String(err)}`,
      );
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  }, [toast]);

  const roleDefinitions: {
    id: UserRoleType;
    label: string;
    icon: React.ReactNode;
    description: string;
  }[] = [
    {
      id: "performer",
      label: "Performer",
      icon: <Mic className="h-5 w-5" />,
      description: "Access to setlists, songs, and performance tools",
    },
    {
      id: "student",
      label: "Student",
      icon: <GraduationCap className="h-5 w-5" />,
      description: "Access to learning materials and practice tools",
    },
    {
      id: "teacher",
      label: "Teacher",
      icon: <BookOpen className="h-5 w-5" />,
      description: "Create and manage curricula and lesson plans",
    },
    {
      id: "parent",
      label: "Parent",
      icon: <Users className="h-5 w-5" />,
      description: "Monitor and manage student accounts",
    },
  ];

  // Load user instruments when component mounts
  useEffect(() => {
    const loadUserInstruments = async () => {
      if (!currentProfile?.id) return;

      try {
        const instruments = await fetchUserInstruments(currentProfile.id);
        setUserInstruments(instruments);
      } catch (error) {
        console.error("Error fetching user instruments:", error);
      }
    };

    loadUserInstruments();
  }, [currentProfile]);

  // Count instruments with competencies for a specific role
  const countInstrumentsWithCompetency = (role: UserRoleType) => {
    if (!userInstruments.length) return 0;

    const metadataKey =
      role === "teacher"
        ? "teaching_skill_level"
        : role === "performer"
          ? "performing_skill_level"
          : role === "student"
            ? "student_skill_level"
            : null;

    if (!metadataKey) return 0;

    return userInstruments.filter((instrument) => {
      const metadata = instrument.metadata || {};
      return metadata[metadataKey] && metadata[metadataKey] !== "none";
    }).length;
  };

  const handleRoleChange = async (role: UserRoleType, checked: boolean) => {
    if (!currentProfile) return;

    // If adding a role, proceed directly
    if (checked) {
      await updateRole(role, checked);
      return;
    }

    // If removing a role, check if there are competencies that would be affected
    const affectedCount = countInstrumentsWithCompetency(role);

    if (
      affectedCount > 0 &&
      (role === "teacher" || role === "performer" || role === "student")
    ) {
      // Show confirmation dialog
      setRoleToRemove(role);
      setAffectedInstruments(affectedCount);
      setConfirmationOpen(true);
    } else {
      // No competencies affected, proceed with removal
      await updateRole(role, checked);
    }
  };

  const handleConfirmRemoveRole = async () => {
    if (roleToRemove) {
      await updateRole(roleToRemove, false);
      setConfirmationOpen(false);
      setRoleToRemove(null);
    }
  };

  const handleCancelRemoveRole = () => {
    setConfirmationOpen(false);
    setRoleToRemove(null);
  };

  const updateRole = async (role: UserRoleType, checked: boolean) => {
    setIsLoading(true);

    try {
      // Extract current roles from the store's roles object
      const currentRoles: UserRoleType[] = [];
      if (userRoles?.performer) currentRoles.push("performer");
      if (userRoles?.teacher) currentRoles.push("teacher");
      if (userRoles?.student) currentRoles.push("student");
      if (userRoles?.parent) currentRoles.push("parent");
      if (userRoles?.admin) currentRoles.push("admin");

      // Create new roles array based on the change
      let newRoles: UserRoleType[];

      if (checked) {
        // Add the role if it's not already in the array
        newRoles = currentRoles.includes(role)
          ? currentRoles
          : [...currentRoles, role];
      } else {
        // Remove the role if it's in the array
        newRoles = currentRoles.filter((r: UserRoleType) => r !== role);
      }

      console.log(
        `[RoleSettings] Updating roles from [${currentRoles.join(", ")}] to [${newRoles.join(", ")}]`,
      );

      // Update the profile with the new roles
      const updatedProfile = await updateProfile(
        currentProfile!.id,
        {
          roles: newRoles,
        },
        authUser?.id, // Pass the current user ID for permission check
      );

      // Update the store with the updated profile
      if (updatedProfile) {
        console.log("[RoleSettings] Updated profile received:", updatedProfile);
        // The updatedProfile should have the roles array populated by fetchProfileById
        setCurrentProfile(updatedProfile as unknown as Tables<"profiles">);

        // Also update the roles in the store directly
        const newRolesObject = {
          id: currentProfile!.id,
          created_at: userRoles?.created_at || new Date().toISOString(),
          performer: newRoles.includes("performer"),
          teacher: newRoles.includes("teacher"),
          student: newRoles.includes("student"),
          parent: newRoles.includes("parent"),
          admin: newRoles.includes("admin"),
        };
        setUserRoles(newRolesObject);
        console.log("[RoleSettings] Updated roles in store:", newRolesObject);
      }

      toast({
        title: checked ? "Role added" : "Role removed",
        description: checked
          ? `You now have the ${role} role.`
          : `The ${role} role has been removed.`,
      });
    } catch (error) {
      console.error("Error updating role:", error);
      toast({
        title: "Error",
        description: "Failed to update roles. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!currentProfile) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <div className="space-y-4">
            <MusicLoadingMessage />

            {loadingTime > 10 && (
              <div className="mt-8">
                <p className="text-sm text-gray-500 mb-4">
                  It's taking longer than expected to load your profile.
                </p>
                <button
                  onClick={handleRefreshSession}
                  disabled={refreshing}
                  className="px-4 py-2 bg-purple-600 text-white rounded flex items-center mx-auto hover:bg-purple-700 disabled:opacity-50"
                >
                  <RefreshCw
                    className={`w-4 h-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
                  />
                  Refresh Session
                </button>
                <Link
                  href="/debug"
                  className="mt-4 px-4 py-2 bg-gray-600 text-white rounded flex items-center mx-auto hover:bg-gray-700 transition-colors"
                >
                  <Bug className="w-4 h-4 mr-2" />
                  Debug Issues
                </Link>
              </div>
            )}

            {error && (
              <div className="p-2 mb-4 bg-red-100 border border-red-400 rounded">
                <p className="text-red-700">{error}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Extract roles from the store's roles object (excluding admin which is shown in status bar)
  const profileRoles: UserRoleType[] = [];
  if (userRoles?.performer) profileRoles.push("performer");
  if (userRoles?.teacher) profileRoles.push("teacher");
  if (userRoles?.student) profileRoles.push("student");
  if (userRoles?.parent) profileRoles.push("parent");

  console.log("[RoleSettings] Current profile roles:", profileRoles);
  console.log("[RoleSettings] Store roles object:", userRoles);
  console.log(
    "[RoleSettings] Should show instruments?",
    profileRoles.some((role: UserRoleType) =>
      ["teacher", "performer", "student"].includes(role),
    ),
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Roles & Assignments</CardTitle>
        <CardDescription>
          Manage roles for{" "}
          {currentProfile.display_name || currentProfile.username} (
          {currentProfile.profile_type === "child" ? "your child" : "yourself"})
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {roleDefinitions.map((role) => (
            <Card
              key={role.id}
              className={profileRoles.includes(role.id) ? "border-primary" : ""}
            >
              <CardHeader className="pb-2 flex flex-row items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2">{role.icon}</div>
                  <CardTitle className="text-sm font-medium">
                    {role.label}
                  </CardTitle>
                </div>
                <Switch
                  checked={profileRoles.includes(role.id) || false}
                  onCheckedChange={(checked) =>
                    handleRoleChange(role.id, checked)
                  }
                  disabled={isLoading}
                />
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {role.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="mt-6">
          <p className="text-sm text-muted-foreground">
            Note: Changing your role will affect the features and tools
            available to you in the application.
          </p>
        </div>

        {/* Note: Instrument Competencies have moved to the Skills tab */}
        {profileRoles.some((role: UserRoleType) =>
          ["teacher", "performer", "student"].includes(role),
        ) && (
          <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> Instrument competencies have moved to the{" "}
              <a
                href="/settings#skills"
                className="underline hover:text-blue-900"
                onClick={(e) => {
                  e.preventDefault();
                  window.location.hash = 'skills';
                  window.location.reload();
                }}
              >
                Skills tab
              </a>
              . You can manage your instrument skill levels there.
            </p>
          </div>
        )}
      </CardContent>

      {/* Confirmation Dialog */}
      {roleToRemove && (
        <RoleConfirmationDialog
          open={confirmationOpen}
          onOpenChange={setConfirmationOpen}
          role={roleToRemove}
          onConfirm={handleConfirmRemoveRole}
          onCancel={handleCancelRemoveRole}
          affectedInstruments={affectedInstruments}
        />
      )}
    </Card>
  );
}
