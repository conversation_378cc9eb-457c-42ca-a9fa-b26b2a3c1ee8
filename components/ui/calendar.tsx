"use client";

import * as React from "react";
import ReactCalendar from "react-calendar";
import { cn } from "@/lib/utils";
import "react-calendar/dist/Calendar.css";

export interface CalendarProps {
  value?: Date | null;
  onChange?: (date: Date | null) => void;
  disabled?: boolean | ((date: Date) => boolean);
  minDate?: Date;
  maxDate?: Date;
  className?: string;
  selectRange?: boolean;
  allowPartialRange?: boolean;
  returnValue?: "start" | "end" | "range";
  showNeighboringMonth?: boolean;
  showNavigation?: boolean;
  navigationLabel?: ({ date, view, label }: { date: Date; view: string; label: string }) => string;
  formatShortWeekday?: (locale: string, date: Date) => string;
  formatMonthYear?: (locale: string, date: Date) => string;
  formatYear?: (locale: string, date: Date) => string;
  locale?: string;
  calendarType?: "gregory" | "hebrew" | "islamic" | "iso8601";
  view?: "month" | "year" | "decade" | "century";
  activeStartDate?: Date;
  onActiveStartDateChange?: ({ activeStartDate, view }: { activeStartDate: Date; view: string }) => void;
  onViewChange?: ({ activeStartDate, view }: { activeStartDate: Date; view: string }) => void;
  onClickDay?: (value: Date, event: React.MouseEvent<HTMLButtonElement>) => void;
  onClickMonth?: (value: Date, event: React.MouseEvent<HTMLButtonElement>) => void;
  onClickYear?: (value: Date, event: React.MouseEvent<HTMLButtonElement>) => void;
  onClickDecade?: (value: Date, event: React.MouseEvent<HTMLButtonElement>) => void;
  tileClassName?: string | ((args: { date: Date; view: string }) => string | null);
  tileContent?: React.ReactNode | ((args: { date: Date; view: string }) => React.ReactNode);
  tileDisabled?: (args: { date: Date; view: string }) => boolean;
  showDoubleView?: boolean;
  showFixedNumberOfWeeks?: boolean;
  showWeekNumbers?: boolean;
  prev2Label?: React.ReactNode;
  prevLabel?: React.ReactNode;
  nextLabel?: React.ReactNode;
  next2Label?: React.ReactNode;
}

function Calendar({
  value,
  onChange,
  disabled,
  minDate,
  maxDate,
  className,
  selectRange = false,
  showNeighboringMonth = true,
  showNavigation = true,
  locale = "en-US",
  calendarType = "gregory",
  view = "month",
  showDoubleView = false,
  showFixedNumberOfWeeks = false,
  showWeekNumbers = false,
  ...props
}: CalendarProps) {
  // Handle the onChange event to match our expected signature
  const handleChange = (newValue: Date | Date[] | null) => {
    if (onChange) {
      if (Array.isArray(newValue)) {
        // For range selection, return the start date or null
        onChange(newValue[0] || null);
      } else {
        onChange(newValue);
      }
    }
  };

  // Handle disabled dates
  const tileDisabled = React.useCallback(
    ({ date }: { date: Date }) => {
      if (typeof disabled === "function") {
        return disabled(date);
      }
      return Boolean(disabled);
    },
    [disabled]
  );

  return (
    <div className={cn("react-calendar-wrapper", className)}>
      <ReactCalendar
        value={value}
        onChange={handleChange}
        minDate={minDate}
        maxDate={maxDate}
        selectRange={selectRange}
        showNeighboringMonth={showNeighboringMonth}
        showNavigation={showNavigation}
        locale={locale}
        calendarType={calendarType}
        view={view}
        showDoubleView={showDoubleView}
        showFixedNumberOfWeeks={showFixedNumberOfWeeks}
        showWeekNumbers={showWeekNumbers}
        tileDisabled={tileDisabled}
        className="react-calendar-custom"
        {...props}
      />
      <style jsx global>{`
        .react-calendar-custom {
          width: 100%;
          background: white;
          border: 1px solid #e2e8f0;
          border-radius: 0.5rem;
          font-family: inherit;
          line-height: 1.125em;
        }

        .react-calendar-custom *,
        .react-calendar-custom *:before,
        .react-calendar-custom *:after {
          box-sizing: border-box;
        }

        .react-calendar-custom button {
          margin: 0;
          border: 0;
          outline: none;
        }

        .react-calendar-custom button:enabled:hover,
        .react-calendar-custom button:enabled:focus {
          background-color: #f1f5f9;
        }

        .react-calendar-custom__navigation {
          display: flex;
          height: 44px;
          margin-bottom: 1em;
        }

        .react-calendar-custom__navigation button {
          min-width: 44px;
          background: none;
          font-size: 16px;
          font-weight: 500;
        }

        .react-calendar-custom__navigation button:enabled:hover,
        .react-calendar-custom__navigation button:enabled:focus {
          background-color: #f1f5f9;
        }

        .react-calendar-custom__navigation button[disabled] {
          background-color: #f8fafc;
          color: #94a3b8;
        }

        .react-calendar-custom__month-view__weekdays {
          text-align: center;
          text-transform: uppercase;
          font-weight: bold;
          font-size: 0.75em;
          color: #64748b;
        }

        .react-calendar-custom__month-view__weekdays__weekday {
          padding: 0.5em;
        }

        .react-calendar-custom__month-view__weekNumbers .react-calendar-custom__tile {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.75em;
          font-weight: bold;
          color: #64748b;
        }

        .react-calendar-custom__month-view__days__day--weekend {
          color: #ef4444;
        }

        .react-calendar-custom__month-view__days__day--neighboringMonth {
          color: #94a3b8;
        }

        .react-calendar-custom__year-view .react-calendar-custom__tile,
        .react-calendar-custom__decade-view .react-calendar-custom__tile,
        .react-calendar-custom__century-view .react-calendar-custom__tile {
          padding: 2em 0.5em;
        }

        .react-calendar-custom__tile {
          max-width: 100%;
          padding: 10px 6px;
          background: none;
          text-align: center;
          line-height: 16px;
          font-size: 0.833em;
        }

        .react-calendar-custom__tile:disabled {
          background-color: #f8fafc;
          color: #cbd5e1;
        }

        .react-calendar-custom__tile:enabled:hover,
        .react-calendar-custom__tile:enabled:focus {
          background-color: #f1f5f9;
        }

        .react-calendar-custom__tile--now {
          background: #3b82f6;
          color: white;
        }

        .react-calendar-custom__tile--now:enabled:hover,
        .react-calendar-custom__tile--now:enabled:focus {
          background: #2563eb;
        }

        .react-calendar-custom__tile--hasActive {
          background: #1e40af;
        }

        .react-calendar-custom__tile--hasActive:enabled:hover,
        .react-calendar-custom__tile--hasActive:enabled:focus {
          background: #1d4ed8;
        }

        .react-calendar-custom__tile--active {
          background: #3b82f6;
          color: white;
        }

        .react-calendar-custom__tile--active:enabled:hover,
        .react-calendar-custom__tile--active:enabled:focus {
          background: #2563eb;
        }

        .react-calendar-custom--selectRange .react-calendar-custom__tile--hover {
          background-color: #dbeafe;
        }
      `}</style>
    </div>
  );
}

Calendar.displayName = "Calendar";

export { Calendar };
