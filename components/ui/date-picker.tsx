"use client";

import * as React from "react";
import { format, parse, isValid } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DatePickerProps {
  date: Date | null;
  setDate: (date: Date | null) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  allowFutureDates?: boolean;
  fromYear?: number;
  toYear?: number;
}

export function DatePicker({
  date,
  setDate,
  className,
  placeholder = "Pick a date",
  disabled = false,
  allowFutureDates = true,
  fromYear = 1900,
  toYear = new Date().getFullYear() + (allowFutureDates ? 10 : 0)
}: DatePickerProps) {
  const [inputValue, setInputValue] = React.useState<string>("");
  const [isOpen, setIsOpen] = React.useState(false);

  // Update input value when date changes
  React.useEffect(() => {
    if (date) {
      setInputValue(format(date, "yyyy-MM-dd"));
    } else {
      setInputValue("");
    }
  }, [date]);

  // Handle manual input
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    // Try to parse the date
    try {
      const parsedDate = parse(value, "yyyy-MM-dd", new Date());
      if (isValid(parsedDate)) {
        // Check if future dates are allowed
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (!allowFutureDates && parsedDate > today) {
          return; // Don't update if future dates aren't allowed
        }

        setDate(parsedDate);
      }
    } catch (error) {
      // Invalid date format, do nothing
    }
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <div className="flex">
        <Input
          type="date"
          value={inputValue}
          onChange={handleInputChange}
          className="rounded-r-none"
          disabled={disabled}
          min={`${fromYear}-01-01`}
          max={allowFutureDates ? undefined : format(new Date(), "yyyy-MM-dd")}
          placeholder={placeholder}
        />
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant={"outline"}
              className="rounded-l-none border-l-0"
              onClick={() => setIsOpen(true)}
              disabled={disabled}
            >
              <CalendarIcon className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="end">
            <Calendar
              value={date}
              onChange={(newDate) => {
                setDate(newDate);
                setIsOpen(false);
              }}
              disabled={(date) =>
                (!allowFutureDates && date > new Date()) ||
                date < new Date(`${fromYear}-01-01`) ||
                date > new Date(`${toYear}-12-31`)
              }
              minDate={new Date(`${fromYear}-01-01`)}
              maxDate={allowFutureDates ? new Date(`${toYear}-12-31`) : new Date()}
              showNavigation={true}
              showNeighboringMonth={true}
              view="month"
              className="border-0"
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}
