"use client";

import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";

interface EnhancedDatePickerProps {
  date: Date | null;
  setDate: (date: Date | null) => void;
  className?: string;
  allowFutureDates?: boolean;
  placeholder?: string;
  fromYear?: number;
  toYear?: number;
}

export function EnhancedDatePicker({
  date,
  setDate,
  className,
  allowFutureDates = true,
  placeholder = "Pick a date",
  fromYear = 1900,
  toYear = new Date().getFullYear() + (allowFutureDates ? 10 : 0),
}: EnhancedDatePickerProps) {
  const [inputValue, setInputValue] = React.useState<string>(
    date ? format(date, "yyyy-MM-dd") : "",
  );

  // Update input value when date changes externally
  React.useEffect(() => {
    if (date) {
      setInputValue(format(date, "yyyy-MM-dd"));
    } else {
      setInputValue("");
    }
  }, [date]);

  // Handle manual input
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    // Try to parse the date
    if (value) {
      try {
        const parsedDate = new Date(value);

        // Check if it's a valid date
        if (!isNaN(parsedDate.getTime())) {
          // Check if future dates are allowed
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          if (!allowFutureDates && parsedDate > today) {
            // Don't update the date if future dates aren't allowed
            return;
          }

          setDate(parsedDate);
        }
      } catch (error) {
        // Invalid date format, don't update the date
      }
    } else {
      setDate(null);
    }
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <div className="flex gap-2">
        <Input
          type="date"
          value={inputValue}
          onChange={handleInputChange}
          min={`${fromYear}-01-01`}
          max={allowFutureDates ? undefined : format(new Date(), "yyyy-MM-dd")}
          className="flex-1"
        />
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="icon">
              <CalendarIcon className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="end">
            <Calendar
              value={date}
              onChange={setDate}
              disabled={(date) =>
                (!allowFutureDates && date > new Date()) ||
                date < new Date(`${fromYear}-01-01`) ||
                date > new Date(`${toYear}-12-31`)
              }
              minDate={new Date(`${fromYear}-01-01`)}
              maxDate={allowFutureDates ? new Date(`${toYear}-12-31`) : new Date()}
              showNavigation={true}
              showNeighboringMonth={true}
              view="month"
              className="border-0"
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}
