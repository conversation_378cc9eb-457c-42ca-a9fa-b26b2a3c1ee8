"use client";

import { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { MapPin, Search, X } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { useAppStore } from "@/lib/store";

// Add the global callback type
declare global {
  interface Window {
    [key: string]: any;
    google?: any;
  }
}

// Define the Google Maps place result type
interface PlaceResult {
  name: string;
  formatted_address: string;
  address_components: {
    long_name: string;
    short_name: string;
    types: string[];
  }[];
  geometry: {
    location: {
      lat: () => number;
      lng: () => number;
    };
  };
  place_id: string;
  url?: string;
}

interface GoogleMapsSearchProps {
  onSelect: (place: {
    name: string;
    address: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    lat: number;
    lng: number;
    googleMapsUrl: string;
  }) => void;
  initialValue?: string;
  label?: string;
  placeholder?: string;
  className?: string;
}

export function GoogleMapsSearch({
  onSelect,
  initialValue = "",
  label = "Location",
  placeholder = "Search for a location",
  className = "",
}: GoogleMapsSearchProps) {
  const [searchValue, setSearchValue] = useState(initialValue);
  const [predictions, setPredictions] = useState<any[]>([]);
  const [showPredictions, setShowPredictions] = useState(false);
  const [isManualEntry, setIsManualEntry] = useState(false);
  const [savedVenues, setSavedVenues] = useState<any[]>([]);
  const [showSavedVenues, setShowSavedVenues] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const autocompleteService =
    useRef<google.maps.places.AutocompleteService | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);
  const searchRef = useRef<HTMLDivElement>(null);
  const { currentProfile } = useAppStore();

  // Manual entry fields
  const [manualAddress, setManualAddress] = useState({
    name: "",
    address: "",
    city: "",
    state: "",
    zip: "",
    country: "",
  });

  // Load Google Maps API
  useEffect(() => {
    // Check if Google Maps API is already loaded
    if (window.google && window.google.maps) {
      console.log("Google Maps API already loaded");
      initializeServices();
      return;
    }

    console.log("Loading Google Maps API...");

    // Get the API key directly from the environment variable
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

    if (!apiKey) {
      console.error(
        "Google Maps API key is missing. Please add NEXT_PUBLIC_GOOGLE_MAPS_API_KEY to your .env.local file.",
      );
      return;
    }

    console.log("API Key available:", apiKey.substring(0, 5) + "...");

    // Define a unique callback name to avoid conflicts
    const callbackName = "googleMapsCallback" + Date.now();

    // Create a global callback function that will be called when the API is loaded
    window[callbackName] = function () {
      console.log("Google Maps API loaded via callback");
      initializeServices();
    };

    // Load Google Maps API if not already loaded
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=${callbackName}`;
    script.async = true;
    script.defer = true;

    // Handle script load errors
    script.onerror = (error) => {
      console.error("Error loading Google Maps API:", error);
    };

    document.head.appendChild(script);

    return () => {
      // Clean up script and global callback if component unmounts before script loads
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
      if (window[callbackName]) {
        delete window[callbackName];
      }
    };
  }, []);

  // Debug Google Maps API loading
  useEffect(() => {
    const checkGoogleMapsLoaded = () => {
      if (window.google && window.google.maps) {
        console.log("Google Maps API loaded successfully");
        if (window.google.maps.places) {
          console.log("Places library loaded successfully");
          // Try to initialize services again if they weren't initialized earlier
          if (!autocompleteService.current || !placesService.current) {
            console.log("Reinitializing services...");
            initializeServices();
          }
        } else {
          console.error("Places library failed to load");
        }
      } else {
        console.error("Google Maps API failed to load");
      }
    };

    // Check after a short delay to allow script to load
    const timer = setTimeout(checkGoogleMapsLoaded, 2000);
    return () => clearTimeout(timer);
  }, []);

  // Initialize Google Maps services
  const initializeServices = () => {
    if (window.google && window.google.maps && window.google.maps.places) {
      console.log("Initializing Google Maps services");
      try {
        // Initialize AutocompleteService
        if (!autocompleteService.current) {
          autocompleteService.current =
            new google.maps.places.AutocompleteService();
          console.log("AutocompleteService initialized");
        }

        // Initialize PlacesService
        if (!placesService.current) {
          // Create a dummy div for PlacesService (it requires a DOM element)
          const dummyDiv = document.createElement("div");
          dummyDiv.id = "places-service-element-" + Date.now();
          document.body.appendChild(dummyDiv); // Attach to DOM to ensure it works
          placesService.current = new google.maps.places.PlacesService(
            dummyDiv,
          );
          console.log("PlacesService initialized with element:", dummyDiv.id);
        }

        // Test the services
        if (autocompleteService.current) {
          autocompleteService.current.getPlacePredictions(
            { input: "New York" },
            (predictions, status) => {
              console.log("Test prediction status:", status);
              console.log(
                "Test predictions count:",
                predictions ? predictions.length : 0,
              );
            },
          );
        }
      } catch (error) {
        console.error("Error initializing Google Maps services:", error);
      }
    } else {
      console.error("Google Maps or Places library not available");
    }
  };

  // Fetch saved venues
  useEffect(() => {
    const fetchSavedVenues = async () => {
      if (!currentUser) return;

      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from("venues")
          .select("*")
          .eq("user_id", currentUser.id)
          .order("name", { ascending: true });

        if (error) throw error;

        setSavedVenues(data || []);
      } catch (error) {
        console.error("Error fetching venues:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSavedVenues();
  }, [currentUser]);

  // Handle click outside to close predictions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setShowPredictions(false);
        setShowSavedVenues(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    if (!value.trim()) {
      setPredictions([]);
      setShowPredictions(false);
    }
  };

  // Debounced search effect
  useEffect(() => {
    if (!searchValue.trim()) return;

    const timeoutId = setTimeout(() => {
      if (autocompleteService.current) {
        console.log("Fetching predictions for:", searchValue);
        try {
          autocompleteService.current.getPlacePredictions(
            { input: searchValue },
            (predictions, status) => {
              console.log("Prediction status:", status);
              console.log("Predictions:", predictions);

              if (
                status === google.maps.places.PlacesServiceStatus.OK &&
                predictions
              ) {
                setPredictions(predictions);
                setShowPredictions(true);
              } else {
                setPredictions([]);
                setShowPredictions(false);
              }
            },
          );
        } catch (error) {
          console.error("Error getting place predictions:", error);
        }
      } else {
        console.error("AutocompleteService not initialized");
        // Try to initialize services again
        if (window.google && window.google.maps && window.google.maps.places) {
          initializeServices();
        }
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchValue]);

  // Handle prediction selection
  const handlePredictionSelect = (placeId: string) => {
    if (placesService.current) {
      placesService.current.getDetails(
        {
          placeId,
          fields: [
            "name",
            "formatted_address",
            "address_components",
            "geometry",
            "url",
          ],
        },
        (place: PlaceResult | null, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && place) {
            console.log("Place details:", place);

            // Extract address components
            const addressComponents = place.address_components || [];
            const city =
              addressComponents.find(
                (component) =>
                  component.types.includes("locality") ||
                  component.types.includes("administrative_area_level_2"),
              )?.long_name || "";
            const state =
              addressComponents.find((component) =>
                component.types.includes("administrative_area_level_1"),
              )?.long_name || "";
            const zip =
              addressComponents.find((component) =>
                component.types.includes("postal_code"),
              )?.long_name || "";
            const country =
              addressComponents.find((component) =>
                component.types.includes("country"),
              )?.long_name || "";

            // Create place object
            const placeData = {
              name: place.name || "",
              address: place.formatted_address || "",
              city,
              state,
              zip,
              country,
              lat: place.geometry.location.lat(),
              lng: place.geometry.location.lng(),
              googleMapsUrl:
                place.url ||
                `https://www.google.com/maps/place/?q=place_id:${place.place_id}`,
            };

            console.log("Extracted place data:", placeData);

            // Update search value
            setSearchValue(place.name || place.formatted_address || "");
            setShowPredictions(false);

            // Call onSelect callback
            onSelect(placeData);
          } else {
            console.error("Error fetching place details:", status);
          }
        },
      );
    } else {
      console.error("Places service not initialized");
    }
  };

  // Handle saved venue selection
  const handleSavedVenueSelect = (venue: any) => {
    setSearchValue(venue.name);
    setShowSavedVenues(false);

    // Call onSelect callback
    onSelect({
      name: venue.name,
      address: venue.address || "",
      city: venue.city || "",
      state: venue.state || "",
      zip: venue.zip || "",
      country: venue.country || "",
      lat: venue.lat || 0,
      lng: venue.lng || 0,
      googleMapsUrl: venue.google_maps_url || "",
    });
  };

  // Handle manual address submission
  const handleManualAddressSubmit = () => {
    // Create place object from manual entry
    const placeData = {
      name: manualAddress.name,
      address: manualAddress.address,
      city: manualAddress.city,
      state: manualAddress.state,
      zip: manualAddress.zip,
      country: manualAddress.country,
      lat: 0,
      lng: 0,
      googleMapsUrl: "",
    };

    // Update search value
    setSearchValue(manualAddress.name);

    // Call onSelect callback
    onSelect(placeData);

    // Reset manual entry mode
    setIsManualEntry(false);
  };

  return (
    <div className={`space-y-2 ${className}`} ref={searchRef}>
      {label && <Label htmlFor="location-search">{label}</Label>}

      <div className="relative">
        <div className="relative">
          <Input
            id="location-search"
            value={searchValue}
            onChange={handleSearchChange}
            placeholder={placeholder}
            className="pr-10 border-primary/50 focus:border-primary focus-visible:ring-primary/30"
          />
          {searchValue && (
            <button
              className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
              onClick={() => {
                setSearchValue("");
                setPredictions([]);
                setShowPredictions(false);
              }}
              type="button"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>

        {/* Google Maps predictions */}
        {showPredictions && predictions.length > 0 && (
          <div className="absolute z-10 w-full mt-1 bg-background border rounded-md shadow-md max-h-60 overflow-y-auto">
            {predictions.map((prediction) => (
              <div
                key={prediction.place_id}
                className="p-2 hover:bg-muted cursor-pointer"
                onClick={() => handlePredictionSelect(prediction.place_id)}
              >
                <div className="font-medium">
                  {prediction.structured_formatting.main_text}
                </div>
                <div className="text-sm text-muted-foreground">
                  {prediction.structured_formatting.secondary_text}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Saved venues - only show if we have saved venues */}
        {showSavedVenues && savedVenues.length > 0 && (
          <div className="absolute z-10 w-full mt-1 bg-background border rounded-md shadow-md max-h-60 overflow-y-auto">
            <div className="p-2 border-b">
              <div className="font-medium">Saved Venues</div>
            </div>
            {savedVenues.map((venue) => (
              <div
                key={venue.id}
                className="p-2 hover:bg-muted cursor-pointer"
                onClick={() => handleSavedVenueSelect(venue)}
              >
                <div className="font-medium">{venue.name}</div>
                <div className="text-sm text-muted-foreground">
                  {venue.address}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
