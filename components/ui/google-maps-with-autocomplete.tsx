"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { Label } from "@/components/ui/label";
import { X, MapPin } from "lucide-react";

interface Coordinates {
  lat: number;
  lng: number;
}

interface GoogleMapsWithAutocompleteProps {
  onSelect: (place: {
    name: string;
    address: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    lat: number;
    lng: number;
    place_id: string;
    googleMapsUrl: string;
  }) => void;
  initialValue?: string;
  initialCoordinates?: Coordinates;
  label?: string;
  placeholder?: string;
  className?: string;
}

export function GoogleMapsWithAutocomplete({
  onSelect,
  initialValue = "",
  initialCoordinates = { lat: 37.7749, lng: -122.4194 }, // Default to San Francisco
  label = "Location",
  placeholder = "Search for a location",
  className = "",
}: GoogleMapsWithAutocompleteProps) {
  const [searchValue, setSearchValue] = useState(initialValue);
  const [map, setMap] = useState<any>(null);
  const [marker, setMarker] = useState<any>(null);
  const mapRef = useRef<HTMLDivElement>(null);
  const autocompleteRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle place selection
  const handlePlaceSelect = useCallback((place: any) => {
    if (!place.geometry || !place.geometry.location) {
      console.error("No geometry information available for this place");
      return;
    }

    console.log("Place selected:", place);

    // Update map and marker
    if (map && marker) {
      // Center the map on the selected location
      map.setCenter(place.geometry.location);
      map.setZoom(15);

      // Update the marker position based on its type
      if (marker && marker.setPosition) {
        marker.setPosition(place.geometry.location);
      } else if (marker && marker.position) {
        marker.position = place.geometry.location;
      }
    }

    // Extract address components
    const addressComponents = place.address_components || [];
    const city =
      addressComponents.find(
        (component) =>
          component.types.includes("locality") ||
          component.types.includes("administrative_area_level_2"),
      )?.long_name || "";
    const state =
      addressComponents.find((component) =>
        component.types.includes("administrative_area_level_1"),
      )?.long_name || "";
    const zip =
      addressComponents.find((component) =>
        component.types.includes("postal_code"),
      )?.long_name || "";
    const country =
      addressComponents.find((component) => component.types.includes("country"))
        ?.long_name || "";

    // Create place object
    const placeData = {
      name: place.name || "",
      address: place.formatted_address || "",
      city,
      state,
      zip,
      country,
      lat: place.geometry.location.lat(),
      lng: place.geometry.location.lng(),
      place_id: place.place_id || "",
      googleMapsUrl: `https://www.google.com/maps/place/?q=place_id:${place.place_id}`,
    };

    // Update search value
    setSearchValue(place.name || place.formatted_address || "");

    // Call onSelect callback
    onSelect(placeData);
  }, [map, marker, onSelect]);

  // Initialize map and autocomplete
  const initializeMap = useCallback(() => {
    if (
      !mapRef.current ||
      !autocompleteRef.current ||
      !window.google ||
      !window.google.maps
    )
      return;

    console.log("Initializing Google Maps");

    // Create the map
    const mapOptions: google.maps.MapOptions = {
      center: initialCoordinates,
      zoom: 15,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
    };

    const newMap = new google.maps.Map(mapRef.current, mapOptions);
    setMap(newMap);

    // Create a simple search input first
    const searchInput = document.createElement("input");
    searchInput.type = "text";
    searchInput.placeholder = placeholder;
    searchInput.value = initialValue;
    searchInput.className =
      "flex h-10 w-full rounded-md border border-primary/50 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/30 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50";

    // Clear existing content and append the search input
    autocompleteRef.current.innerHTML = "";
    autocompleteRef.current.appendChild(searchInput);

    // Create an Autocomplete instance (fallback to older API for now)
    const autocompleteInstance = new google.maps.places.Autocomplete(
      searchInput,
      {
        fields: [
          "address_components",
          "geometry",
          "name",
          "formatted_address",
          "place_id",
        ],
      },
    );

    // Create a marker using AdvancedMarkerElement if available, otherwise fallback to Marker
    let newMarker:
      | google.maps.Marker
      | google.maps.marker.AdvancedMarkerElement;

    if (google.maps.marker && google.maps.marker.AdvancedMarkerElement) {
      // Use the new AdvancedMarkerElement
      newMarker = new google.maps.marker.AdvancedMarkerElement({
        position: initialCoordinates,
        map: newMap,
        draggable: true,
        title: "Drag to adjust location",
      });
    } else {
      // Fallback to the deprecated Marker
      newMarker = new google.maps.Marker({
        position: initialCoordinates,
        map: newMap,
        draggable: true,
        animation: google.maps.Animation.DROP,
      });
    }

    setMarker(newMarker);

    // Add place_changed event listener
    autocompleteInstance.addListener("place_changed", () => {
      const place = autocompleteInstance.getPlace();
      if (place && place.geometry && place.geometry.location) {
        // Update the map to center on the selected location
        newMap.setCenter(place.geometry.location);
        newMap.setZoom(15);

        // Update the marker position
        if (newMarker) {
          if (newMarker instanceof google.maps.Marker) {
            newMarker.setPosition(place.geometry.location);
          } else if (
            newMarker instanceof google.maps.marker.AdvancedMarkerElement
          ) {
            newMarker.position = place.geometry.location;
          }
        }

        handlePlaceSelect(place);
      }
    });

    // Add marker drag event
    newMarker.addListener("dragend", () => {
      if (newMarker && newMap) {
        // @ts-ignore - getPosition exists on both types but TypeScript doesn't know
        const position = newMarker.getPosition();
        if (position) {
          newMap.setCenter(position);

          // Reverse geocode the position
          const geocoder = new google.maps.Geocoder();
          geocoder.geocode({ location: position }, (results, status) => {
            if (
              status === google.maps.GeocoderStatus.OK &&
              results &&
              results[0]
            ) {
              handlePlaceSelect(results[0]);

              // Update the search input value
              if (searchInput && results[0].formatted_address) {
                searchInput.value = results[0].formatted_address;
                setSearchValue(results[0].formatted_address);
              }
            }
          });
        }
      }
    });
  }, [initialCoordinates, placeholder, initialValue, handlePlaceSelect]);

  // Load Google Maps API
  useEffect(() => {
    // Check if Google Maps API is already loaded
    if (window.google && window.google.maps) {
      console.log("Google Maps API already loaded");
      initializeMap();
      return;
    }

    console.log("Loading Google Maps API...");

    // Get the API key directly from the environment variable
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

    if (!apiKey) {
      console.error(
        "Google Maps API key is missing. Please add NEXT_PUBLIC_GOOGLE_MAPS_API_KEY to your .env.local file.",
      );
      // Set a fallback value for the search field
      setSearchValue("Google Maps API key is missing");
      return;
    }

    console.log("API Key available:", apiKey.substring(0, 5) + "...");

    // Define a unique callback name to avoid conflicts
    const callbackName = "googleMapsCallback" + Date.now();

    // Create a global callback function that will be called when the API is loaded
    window[callbackName] = function () {
      console.log("Google Maps API loaded via callback");
      initializeMap();
    };

    // Load Google Maps API if not already loaded - using best practices with loading=async
    const script = document.createElement("script");
    // Make sure the API key is properly URL encoded
    const encodedApiKey = encodeURIComponent(apiKey);
    script.src = `https://maps.googleapis.com/maps/api/js?key=${encodedApiKey}&libraries=places&callback=${callbackName}&loading=async`;
    script.async = true;
    script.defer = true; // Add defer attribute

    console.log(
      "Loading Google Maps with API key:",
      apiKey.substring(0, 8) + "...",
    );

    // Handle script load errors
    script.onerror = (error) => {
      console.error("Error loading Google Maps API:", error);
      setSearchValue("Error loading Google Maps API");
    };

    // Add a timeout to detect if the script fails to load
    const timeoutId = setTimeout(() => {
      if (!window.google || !window.google.maps) {
        console.error("Google Maps API failed to load (timeout)");
        setSearchValue("Google Maps API failed to load");
      }
    }, 10000); // 10 seconds timeout

    document.head.appendChild(script);

    return () => {
      // Clean up script and global callback if component unmounts before script loads
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
      if (window[callbackName]) {
        delete window[callbackName];
      }
      clearTimeout(timeoutId);
    };
  }, [initializeMap]);

  return (
    <div className={`space-y-4 ${className}`} ref={containerRef}>
      {label && <Label>{label}</Label>}

      {/* PlaceAutocompleteElement will be inserted here */}
      <div ref={autocompleteRef} className="relative"></div>

      <div
        ref={mapRef}
        className="w-full h-[200px] rounded-md border border-input overflow-hidden"
      />

      {/* Location pin indicator */}
      <div className="flex items-center text-sm text-muted-foreground">
        <MapPin className="h-4 w-4 mr-1" />
        {searchValue ? (
          <span>{searchValue}</span>
        ) : (
          <span>Search for a location or drag the pin on the map</span>
        )}
      </div>
    </div>
  );
}

// Add the global callback type
declare global {
  interface Window {
    [key: string]: any;
    google?: any;
  }
}
