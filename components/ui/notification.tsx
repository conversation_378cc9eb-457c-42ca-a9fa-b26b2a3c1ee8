"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>, X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAppStore } from "@/lib/store";
import { cn } from "@/lib/utils";
import {
  useNotificationsStore,
  Notification,
} from "@/lib/stores/notificationsStore";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export function NotificationPopover() {
  const { currentProfile } = useAppStore();
  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    muteCategory,
  } = useNotificationsStore();
  const [open, setOpen] = useState(false);

  // Fetch notifications when the component mounts or when the user changes
  useEffect(() => {
    if (currentUser?.id) {
      fetchNotifications();
    }
  }, [currentUser, fetchNotifications]);

  // Refresh notifications when the popover opens
  useEffect(() => {
    if (open && currentUser?.id) {
      fetchNotifications();
    }
  }, [open, currentUser, fetchNotifications]);

  // Helper function to get icon for notification type
  const getNotificationIcon = (notification: Notification) => {
    switch (notification.category) {
      case "critical":
        return <Bell className="h-4 w-4 text-red-500" />;
      case "informational":
        return <Bell className="h-4 w-4 text-blue-500" />;
      case "social":
        return <Bell className="h-4 w-4 text-green-500" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  // Handle muting a notification type
  const handleMuteType = (
    notification: Notification,
    event: React.MouseEvent,
  ) => {
    event.stopPropagation();
    muteCategory(notification.category, true);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;

    return date.toLocaleDateString();
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative text-crescender-600 hover:text-crescender-700"
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-crescender-600 text-xs text-white">
              {unreadCount > 9 ? "9+" : unreadCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between border-b p-3">
          <h4 className="font-medium">Notifications</h4>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-auto px-2 py-1 text-xs"
              onClick={() => markAllAsRead()}
            >
              Mark all as read
            </Button>
          )}
        </div>
        <ScrollArea className="h-[300px]">
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-crescender-600"></div>
            </div>
          ) : !currentUser ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <Bell className="h-12 w-12 text-crescender-300 mb-3" />
              <h3 className="text-base font-medium mb-1">
                Sign in to view notifications
              </h3>
              <p className="text-sm text-muted-foreground">
                Please log in to see your notifications
              </p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <Bell className="h-12 w-12 text-red-300 mb-3" />
              <h3 className="text-base font-medium mb-1">
                Error loading notifications
              </h3>
              <p className="text-sm text-muted-foreground">{error}</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={() => fetchNotifications()}
              >
                Try Again
              </Button>
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <Bell className="h-12 w-12 text-crescender-300 mb-3" />
              <h3 className="text-base font-medium mb-1">No notifications</h3>
              <p className="text-sm text-muted-foreground">
                You're all caught up!
              </p>
            </div>
          ) : (
            <div className="divide-y">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={cn(
                    "flex cursor-pointer flex-col p-3 hover:bg-accent/50 group relative",
                    !notification.read && "bg-accent/20",
                  )}
                  onClick={() => {
                    if (!notification.read) {
                      markAsRead(notification.id);
                    }
                    if (notification.link) {
                      window.location.href = notification.link;
                    }
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getNotificationIcon(notification)}
                      <h5 className="font-medium">{notification.title}</h5>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {formatDate(notification.created_at)}
                    </span>
                  </div>
                  <p className="mt-1 text-sm">{notification.message}</p>

                  {/* Mute button - only visible on hover */}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="absolute right-2 top-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={(e) => handleMuteType(notification, e)}
                        >
                          <BellOff className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Mute {notification.category} notifications</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
