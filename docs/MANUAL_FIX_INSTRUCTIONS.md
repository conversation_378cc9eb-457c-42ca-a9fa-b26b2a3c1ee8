# Manual Fix Instructions for User Roles RLS Issue

If the automated scripts don't work, you can apply the fix manually using the Supabase dashboard.

## Option 1: Run SQL in the Supabase Dashboard

1. Log in to the [Supabase Dashboard](https://app.supabase.com)
2. Select your project
3. Go to the "SQL Editor" section
4. Create a new query
5. Copy and paste the following SQL:

```sql
-- Migration to fix RLS policies for user_roles table
-- This ensures that parents can view and update their children's roles

-- First, drop existing policies that might conflict
DROP POLICY IF EXISTS "Allow users to view their own roles" ON public.user_roles;
DROP POLICY IF EXISTS "Allow users to update their own roles" ON public.user_roles;
DROP POLICY IF EXISTS "Allow users to set their own roles" ON public.user_roles;
DROP POLICY IF EXISTS "Allow parents to view their children's roles" ON public.user_roles;
DROP POLICY IF EXISTS "Allow parents to update their children's roles" ON public.user_roles;

-- Create policy for users to view their own roles
CREATE POLICY "Allow users to view their own roles"
ON public.user_roles FOR SELECT
USING (
  auth.uid()::text = id
);

-- Create policy for users to update their own roles
CREATE POLICY "Allow users to update their own roles"
ON public.user_roles FOR UPDATE
USING (
  auth.uid()::text = id
);

-- Create policy for users to insert their own roles
CREATE POLICY "Allow users to set their own roles"
ON public.user_roles FOR INSERT
WITH CHECK (
  auth.uid()::text = id
);

-- Create policy for parents to view their children's roles via profiles.parent_id
CREATE POLICY "Allow parents to view their children's roles via parent_id"
ON public.user_roles FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id::text = user_roles.id
    AND profiles.parent_id = auth.uid()
    AND profiles.profile_type = 'child'
  )
);

-- Create policy for parents to update their children's roles via profiles.parent_id
CREATE POLICY "Allow parents to update their children's roles via parent_id"
ON public.user_roles FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id::text = user_roles.id
    AND profiles.parent_id = auth.uid()
    AND profiles.profile_type = 'child'
  )
);

-- Create policy for parents to view their children's roles via parent_child table
CREATE POLICY "Allow parents to view their children's roles via parent_child"
ON public.user_roles FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM parent_child
    WHERE parent_child.child_id::text = user_roles.id
    AND parent_child.parent_id = auth.uid()
  )
);

-- Create policy for parents to update their children's roles via parent_child table
CREATE POLICY "Allow parents to update their children's roles via parent_child"
ON public.user_roles FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM parent_child
    WHERE parent_child.child_id::text = user_roles.id
    AND parent_child.parent_id = auth.uid()
  )
);

-- Create policy for parents to view their children's roles via parent_child_links table
CREATE POLICY "Allow parents to view their children's roles via parent_child_links"
ON public.user_roles FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM parent_child_links
    WHERE parent_child_links.child_id::text = user_roles.id
    AND parent_child_links.parent_id = auth.uid()
  )
);

-- Create policy for parents to update their children's roles via parent_child_links table
CREATE POLICY "Allow parents to update their children's roles via parent_child_links"
ON public.user_roles FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM parent_child_links
    WHERE parent_child_links.child_id::text = user_roles.id
    AND parent_child_links.parent_id = auth.uid()
  )
);

-- Make sure RLS is enabled on the user_roles table
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
```

6. Click "Run" to execute the SQL

## Option 2: Update RLS Policies via the Supabase Dashboard UI

1. Log in to the [Supabase Dashboard](https://app.supabase.com)
2. Select your project
3. Go to the "Authentication" section
4. Click on "Policies" in the sidebar
5. Find the "user_roles" table
6. Delete any existing policies for the table
7. Add the following policies one by one:

### Policy 1: Allow users to view their own roles
- Policy name: "Allow users to view their own roles"
- Operation: SELECT
- Using expression: `auth.uid()::text = id`

### Policy 2: Allow users to update their own roles
- Policy name: "Allow users to update their own roles"
- Operation: UPDATE
- Using expression: `auth.uid()::text = id`

### Policy 3: Allow users to set their own roles
- Policy name: "Allow users to set their own roles"
- Operation: INSERT
- With check expression: `auth.uid()::text = id`

### Policy 4: Allow parents to view their children's roles via parent_id
- Policy name: "Allow parents to view their children's roles via parent_id"
- Operation: SELECT
- Using expression: 
```sql
EXISTS (
  SELECT 1 FROM profiles
  WHERE profiles.id::text = user_roles.id
  AND profiles.parent_id = auth.uid()
  AND profiles.profile_type = 'child'
)
```

### Policy 5: Allow parents to update their children's roles via parent_id
- Policy name: "Allow parents to update their children's roles via parent_id"
- Operation: UPDATE
- Using expression:
```sql
EXISTS (
  SELECT 1 FROM profiles
  WHERE profiles.id::text = user_roles.id
  AND profiles.parent_id = auth.uid()
  AND profiles.profile_type = 'child'
)
```

### Policy 6: Allow parents to view their children's roles via parent_child
- Policy name: "Allow parents to view their children's roles via parent_child"
- Operation: SELECT
- Using expression:
```sql
EXISTS (
  SELECT 1 FROM parent_child
  WHERE parent_child.child_id::text = user_roles.id
  AND parent_child.parent_id = auth.uid()
)
```

### Policy 7: Allow parents to update their children's roles via parent_child
- Policy name: "Allow parents to update their children's roles via parent_child"
- Operation: UPDATE
- Using expression:
```sql
EXISTS (
  SELECT 1 FROM parent_child
  WHERE parent_child.child_id::text = user_roles.id
  AND parent_child.parent_id = auth.uid()
)
```

### Policy 8: Allow parents to view their children's roles via parent_child_links
- Policy name: "Allow parents to view their children's roles via parent_child_links"
- Operation: SELECT
- Using expression:
```sql
EXISTS (
  SELECT 1 FROM parent_child_links
  WHERE parent_child_links.child_id::text = user_roles.id
  AND parent_child_links.parent_id = auth.uid()
)
```

### Policy 9: Allow parents to update their children's roles via parent_child_links
- Policy name: "Allow parents to update their children's roles via parent_child_links"
- Operation: UPDATE
- Using expression:
```sql
EXISTS (
  SELECT 1 FROM parent_child_links
  WHERE parent_child_links.child_id::text = user_roles.id
  AND parent_child_links.parent_id = auth.uid()
)
```

## Verifying the Fix

After applying the fix, restart your application and check if the unauthorized profile fetches are still occurring. The error logs should no longer show 406 (Not Acceptable) errors for profile fetches.
