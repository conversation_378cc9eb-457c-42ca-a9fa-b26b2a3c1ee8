# Setlist Manager Project Plan

## Current State

The application currently manages setlists with a JSONB-based storage mechanism for songs, replacing the previous join table approach. The primary song data source is Genius API, with limited metadata.

## Spotify Migration Plan

### Epic: Migrate from Genius to Spotify as Primary Music Data Source

#### Phase 1: Setup & Authentication (Current Sprint)
- [x] Configure Spotify Web API integration in the backend
- [x] Add Spotify API service using client credentials flow
- [x] Create provider abstraction layer for music data sources
- [x] Implement Spotify OAuth flow via Supabase Auth
- [x] Create settings page for connecting Spotify accounts

#### Phase 2: Hybrid Operation (Next Sprint)
- [ ] Add database columns to store Spotify IDs for songs
- [ ] Create bulk enrichment job to add Spotify metadata to existing songs
- [ ] Update search UI to show provider source and enhanced metadata
- [ ] Add playlist import functionality from connected Spotify accounts
- [ ] Implement song matching algorithm between Genius and Spotify

#### Phase 3: Data Backfill (Sprint +2)
- [ ] Create admin tool for bulk metadata enrichment
- [ ] Run migration to match existing songs with Spotify tracks
- [ ] Add database metrics for tracking source coverage
- [ ] Create fallback mechanisms when Spotify data is unavailable
- [ ] Add manual link/unlink capability for incorrect matches

#### Phase 4: Feature Expansion (Sprint +3)
- [ ] Add audio preview capability using Spotify
- [ ] Implement playlist synchronization (Spotify ↔ Setlists)
- [ ] Create analytics dashboard for setlist performance metrics
- [ ] Add "Similar Songs" recommendations using Spotify audio features
- [ ] Implement smart song ordering based on musical key progression

#### Phase 5: Genius Deprecation (Sprint +4)
- [ ] Audit application for remaining Genius dependencies
- [ ] Create migration plan for any unmatchable Genius songs
- [ ] Update documentation to reference Spotify as primary source
- [ ] Move Genius provider to "legacy" status with warning banners
- [ ] Implement final database changes to formalize Spotify as primary source

## Technical Implementation Details

### Database Changes
- Add `spotify_id` column to songs table (VARCHAR)
- Add audio feature columns:
  - `tempo` (NUMERIC)
  - `key` (INTEGER)
  - `mode` (INTEGER)
  - `time_signature` (INTEGER)
  - `danceability` (NUMERIC)
  - `energy` (NUMERIC)
  - `acousticness` (NUMERIC)
  - `instrumentalness` (NUMERIC)
  - `liveness` (NUMERIC)
  - `valence` (NUMERIC)
- Create new `spotify_playlists` table for caching user playlists

### API Routes
- `/api/songs` - Enhanced to pull from multiple providers
- `/api/songs/[id]` - Added provider handling and enrichment
- `/api/spotify/search` - Created for direct Spotify searches
- `/api/spotify/track/[id]` - Created for fetching track details
- `/api/spotify/playlists` - To be added for user playlist access

### UI/UX Improvements
- Setlist Manager now displays enhanced metadata like tempo and key
- Settings page for Spotify connection
- Song search displays provider icons and additional metadata
- Future: Playlist import modal for Spotify playlists

## Success Metrics
- 90%+ of songs have Spotify metadata
- User adoption of Spotify connection feature
- Reduction in manual song data entry
- Improved user engagement with enhanced metadata

## Project Plan: Music Library & Setlist Feature

**Overall Goal:** Enhance the application to allow users to build a personal song library with rich metadata and create/manage setlists using these songs.

**Phase 1: Song Library Foundation (In Progress / Nearing Completion)**

*   **[DONE] Data Modeling:** Define robust `Song` data structures (Supabase schema, TypeScript types) to include common fields (title, artist, album, duration, release date, genres) and provider-specific data (Spotify ID, audio features, ISRC; Genius ID, URL, etc.) using JSONB columns (`spotify_data`, `genius_data`).
*   **[DONE] External Service Integration:**
    *   Spotify: Fetch track details, audio features.
    *   Genius: Fetch song details (lyrics URL, primary artist, album).
*   **[DONE] Song Search API (`/api/songs/search`):
    *   Accepts a query string.
    *   Searches internal database (user's songs).
    *   Searches Spotify & Genius.
    *   Deduplicates and merges results, prioritizing user's existing versions.
    *   Returns a standardized `SongSearchResultItem` list.
*   **[DONE] Song Creation UI (`CreateSongForm`):
    *   Input for search query.
    *   Displays search results clearly, indicating data sources (DB, Spotify, Genius).
    *   Allows "Quick Add" of a song from search results, mapping API data to the `SongCreationData` structure for saving.
    *   Handles cases where no results are found, prompting to refine search or acknowledge no external results.
*   **[DONE] Backend Song Creation Logic (`addSongAction`, `songQueries.createSong`):
    *   Accepts `SongCreationData`.
    *   Handles authenticated Supabase client for RLS.
    *   Saves song to the database, correctly populating JSONB fields.
*   **[DONE] Song List UI (`SongList`):
    *   Displays user's songs in a data table.
    *   Uses `getDisplayableSongData` helper to correctly access fields from top-level or JSONB data.
    *   Row click updates selected song for detail view.
    *   Action menu per song (View Details, Add to Setlist, Delete).
    *   Authenticates Supabase client for data fetching.
*   **[DONE] Song Detail UI (`SongDetail`):
    *   Displays comprehensive details of a selected song, using the new dynamic, image-driven layout.
    *   Utilizes `getDisplayableSongData`.
    *   Handles color palette extraction from album art.
    *   Shows Spotify and Genius data (with links to external sites) using new SVG icons.
    *   Includes "Add to Setlist" (with submenu), Edit (placeholder), and Delete buttons.
    *   Authenticates Supabase client for data fetching.
*   **[DONE] Song Deletion:** Logic to delete a song from the library (client-side update and backend call).
*   **[IN PROGRESS] Song Editing UI & Logic:** 
    *   Form/modal to edit existing song details.
    *   Backend update logic (`songQueries.updateSong`).
*   **[TODO] Song Variants:** UI and logic for creating/managing different versions/arrangements of a song (e.g., acoustic, live). *(Currently, `SongVariantForm` and related logic exist but are not fully integrated into the new `SongDetail` view)*.
*   **[DONE] Helper Utilities:** (`song-utils.ts`, `date-utils.ts`) for common data transformations.
*   **[DONE] SVG Icons:** (`icons.tsx`) for provider logos.

**Phase 2: Setlist Management (Next)**

*   **[TODO] Data Modeling (Setlist & SetlistEntry):**
    *   Supabase schema for `setlists` (id, user_id, title, description, created_at, updated_at, slug, etc.).
    *   Supabase schema for `setlist_entries` (id, setlist_id, song_id, position, notes, variant_id, etc.). Define what a `song_id` refers to (e.g. `songs.id`). Consider if `song_uuid` is still relevant or should be `song_id` referring to the integer ID.
    *   TypeScript types for `Setlist` and `SetlistEntry`.
*   **[TODO] Setlist Queries (`setlistQueries.ts`):
    *   `createSetlist(userId, data)`
    *   `fetchSetlists(userId)` - (Partially done, used in song list/detail for "Add to Setlist")
    *   `fetchSetlistById(setlistId, userId)`
    *   `fetchSetlistBySlug(slug, userId)` - (Partially done, used in `SongList` for adding song to setlist, but API structure to be confirmed).
    *   `updateSetlist(setlistId, data, userId)`
    *   `deleteSetlist(setlistId, userId)`
    *   `addSongToSetlist(setlistId, songId, entryData, userId)` - (Partially done via API in `SongList`/`SongDetail`, but a direct query function would be good).
    *   `removeSongFromSetlist(setlistId, entryId, userId)`
    *   `updateSongInSetlist(setlistId, entryId, entryData, userId)` (e.g., change notes, variant, position).
*   **[TODO] Setlist API Routes (`/api/setlists/...`):
    *   `POST /api/setlists` (Create new setlist)
    *   `GET /api/setlists` (List user's setlists)
    *   `GET /api/setlists/[slug]` (Get specific setlist by slug, including its songs/entries) - (Partially done, used by `SongList`)
    *   `PATCH /api/setlists/[slug]` (Update setlist details or reorder songs) - (Partially done, used by `SongList`)
    *   `DELETE /api/setlists/[slug]` (Delete setlist)
    *   *(Potentially separate routes for setlist entries if needed for more granular control, e.g., `/api/setlists/[slug]/entries`)*
*   **[TODO] Setlist List UI (`app/setlists/page.tsx`):
    *   Display a list of the user's setlists (cards or table).
    *   Link to view/edit individual setlists.
    *   Button to create a new setlist.
*   **[TODO] Create Setlist UI (`app/setlists/add/page.tsx` or Modal):
    *   Form for setlist title, description, etc.
*   **[TODO] Setlist Detail/Edit UI (`app/setlists/[slug]/page.tsx`):
    *   Display setlist title, description.
    *   Show songs in the setlist (reorderable list - drag and drop).
    *   For each song in the setlist:
        *   Display title, artist.
        *   Allow adding notes specific to this setlist entry.
        *   Potentially select a specific song variant (if variants are implemented).
    *   Ability to remove songs from the setlist.
    *   Ability to search and add more songs to this setlist (could reuse parts of `CreateSongForm` or a simpler search).

**Phase 3: Advanced Features & Refinements (Future)**

*   **[TODO] Background Song Enrichment (`song-post-processor.ts`):** Job to fill in missing Spotify/Genius data for songs added manually or with minimal info.
*   **[TODO] Public Profile Integration:** Display user's setlists or favorite songs on their public profile.
*   **[TODO] Sharing:** Share setlists with other users or publicly.
*   **[TODO] Collaboration:** Allow multiple users to collaborate on setlists.
*   **[TODO] UI/UX Polish:** Further refinements based on user feedback.

**Notes & Considerations:**
*   **Authentication & RLS:** Ensure all database queries and API routes properly implement Row Level Security based on `user_id` and `profile_id`.
*   **Error Handling:** Robust error handling and user feedback (e.g., toasts) throughout.
*   **Performance:** Optimize queries and data loading, especially for large song libraries or long setlists.
*   **Code Reusability:** Create reusable components and utility functions.
*   **Types:** Maintain strong typing with TypeScript.

Last Updated: 26/04/2023 