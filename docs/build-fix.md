# Build Fix Progress Checklist

## Phase 1: Fix all req.cookies Promise vs sync errors (TS2739)

- [x] app/api/children/username/[username]/route.ts
- [x] app/api/children/verify/route.ts
- [x] app/api/debug/instruments/route.ts
- [x] app/api/deviceapps/children/verify-code/route.ts
- [x] app/api/deviceapps/children/verify-shared-code/route.ts
- [x] app/api/gear/[id]/favourite/route.ts
- [x] app/api/gear/[id]/images/route.ts
- [x] app/api/gear/[id]/route.ts
- [x] app/api/gear/[id]/subitems/route.ts
- [x] app/api/gear/brands/route.ts
- [x] app/api/gear/route.ts
- [x] app/api/gear/test/route.ts
- [x] app/api/genres/route.ts
- [x] app/api/notifications/device-token/route.ts
- [x] app/api/notifications/mark-read/route.ts
- [x] app/api/notifications/mute-category/route.ts
- [x] app/api/notifications/send/route.ts
- [x] app/api/parent/add-child/route.ts
- [x] app/api/parent/verify-token/route.ts
- [x] app/api/practice/route.ts
- [x] app/api/practice/stats/route.ts
- [x] app/api/profile/public-settings/route.ts
- [x] app/api/profile/social-links/route.ts
- [x] app/api/profiles/[id]/route.ts
- [x] app/api/public-profile/[username]/route.ts
- [x] app/api/setlists/[slug]/entries/route.ts
- [x] app/api/setlists/[slug]/route.ts
- [x] app/api/setlists/route.ts
- [x] app/api/songs/[id]/route.ts
- [x] app/api/songs/route.ts
- [x] app/api/songs/search/route.ts
- [x] app/api/spotify/import-setlist/route.ts
- [x] app/api/spotify/playlist/route.ts
- [x] app/api/spotify/track/[id]/route.ts
- [x] app/api/transactions/route.ts
- [x] app/api/receipts/route.ts
- [x] app/api/receipts/process/route.ts

**Status:** Phase 1 COMPLETE — All API route files now use synchronous cookies() as required by Next.js 13+.

---

**Next:** Proceed to the "'error' is of type 'unknown'" set (Phase 2).

⸻

🗂 De-duplicated Error Summary

1. "Type 'ReadonlyRequestCookies' is missing … Promise<ReadonlyRequestCookies>"

All these API route files assume req.cookies returns a Promise. In Next.js 13, req.cookies is synchronous.
	•	app/api/children/username/[username]/route.ts
	•	app/api/children/verify/route.ts
	•	app/api/debug/instruments/route.ts
	•	app/api/deviceapps/children/verify-code/route.ts
	•	app/api/deviceapps/children/verify-shared-code/route.ts
	•	app/api/gear/[id]/favourite/route.ts
	•	app/api/gear/[id]/images/route.ts
	•	app/api/gear/[id]/route.ts
	•	app/api/gear/[id]/subitems/route.ts
	•	app/api/gear/brands/route.ts
	•	app/api/gear/route.ts
	•	app/api/gear/test/route.ts
	•	app/api/genres/route.ts
	•	app/api/notifications/device-token/route.ts
	•	app/api/notifications/mark-read/route.ts
	•	app/api/notifications/mute-category/route.ts
	•	app/api/notifications/send/route.ts
	•	app/api/parent/add-child/route.ts
	•	app/api/parent/verify-token/route.ts
	•	app/api/practice/route.ts
	•	app/api/practice/stats/route.ts
	•	app/api/profile/public-settings/route.ts
	•	app/api/profile/social-links/route.ts
	•	app/api/profiles/[id]/route.ts
	•	app/api/public-profile/[username]/route.ts (implicitly via some uses)
	•	app/api/setlists/[slug]/entries/route.ts
	•	app/api/setlists/[slug]/route.ts
	•	app/api/setlists/route.ts
	•	app/api/songs/[id]/route.ts
	•	app/api/songs/route.ts
	•	app/api/songs/search/route.ts
	•	app/api/spotify/import-setlist/route.ts
	•	app/api/spotify/playlist/route.ts
	•	app/api/spotify/track/[id]/route.ts
	•	app/api/transactions/route.ts

(All above report TS2739.)

⸻

2. "'error' is of type 'unknown'"

Any catch (error) { … } without narrowing.
	•	app/api/cron/notifications/route.ts
	•	components/dashboard/migration-helper.tsx
	•	(Possibly others in your workspace if they have untyped catch.)

(Reports TS18046.)

⸻

3. "Property 'ip' does not exist on type 'NextRequest'"

Express-style req.ip used in App Router.
	•	app/api/deviceapps/children/login/route.ts

(Reports TS2339.)

⸻

4. "Cannot find name '…'"

Typo or missing import/stub for various helper functions or variables.
	•	app/api/deviceapps/children/verify-shared-code/route.ts ("verifyAccessCode")
	•	app/api/profiles/[id]/route.ts ("id")
	•	app/api/songs/[id]/route.ts ("id")
	•	components/gear/dbfull/[id]/page.tsx ("authUser")
	•	Many components referencing currentUser, setCurrentUser, or other state values not declared on AppState. E.g.:
	•	app/calendar/availability/page.tsx
	•	app/calendar/create-event-form.tsx
	•	app/calendar/events/[id]/page.tsx
	•	app/calendar/events/edit/[id]/page.tsx
	•	app/calendar/events/new/enhanced-event-form.tsx
	•	app/calendar/events/page.tsx
	•	app/calendar/page.tsx
	•	app/children/[username]/edit/page.tsx
	•	app/curriculum/create-curriculum-form.tsx
	•	app/curriculum/create-lesson-plan-form.tsx
	•	app/deviceapps/kids/mobile/android/dashboard/[username]/page.tsx
	•	app/deviceapps/kids/mobile/ios/dashboard/[username]/page.tsx
	•	app/gear/dbfull/[id]/page.tsx
	•	app/learning/curricula/page.tsx
	•	app/money/create-transaction-form.tsx
	•	app/new-profile/page.tsx
	•	app/parent/child-progress.tsx
	•	app/parent/events/page.tsx
	•	app/parent/new-page.tsx
	•	app/parent/page.tsx
	•	app/profiles/page.tsx
	•	app/setlists/page.tsx ("Setlist" not exported)
	•	app/settings/page.tsx
	•	app/subscription/page.tsx
	•	app/transactions/edit/[id]/page.tsx
	•	app/transactions/new/page.tsx
	•	app/transactions/page-new.tsx
	•	app/transactions/page.tsx
	•	components/account/qr-scanner.tsx (re: currentUser)
	•	components/admin/... (e.g. forceRefresh)
	•	components/affiliations/affiliation-manager.tsx (re: currentUser, email on an error array)
	•	components/auth/auth-initializer.tsx (cookies issue too)
	•	components/auth/MusicLoadingMessage.tsx (union-type property mismatch)
	•	components/calendar/event-detail.tsx
	•	components/calendar/event-form-tabs/music-tab.tsx (re: currentUser)
	•	components/calendar/event-form-tabs/people-tab.tsx (select query-error object missing fields)
	•	components/calendar/event-form-tabs/summary-tab.tsx
	•	components/calendar/event-form.tsx ("currentUser", property mismatches)
	•	components/calendar/event-invitations.tsx ("currentUser", missing helpers, overly deep types, relation mismatch)
	•	components/calendar/event-participants.tsx ("currentUser")
	•	components/calendar/event-tabs/music-tab.tsx ("SetlistSegment" vs number[])
	•	components/children/child-detail.tsx ("child" possibly null)
	•	components/children/child-edit-form.tsx (icon prop mismatch)
	•	components/children/child-profile-card.tsx ("instrument_name" vs instrument)
	•	components/children/children-list.tsx ("username" being string|null vs required string)
	•	components/children/device-details.tsx (query "information_schema.tables" not allowed, missing fields)
	•	components/children/instrument-selector.tsx ('is_popular' not on Instrument)
	•	components/children/practice-dashboard-chart.tsx ('string|null' passed to new Date(...))
	•	components/children/profile-based-child-card.tsx (color-scheme type mismatch)
	•	components/curriculum/... (re: currentUser)
	•	components/dashboard/dynamic-feed.tsx ("Notification.created_at" is string|null)
	•	components/dashboard/hero-action-panel.tsx ('number' vs string, optional string)
	•	components/dashboard/migration-helper.tsx ('duration_minutes', 'user_id', 'song_id' not in that table type)
	•	components/dashboard/subscription-manager.tsx (missing stripe_customer_id, plan_type, current_period_start/end, cancel_at; plus "string" not assignable to literal union)
	•	components/dashboard/upcoming-events.tsx (id: number vs string)
	•	components/debug/auth-debug.tsx (missing fields on User→specific shape, variable used before declaration)
	•	components/debug/EnhancedTableTester.tsx (property mismatches in select, literal mismatches for "unknown"/"array"/"jsonb", relation string not allowed)
	•	(…and likely more in other components not exhaustively listed here.)

⸻

5. "Argument of type 'number' is not assignable to parameter of type 'string'" & object-shape mismatches
	•	app/api/gear/[id]/subitems/route.ts ("number"↔"string" mismatches; "gearItem" possibly null)
	•	app/api/gear/test/route.ts (omitted required properties: is_active, ownership_status; "number"↔"string")
	•	app/api/practice/log-direct/route.ts ("duration_minutes" not in PracticeSession)
	•	app/api/parent/events/page.tsx ("updated_at" missing from payload vs CalendarEvent)
	•	app/calendar/events/[id]/page.tsx ("ParamValue" not number)
	•	app/calendar/events/edit/[id]/page.tsx (ditto)
	•	app/calendar/page.tsx ("updated_at" missing)
	•	app/money/page.tsx ("number"↔"string"; function signature mismatch; missing any type annotation)
	•	app/practice/[id]/page.tsx (multiple: "string"↔SupabaseClient<any>, array shape mismatches vs Song[], SetStateAction<Song[]> mismatch, includes called on setter)
	•	app/practice/page.tsx ("string|null"↔literal ColorScheme|null; "void"↔Profile[])
	•	app/songs/create-song-form.tsx (expected 2 args, got 1)
	•	app/subscription/page.tsx (Subscription|Subscription[]↔Subscription|null; Subscription missing plan_type)
	•	In many components (see list under section 4), passing string|null into places expecting string, arrays of wrong element-type, omitted required fields in object literals (e.g. missing updated_at, created_at), etc.

⸻

6. "Type instantiation is excessively deep and possibly infinite"
	•	components/calendar/event-invitations.tsx (TS2589: very deep generic from Supabase or complex union)

⸻

7. Miscellaneous Prop-Type or Overload Mismatches
	•	app/api/gear/[id]/route.ts, app/api/gear/[id]/subitems/route.ts: "gearItem possibly null" (TS18047)
	•	app/api/deviceapps/test/create-check-table-exists-function/route.ts: missing .execute on PostgrestFilterBuilder (TS2339)
	•	app/api/stripe/checkout-session/route.ts: hard-coded "2022-11-15" not matching expected "2025-05-28.basil" (TS2322)
	•	app/api/stripe/webhook/route.ts: Promise<ReadonlyHeaders> indexing directly; Subscription missing fields (current_period_start/end, user_id, plan_type); Invoice missing subscription (TS2339)
	•	components/account/qr-scanner.tsx: dynamic import mis-typed as () => Promise<typeof Html5QrcodeScanner> vs loader signature (TS2345); also Property 'currentUser' here—see section 4.
	•	components/auth/MusicLoadingMessage.tsx: union-type property mismatches (TS2339)
	•	Many "Property X does not exist on type Y" in components/... (already enumerated under section 4).

⸻

🗺 Project Plan: Work Through Errors in Phases

Below is a suggested sequence of phases. In each phase, address all files listed, then re-run npx tsc --noEmit (with skipLibCheck and .next excluded) to collapse that batch of errors before moving on.

⸻

Phase 0: Ensure TS isn't checking unwanted files
	1.	In tsconfig.json, add:

{
  "exclude": ["node_modules", ".next", "type-errors.txt"]
}


	2.	Confirm "skipLibCheck": true is in "compilerOptions".
	3.	Run:

npx tsc --noEmit

You should now see only errors in your own app/ and components/ folders.

⸻

Phase 1: Fix all req.cookies Promise vs sync errors (TS2739)

All the files under "Phase 1" assume await req.cookies. You can safely remove any await  in front of req.cookies, or change function signatures to treat req.cookies as a synchronous object.
	1.	Open each file listed under section 1 above.
	2.	Anywhere you see const cookies = await req.cookies, change to const cookies = req.cookies.
	3.	If a function signature still expects Promise<ReadonlyRequestCookies>, update it to accept ReadonlyRequestCookies (or wrap it in async function handler(req: NextRequest, ...) { … } but don't await req.cookies).
	4.	Save all changes and re-run npx tsc --noEmit. All TS2739 errors should disappear.

⸻

Phase 2: Annotate or narrow every catch (error) (TS18046)

- [x] app/actions/stripe-enterprise-actions.ts
- [x] lib/services/subscription-service.ts
- [x] lib/services/beta-signup-service.ts
- [x] lib/services/billing-notifications.ts
- [x] lib/services/song-service.ts
- [x] lib/services/receiptProcessor.ts
- [x] lib/notifications/triggers/social-interactions.ts
- [x] lib/notifications/triggers/billing-reminders.ts
- [x] lib/notifications/triggers/upcoming-lessons.ts
- [x] components/ui/google-maps-test.tsx
- [x] components/setlists/setlist-manager.tsx
- [x] app/api/cron/notifications/route.ts
- [x] components/dashboard/migration-helper.tsx
- [x] lib/queries/deviceQueries.ts
- [x] lib/queries/practiceDashboardQueries.ts
- [x] lib/services/spotify.ts
- [x] lib/queries/profileQueries.ts
- [x] lib/queries/practiceAssignmentQueries.ts
- [x] lib/queries/userData.ts
- [x] lib/queries/childPracticeQueries.ts
- [x] lib/api/accountLinking.ts
- [x] lib/utils/permissionFixer.ts
- [x] lib/utils/network-diagnostics.ts
- [x] lib/utils/authSessionRefresher.ts
- [x] lib/logs/logger.ts
- [x] lib/utils/authReset.ts
- [x] lib/logs/api-logger.ts
- [x] lib/providers/genius-provider.ts
- [x] lib/auth-helpers.ts
- [x] lib/middleware/admin-guard.ts
- [x] lib/middleware/api-auth-middleware.ts
- [x] lib/middleware/gear-middleware.ts

**Status:** Phase 2 COMPLETE — All main app and lib files have been updated for catch (error: any). Next: Phase 3.

⸻

Phase 3: Replace req.ip usage (TS2339)
	1.	In app/api/deviceapps/children/login/route.ts, find where req.ip is used.
	2.	Replace it with something like:

const forwarded = req.headers.get("x-forwarded-for");
const ip = forwarded?.split(",")[0] || req.ip; // if you've polyfilled `req.ip`

or, if you rely on Vercel/Cloudflare:

const ip = req.headers.get("cf-connecting-ip") ?? undefined;


	3.	Save and re-run npx tsc --noEmit. The TS2339 there should vanish.

⸻

Phase 4: Stub or import missing names & fix "Cannot find name '…'"

This is the largest batch. For each missing-name error, either create a proper import or add a quick stub in a single global file. Then, in the next phase, refine those stubs.

4.1 Create a "global stubs" file
	1.	Create a new file at app/api/_globals.d.ts with content such as:

// app/api/_globals.d.ts
declare function verifyAccessCode(...args: any[]): Promise<boolean>;
declare const authUser: any;
// Add other missing names you see frequently:
declare function getStatusBadge(...args: any[]): any;
// …etc…


	2.	In tsconfig.json, ensure TypeScript picks it up, e.g.:

{
  "compilerOptions": {
    "typeRoots": ["./app/api", "./node_modules/@types"]
  }
}

This silences "Cannot find name '…'" for those you stubbed.

4.2 Fix missing exports
	•	In app/lib/queries/setlistQueries.ts, add:

export type Setlist = /* …whatever that interface is… */;
// or export the interface/class directly if it already exists.



4.3 Re-run npx tsc --noEmit.
You should see that many "Cannot find name" errors disappear. Leave any remaining missing-names in your editor to fix one by one if they're only used in a handful of places.

⸻

Phase 5: Batch-fix common object-shape and type mismatches

Tackle every "Argument of type 'number' is not assignable to parameter of type 'string'" or "object literal may only specify known properties." Most of these cluster around your database models or Supabase queries. Fix the underlying types or convert the values.
	1.	app/api/gear/[id]/subitems/route.ts
	•	Find each line where you pass a number into something expecting a string. Convert with String(someNumber) or adjust the function signature.
	•	For "object literal may only specify known properties" (e.g. missing is_active, ownership_status in GearFormData), update that type in lib/queries/gearQueries.ts or adjust the literal to include all required fields.
	2.	app/api/gear/test/route.ts
	•	Add is_active and ownership_status fields to the literal passed into your insert/update call, or update the GearFormData interface to mark them optional if that's correct.
	3.	app/api/practice/log-direct/route.ts
	•	Remove or rename duration_minutes so it matches your PracticeSession type (e.g. use duration instead).
	4.	app/api/parent/events/page.tsx, app/api/calendar/**/*.tsx and all Calendar-related pages (app/calendar/events/[id]/page.tsx, etc.)
	•	Your Supabase returns a raw object lacking updated_at. Either update your CalendarEvent interface (e.g. make updated_at?: string | null) or cast the payload to unknown then to CalendarEvent once you've filled in a default updated_at.
	5.	app/money/page.tsx and similar
	•	Where a setter expects a certain type, explicitly annotate or cast. E.g. if useState<string>() but you call setSomething(123), switch to useState<number|string>(), or convert 123→"123".
	6.	app/practice/[id]/page.tsx
	•	Wherever you call a function expecting a SupabaseClient but pass a string, correct the call signature or wrap a new Supabase client instance instead of passing the wrong param.
	•	For Song[] mismatches, align your local Song type with the one from lib/queries/songQueries.ts. If fields differ, add missing fields (e.g. source_type, notes, tags, is_public) to the type you use in that component.
	7.	app/practice/page.tsx
	•	Change useState<ColorScheme|null>() to accept a string|null or refine the code to cast a string|null into ColorScheme|null (for example, as ColorScheme).
	•	Where you pass void into setProfiles, ensure you either pass an array or change the setter's type to void | Profile[].
	8.	app/subscription/page.tsx
	•	If Subscription and SubscriptionWithComputed differ (missing plan_type), update your Subscription interface to include plan_type (or cast).
	•	Where you pass Subscription[] into a setter expecting Subscription|null, adjust your state to Subscription|Subscription[]|null.
	9.	Components in components/...
	•	Wherever you see a literal missing updated_at/created_at for a type (e.g. CalendarEvent, Notification, RecentItem, Event, etc.), update that interface to make those fields optional (i.e. updated_at?: string | null).
	•	Convert any string|null → string or update the consuming prop type to accept string|null.
	•	If an array literal's elements lack required properties, add them or adjust the component's type.
	•	For colour-scheme mismatches, either convert string inputs to valid ColorScheme or change the component's prop to accept string.
	•	Wherever you see includes() on a setter return type, ensure you're calling .includes on the array itself, not on the setter. E.g.:

// ❌ wrong:
someSetter.includes(x)
// ✅ right:
const [arr, setArr] = useState<(string|number)[]>([]);
arr.includes(x);
setArr([...]);


	10.	components/dashboard/migration-helper.tsx
	•	Remove or rename duration_minutes, song_id, and ensure the object literal keys match your table type.
	•	Wherever you do .from("information_schema.tables"), use a genuine table in your Postgrest query (or wrap it in an explicit any cast if you must query a system view).
	11.	components/calendar/event-invitations.tsx
	•	Some relations "event_invitations" aren't enumerated in your Supabase client's Tables union. Either extend the auto-generated type so "event_invitations" is allowed, or wrap that call in as any or as PostgrestQueryBuilder<...> to bypass strict typing for now.
	•	For "Type instantiation is excessively deep," simplify one subexpression by casting to unknown or extracting into a narrower type alias.
	12.	components/account/qr-scanner.tsx
	•	Change the dynamic import to match the loader signature: e.g. use:

const Html5QrcodeScannerLoader: LoaderComponent<string> = () => import("html5-qrcode").then(mod => mod.Html5QrcodeScanner);
<Dynamic loader={Html5QrcodeScannerLoader} … />


	•	Or install a proper type mapping for html5-qrcode.

	13.	components/auth/MusicLoadingMessage.tsx
	•	Narrow the union before accessing .lyric vs .text. For example:

if ("lyric" in props) { /* safe to use props.lyric */ }
else { /* safe to use props.text */ }


	14.	components/debug/auth-debug.tsx & components/debug/AuthDebugger.tsx
	•	Add missing fields (avatar_url, is_admin, etc.) to your local User type, or cast the passed object into the narrower shape.
	•	For "Block-scoped variable used before declaration," ensure you define let userData before referencing it, or refactor that function.
	15.	Other "Property X does not exist on type Y" cases
	•	For every "Property 'X' does not exist on type '…'" in your components, open the type definition (e.g. Supabase's GeneratedTypes) and either add that field (if valid), or rename your code to use a real field name (e.g. instrument vs instrument_name).
	•	Use quick // @ts-ignore for lower-priority or truly legacy code you don't plan to ship in six days; annotate each with // @ts-expect-error so you know exactly where to revisit later.
	16.	Final sweep
	•	Once you've resolved each category above, re-run npx tsc --noEmit.
	•	Fix the remaining "overload mismatch" or "literal union" errors by adjusting your props or adding the missing literal to the union type (e.g. allow "warning" in your button variant type).

⸻

Phase 6: Clean up any residual "deep instantiation" or "excessively deep" errors
	•	In components/calendar/event-invitations.tsx, find the line flagged as TS2589. Extract the complex generic into a narrower alias. For example:

type InviteRecords = SelectQueryError<"…"> | AnotherType;  
// Then use InviteRecords[] instead of writing out the full union.


	•	If necessary, wrap that single line in as unknown as <DesiredType>.

⸻

Phase 7: Final sanity check and build
	1.	Re-run:

npx tsc --noEmit
npm run build


	2.	Confirm there are zero TypeScript errors, and that the Next.js build completes cleanly.




⸻

📌 Tips to Stay Focused
	1.	Batch by category, not by file. E.g. fix all "cookies" issues in one go rather than hopping file to file.
	2.	Use search-and-replace patterns where safe:
	•	await req\.cookies → req.cookies
	•	catch\s*\(\s*error\s*\) → catch (error: any)
	3.	Temporarily stub complicated generics so you can release on time, then plan a follow-up sprint to re-introduce full type safety.
	4.	Annotate every // @ts-ignore with a comment like // @ts-ignore TODO: reconcile with real type by YYYY‐MM‐DD so you can track these later.

