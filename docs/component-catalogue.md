# Component Catalogue

## Summary

- **Total Components**: 518
- **Test Coverage**: 0%
- **Storybook Coverage**: 0%
- **Type Coverage**: 72%

## Component Breakdown

### By Type

- **component**: 336
- **ui**: 130
- **context**: 22
- **shared**: 17
- **layout**: 9
- **provider**: 4

### By Directory

- **components**: 293
- **backups**: 209
- **deviceapps-standalone**: 16

## Component List

| Name | Type | Path | Tests | Stories | Types |
|------|------|------|-------|---------|-------|
| . | component | components/calendar/event-tabs/index.ts | ❌ | ❌ | ❌ |
| . | component | components/calendar/event-form-tabs/index.ts | ❌ | ❌ | ❌ |
| . | component | backups/components/calendar/event-form-tabs/index.ts | ❌ | ❌ | ❌ |
| . | component | backups/components/calendar/event-tabs/index.ts | ❌ | ❌ | ❌ |
| accordion | ui | components/ui/accordion.tsx | ❌ | ❌ | ❌ |
| accordion | ui | backups/components/ui/accordion.tsx | ❌ | ❌ | ❌ |
| ActivityCharts | component | components/admin/dashboard/activity-charts.tsx | ❌ | ❌ | ✅ |
| ActivityMetrics | component | components/admin/dashboard/activity-metrics.tsx | ❌ | ❌ | ✅ |
| AdminDebugger | component | components/admin/AdminDebugger.tsx | ❌ | ❌ | ❌ |
| AdminPanel | component | components/admin/admin-panel.tsx | ❌ | ❌ | ❌ |
| AdminRolePanel | component | components/admin/admin-role-panel.tsx | ❌ | ❌ | ❌ |
| AdminStatusBar | layout | components/layout/admin-status-bar.tsx | ❌ | ❌ | ❌ |
| Advertisement | component | components/ads/advertisement.tsx | ❌ | ❌ | ✅ |
| Advertisement | component | backups/components/ads/advertisement.tsx | ❌ | ❌ | ✅ |
| AffiliationManager | component | components/affiliations/affiliation-manager.tsx | ❌ | ❌ | ✅ |
| AffiliationManager | component | backups/components/affiliations/affiliation-manager.tsx | ❌ | ❌ | ✅ |
| alert | ui | components/ui/alert.tsx | ❌ | ❌ | ✅ |
| alert | ui | backups/components/ui/alert.tsx | ❌ | ❌ | ✅ |
| alert-dialog | ui | components/ui/alert-dialog.tsx | ❌ | ❌ | ❌ |
| alert-dialog | ui | backups/components/ui/alert-dialog.tsx | ❌ | ❌ | ❌ |
| AnalyticsPanels | component | components/admin/dashboard/analytics-panels.tsx | ❌ | ❌ | ✅ |
| AppearanceStep | component | components/children/add-steps/appearance-step.tsx | ❌ | ❌ | ✅ |
| AppLayout | layout | components/layout/app-layout.tsx | ❌ | ❌ | ✅ |
| AppLayout | layout | backups/components/layout/app-layout.tsx | ❌ | ❌ | ✅ |
| aspect-ratio | ui | components/ui/aspect-ratio.tsx | ❌ | ❌ | ❌ |
| aspect-ratio | ui | backups/components/ui/aspect-ratio.tsx | ❌ | ❌ | ❌ |
| auth-initializer | component | components/auth/auth-initializer.tsx | ❌ | ❌ | ❌ |
| AuthCheck | component | backups/components/auth/auth-check.tsx | ❌ | ❌ | ✅ |
| AuthDebug | component | components/debug/auth-debug.tsx | ❌ | ❌ | ❌ |
| AuthDebugger | component | components/debug/AuthDebugger.tsx | ❌ | ❌ | ❌ |
| AuthGuard | context | components/auth/AuthGuard.tsx | ❌ | ❌ | ❌ |
| AuthInitializer | component | components/auth/AuthInitializer.tsx | ❌ | ❌ | ❌ |
| AuthReset | component | components/debug/AuthReset.tsx | ❌ | ❌ | ❌ |
| AuthStateHydrator | component | components/auth/auth-state-hydrator.tsx | ❌ | ❌ | ✅ |
| AuthWrapper | context | components/auth/auth-wrapper.tsx | ❌ | ❌ | ❌ |
| AvailabilityBooking | component | components/public-profile/availability-booking.tsx | ❌ | ❌ | ✅ |
| avatar | ui | components/ui/avatar.tsx | ❌ | ❌ | ❌ |
| avatar | ui | deviceapps-standalone/components/ui/avatar.tsx | ❌ | ❌ | ❌ |
| avatar | ui | backups/components/ui/avatar.tsx | ❌ | ❌ | ❌ |
| AvatarSelector | component | components/children/avatar-selector.tsx | ❌ | ❌ | ✅ |
| AvatarSelector | component | backups/components/children/avatar-selector.tsx | ❌ | ❌ | ✅ |
| badge | ui | components/ui/badge.tsx | ❌ | ❌ | ✅ |
| badge | ui | backups/components/ui/badge.tsx | ❌ | ❌ | ✅ |
| BasicInformationStep | component | components/children/add-steps/basic-information-step.tsx | ❌ | ❌ | ✅ |
| BatchActionsModal | component | components/admin/dashboard/batch-actions-modal.tsx | ❌ | ❌ | ✅ |
| BetaBadge | ui | components/ui/beta-badge.tsx | ❌ | ❌ | ✅ |
| BookRow | component | components/learning/book-row.tsx | ❌ | ❌ | ✅ |
| BookRow | component | backups/components/learning/book-row.tsx | ❌ | ❌ | ✅ |
| BottomNav | component | deviceapps-standalone/components/kids/bottom-nav.tsx | ❌ | ❌ | ✅ |
| BrandAutocomplete | component | components/gear/brand-autocomplete.tsx | ❌ | ❌ | ✅ |
| BrandAutocomplete | component | backups/components/gear/brand-autocomplete.tsx | ❌ | ❌ | ✅ |
| breadcrumb | ui | components/ui/breadcrumb.tsx | ❌ | ❌ | ❌ |
| breadcrumb | ui | backups/components/ui/breadcrumb.tsx | ❌ | ❌ | ❌ |
| button | ui | components/ui/button.tsx | ❌ | ❌ | ✅ |
| button | ui | deviceapps-standalone/components/ui/button.tsx | ❌ | ❌ | ✅ |
| button | ui | backups/components/ui/button.tsx | ❌ | ❌ | ✅ |
| calendar | ui | components/ui/calendar.tsx | ❌ | ❌ | ✅ |
| calendar | ui | backups/components/ui/calendar.tsx | ❌ | ❌ | ✅ |
| CalendarCard | component | components/children/calendar-card.tsx | ❌ | ❌ | ✅ |
| CalendarCard | component | backups/components/children/calendar-card.tsx | ❌ | ❌ | ✅ |
| card | ui | components/ui/card.tsx | ❌ | ❌ | ❌ |
| card | ui | deviceapps-standalone/components/ui/card.tsx | ❌ | ❌ | ❌ |
| card | ui | backups/components/ui/card.tsx | ❌ | ❌ | ❌ |
| carousel | ui | components/ui/carousel.tsx | ❌ | ❌ | ✅ |
| carousel | ui | backups/components/ui/carousel.tsx | ❌ | ❌ | ✅ |
| CategorySelector | component | components/gear/category-selector.tsx | ❌ | ❌ | ✅ |
| CategorySelector | component | backups/components/gear/category-selector.tsx | ❌ | ❌ | ✅ |
| ChannelRow | component | components/learning/channel-row.tsx | ❌ | ❌ | ✅ |
| ChannelRow | component | backups/components/learning/channel-row.tsx | ❌ | ❌ | ✅ |
| chart | ui | components/ui/chart.tsx | ❌ | ❌ | ✅ |
| chart | ui | backups/components/ui/chart.tsx | ❌ | ❌ | ✅ |
| checkbox | ui | components/ui/checkbox.tsx | ❌ | ❌ | ❌ |
| checkbox | ui | backups/components/ui/checkbox.tsx | ❌ | ❌ | ❌ |
| ChildActivityDashboard | component | components/parent/child-activity-dashboard.tsx | ❌ | ❌ | ✅ |
| ChildActivityDashboard | component | backups/components/parent/child-activity-dashboard.tsx | ❌ | ❌ | ✅ |
| ChildCard | component | components/children/child-card.tsx | ❌ | ❌ | ✅ |
| ChildCard | component | backups/components/children/child-card.tsx | ❌ | ❌ | ✅ |
| ChildDetail | component | components/children/child-detail.tsx | ❌ | ❌ | ✅ |
| ChildDetail | component | backups/components/children/child-detail.tsx | ❌ | ❌ | ✅ |
| ChildDetailView | component | components/children/child-detail-view.tsx | ❌ | ❌ | ✅ |
| ChildDetailView | component | backups/components/children/child-detail-view.tsx | ❌ | ❌ | ✅ |
| ChildEditForm | component | components/children/child-edit-form.tsx | ❌ | ❌ | ✅ |
| ChildEditForm | component | backups/components/children/child-edit-form.tsx | ❌ | ❌ | ✅ |
| ChildForm | component | components/children/child-form.tsx | ❌ | ❌ | ✅ |
| ChildForm | component | backups/components/children/child-form.tsx | ❌ | ❌ | ✅ |
| ChildInstrumentCompetencies | component | components/settings/child-instrument-competencies.tsx | ❌ | ❌ | ✅ |
| ChildInstrumentCompetencies | component | backups/components/settings/child-instrument-competencies.tsx | ❌ | ❌ | ✅ |
| ChildInstrumentList | component | components/settings/child-instrument-list.tsx | ❌ | ❌ | ✅ |
| ChildInstrumentList | component | backups/components/settings/child-instrument-list.tsx | ❌ | ❌ | ✅ |
| ChildInstrumentSelector | component | components/settings/child-instrument-selector.tsx | ❌ | ❌ | ✅ |
| ChildInstrumentSelector | component | components/children/instrument-selector.tsx | ❌ | ❌ | ✅ |
| ChildInstrumentSelector | component | backups/components/settings/child-instrument-selector.tsx | ❌ | ❌ | ✅ |
| ChildInstrumentSelector | component | backups/components/children/instrument-selector.tsx | ❌ | ❌ | ✅ |
| ChildPracticeDashboard | component | components/settings/child-practice-dashboard.tsx | ❌ | ❌ | ✅ |
| ChildPracticeGoals | component | components/settings/child-practice-goals.tsx | ❌ | ❌ | ✅ |
| ChildProfileCard | component | components/children/child-profile-card.tsx | ❌ | ❌ | ✅ |
| ChildProfileCard | component | backups/components/children/child-profile-card.tsx | ❌ | ❌ | ✅ |
| ChildProfileSettings | component | components/settings/child-profile-settings.tsx | ❌ | ❌ | ✅ |
| ChildProfileSettings | component | backups/components/settings/child-profile-settings.tsx | ❌ | ❌ | ✅ |
| ChildrenList | component | components/children/children-list.tsx | ❌ | ❌ | ❌ |
| ChildrenList | component | backups/components/children/children-list.tsx | ❌ | ❌ | ❌ |
| ChildRoleSettings | component | components/settings/child-role-settings.tsx | ❌ | ❌ | ✅ |
| ChildRoleSettings | component | backups/components/settings/child-role-settings.tsx | ❌ | ❌ | ✅ |
| ChildSelection | component | components/deviceapps/child-selection.tsx | ❌ | ❌ | ✅ |
| ChildSelection | component | backups/components/deviceapps/child-selection.tsx | ❌ | ❌ | ✅ |
| collapsible | ui | components/ui/collapsible.tsx | ❌ | ❌ | ❌ |
| collapsible | ui | backups/components/ui/collapsible.tsx | ❌ | ❌ | ❌ |
| ColorSchemeSelector | component | components/children/color-scheme-selector.tsx | ❌ | ❌ | ✅ |
| ColorSchemeSelector | component | backups/components/children/color-scheme-selector.tsx | ❌ | ❌ | ✅ |
| command | ui | components/ui/command.tsx | ❌ | ❌ | ✅ |
| command | ui | backups/components/ui/command.tsx | ❌ | ❌ | ✅ |
| CompetencySelectionDialog | component | components/settings/competency-selection-dialog.tsx | ❌ | ❌ | ✅ |
| CompetencySelectionDialog | component | backups/components/settings/competency-selection-dialog.tsx | ❌ | ❌ | ✅ |
| ContentRow | component | components/learning/content-row.tsx | ❌ | ❌ | ✅ |
| ContentRow | component | backups/components/learning/content-row.tsx | ❌ | ❌ | ✅ |
| context-menu | context | components/ui/context-menu.tsx | ❌ | ❌ | ❌ |
| context-menu | context | backups/components/ui/context-menu.tsx | ❌ | ❌ | ❌ |
| CreateCurriculumForm | component | components/curriculum/create-curriculum-form.tsx | ❌ | ❌ | ✅ |
| CreateCurriculumForm | component | backups/components/curriculum/create-curriculum-form.tsx | ❌ | ❌ | ✅ |
| CreateLessonPlanForm | component | components/curriculum/create-lesson-plan-form.tsx | ❌ | ❌ | ✅ |
| CreateLessonPlanForm | component | backups/components/curriculum/create-lesson-plan-form.tsx | ❌ | ❌ | ✅ |
| CreateSongForm | component | components/songs/create-song-form.tsx | ❌ | ❌ | ✅ |
| CreateSongForm | component | backups/components/songs/create-song-form.tsx | ❌ | ❌ | ✅ |
| CurriculumAssignment | component | components/children/curriculum-assignment.tsx | ❌ | ❌ | ✅ |
| CurriculumAssignment | component | backups/components/children/curriculum-assignment.tsx | ❌ | ❌ | ✅ |
| CurriculumCard | shared | components/deviceapps/shared/curriculum-card.tsx | ❌ | ❌ | ✅ |
| CurriculumCard | shared | backups/components/deviceapps/shared/curriculum-card.tsx | ❌ | ❌ | ✅ |
| DailyTasks | component | deviceapps-standalone/components/kids/daily-tasks.tsx | ❌ | ❌ | ✅ |
| DashboardHeader | shared | components/deviceapps/shared/dashboard-header.tsx | ❌ | ❌ | ✅ |
| DashboardHeader | shared | backups/components/deviceapps/shared/dashboard-header.tsx | ❌ | ❌ | ✅ |
| DataTable | ui | components/ui/data-table.tsx | ❌ | ❌ | ✅ |
| DataTable | ui | backups/components/ui/data-table.tsx | ❌ | ❌ | ✅ |
| DatePicker | ui | components/ui/date-picker.tsx | ❌ | ❌ | ✅ |
| DatePicker | ui | backups/components/ui/date-picker.tsx | ❌ | ❌ | ✅ |
| DebugInfo | component | deviceapps-standalone/components/kids/debug-info.tsx | ❌ | ❌ | ✅ |
| DeviceDetails | component | components/children/device-details.tsx | ❌ | ❌ | ✅ |
| DeviceDetails | component | backups/components/children/device-details.tsx | ❌ | ❌ | ✅ |
| dialog | ui | components/ui/dialog.tsx | ❌ | ❌ | ❌ |
| dialog | ui | backups/components/ui/dialog.tsx | ❌ | ❌ | ❌ |
| Draggable | ui | components/ui/drag-and-drop.tsx | ❌ | ❌ | ✅ |
| drawer | ui | components/ui/drawer.tsx | ❌ | ❌ | ❌ |
| drawer | ui | backups/components/ui/drawer.tsx | ❌ | ❌ | ❌ |
| dropdown-menu | ui | components/ui/dropdown-menu.tsx | ❌ | ❌ | ❌ |
| dropdown-menu | ui | deviceapps-standalone/components/ui/dropdown-menu.tsx | ❌ | ❌ | ❌ |
| dropdown-menu | ui | backups/components/ui/dropdown-menu.tsx | ❌ | ❌ | ❌ |
| DynamicFeed | component | components/dashboard/dynamic-feed.tsx | ❌ | ❌ | ✅ |
| EnhancedDatePicker | ui | components/ui/enhanced-date-picker.tsx | ❌ | ❌ | ✅ |
| EnhancedDatePicker | ui | backups/components/ui/enhanced-date-picker.tsx | ❌ | ❌ | ✅ |
| EnhancedErrorDisplay | component | components/auth/enhanced-error-display.tsx | ❌ | ❌ | ✅ |
| EnhancedEventDetail | component | components/calendar/enhanced-event-detail.tsx | ❌ | ❌ | ✅ |
| EnhancedEventDetail | component | backups/components/calendar/enhanced-event-detail.tsx | ❌ | ❌ | ✅ |
| EnhancedGearSelector | component | components/finances/enhanced-gear-selector.tsx | ❌ | ❌ | ✅ |
| EnhancedGearSelector | component | backups/components/finances/enhanced-gear-selector.tsx | ❌ | ❌ | ✅ |
| EnhancedTableTester | component | components/debug/EnhancedTableTester.tsx | ❌ | ❌ | ❌ |
| EnhancedTransactionForm | component | components/finances/enhanced-transaction-form.tsx | ❌ | ❌ | ✅ |
| EnhancedTransactionForm | component | backups/components/finances/enhanced-transaction-form.tsx | ❌ | ❌ | ✅ |
| EnterprisePlanSelector | component | components/admin/dashboard/enterprise-plan-selector.tsx | ❌ | ❌ | ❌ |
| EnterpriseSubscriptionBuilder | component | components/admin/dashboard/enterprise-subscription-builder.tsx | ❌ | ❌ | ✅ |
| EVENT | component | components/calendar/event-constants.ts | ❌ | ❌ | ❌ |
| EVENT | component | backups/components/calendar/event-constants.ts | ❌ | ❌ | ❌ |
| EventCard | component | components/receipts/cards/event-card.tsx | ❌ | ❌ | ✅ |
| EventCard | shared | components/deviceapps/shared/event-card.tsx | ❌ | ❌ | ✅ |
| EventCard | component | backups/components/receipts/cards/event-card.tsx | ❌ | ❌ | ✅ |
| EventCard | shared | backups/components/deviceapps/shared/event-card.tsx | ❌ | ❌ | ✅ |
| EventDetail | component | components/calendar/event-detail.tsx | ❌ | ❌ | ✅ |
| EventDetail | component | backups/components/calendar/event-detail.tsx | ❌ | ❌ | ✅ |
| EventForm | component | components/calendar/event-form.tsx | ❌ | ❌ | ✅ |
| EventForm | component | backups/components/calendar/event-form.tsx | ❌ | ❌ | ✅ |
| EventFormGearTab | component | components/calendar/event-form-tabs/gear-tab.tsx | ❌ | ❌ | ✅ |
| EventFormGearTab | component | backups/components/calendar/event-form-tabs/gear-tab.tsx | ❌ | ❌ | ✅ |
| EventFormMoneyTab | component | components/calendar/event-form-tabs/money-tab.tsx | ❌ | ❌ | ✅ |
| EventFormMoneyTab | component | backups/components/calendar/event-form-tabs/money-tab.tsx | ❌ | ❌ | ✅ |
| EventFormMusicTab | component | components/calendar/event-form-tabs/music-tab.tsx | ❌ | ❌ | ✅ |
| EventFormMusicTab | component | backups/components/calendar/event-form-tabs/music-tab.tsx | ❌ | ❌ | ✅ |
| EventFormPeopleTab | component | components/calendar/event-form-tabs/people-tab.tsx | ❌ | ❌ | ✅ |
| EventFormPeopleTab | component | backups/components/calendar/event-form-tabs/people-tab.tsx | ❌ | ❌ | ✅ |
| EventFormSummaryTab | component | components/calendar/event-form-tabs/summary-tab.tsx | ❌ | ❌ | ✅ |
| EventFormSummaryTab | component | backups/components/calendar/event-form-tabs/summary-tab.tsx | ❌ | ❌ | ✅ |
| EventFormTravelTab | component | components/calendar/event-form-tabs/travel-tab.tsx | ❌ | ❌ | ✅ |
| EventFormTravelTab | component | backups/components/calendar/event-form-tabs/travel-tab.tsx | ❌ | ❌ | ✅ |
| EventFormWrapUpTab | component | components/calendar/event-form-tabs/wrap-up-tab.tsx | ❌ | ❌ | ✅ |
| EventFormWrapUpTab | component | backups/components/calendar/event-form-tabs/wrap-up-tab.tsx | ❌ | ❌ | ✅ |
| EventInvitations | component | components/calendar/event-invitations.tsx | ❌ | ❌ | ✅ |
| EventInvitations | component | backups/components/calendar/event-invitations.tsx | ❌ | ❌ | ✅ |
| EventParticipants | component | components/calendar/event-participants.tsx | ❌ | ❌ | ✅ |
| EventParticipants | component | backups/components/calendar/event-participants.tsx | ❌ | ❌ | ✅ |
| EventTab | component | components/calendar/event-tabs/event-tab.tsx | ❌ | ❌ | ✅ |
| EventTab | component | backups/components/calendar/event-tabs/event-tab.tsx | ❌ | ❌ | ✅ |
| form | ui | components/ui/form.tsx | ❌ | ❌ | ❌ |
| form | ui | backups/components/ui/form.tsx | ❌ | ❌ | ❌ |
| formatted-otp-input | ui | components/ui/formatted-otp-input.tsx | ❌ | ❌ | ❌ |
| formatted-otp-input | ui | backups/components/ui/formatted-otp-input.tsx | ❌ | ❌ | ❌ |
| GearCard | component | components/gear/gear-card.tsx | ❌ | ❌ | ✅ |
| GearCard | component | components/receipts/cards/gear-card.tsx | ❌ | ❌ | ✅ |
| GearCard | component | backups/components/gear/gear-card.tsx | ❌ | ❌ | ✅ |
| GearCard | component | backups/components/receipts/cards/gear-card.tsx | ❌ | ❌ | ✅ |
| GearDetail | component | components/gear/gear-detail.tsx | ❌ | ❌ | ✅ |
| GearDetail | context | components/gear/gear-detail-new.tsx | ❌ | ❌ | ✅ |
| GearDetail | component | components/gear/gear-detail-fixed.tsx | ❌ | ❌ | ✅ |
| GearDetail | context | backups/components/gear/gear-detail.tsx | ❌ | ❌ | ✅ |
| GearDetailContent | context | components/gear/gear-detail-content.tsx | ❌ | ❌ | ✅ |
| GearDetailPageClient | component | components/gear/gear-detail-page-client.tsx | ❌ | ❌ | ✅ |
| GearEditPageClient | component | components/gear/gear-edit-page-client.tsx | ❌ | ❌ | ✅ |
| GearEditPageClient | component | backups/components/gear/gear-edit-page-client.tsx | ❌ | ❌ | ✅ |
| GearForm | component | components/gear/gear-form.tsx | ❌ | ❌ | ✅ |
| GearForm | component | components/gear/gear-form-new.tsx | ❌ | ❌ | ✅ |
| GearForm | component | backups/components/gear/gear-form.tsx | ❌ | ❌ | ✅ |
| GearForm | component | backups/components/gear/gear-form-new.tsx | ❌ | ❌ | ✅ |
| GearGrabber | component | components/receipts/gear-grabber.tsx | ❌ | ❌ | ❌ |
| GearGrabber | component | backups/components/receipts/gear-grabber.tsx | ❌ | ❌ | ❌ |
| GearGrabberNew | component | components/receipts/gear-grabber-new.tsx | ❌ | ❌ | ❌ |
| GearGrabberNew | component | backups/components/receipts/gear-grabber-new.tsx | ❌ | ❌ | ❌ |
| GearImageUploader | component | components/gear/gear-image-uploader.tsx | ❌ | ❌ | ✅ |
| GearImageUploader | component | backups/components/gear/gear-image-uploader.tsx | ❌ | ❌ | ✅ |
| GearItemCard | component | components/gear/gear-item-card.tsx | ❌ | ❌ | ✅ |
| GearItemCard | component | backups/components/gear/gear-item-card.tsx | ❌ | ❌ | ✅ |
| GearList | component | components/gear/gear-list.tsx | ❌ | ❌ | ❌ |
| GearList | component | backups/components/gear/gear-list.tsx | ❌ | ❌ | ❌ |
| GearSubitemForm | component | components/gear/gear-subitem-form.tsx | ❌ | ❌ | ✅ |
| GearSubitemForm | component | backups/components/gear/gear-subitem-form.tsx | ❌ | ❌ | ✅ |
| GearTab | component | components/calendar/event-tabs/gear-tab.tsx | ❌ | ❌ | ✅ |
| GearTab | component | backups/components/calendar/event-tabs/gear-tab.tsx | ❌ | ❌ | ✅ |
| GlobalAddButton | component | components/global-add-button.tsx | ❌ | ❌ | ❌ |
| GlobalAddButton | component | backups/components/global-add-button.tsx | ❌ | ❌ | ❌ |
| GoogleMapsSearch | ui | components/ui/google-maps-search.tsx | ❌ | ❌ | ✅ |
| GoogleMapsSearch | ui | backups/components/ui/google-maps-search.tsx | ❌ | ❌ | ✅ |
| GoogleMapsTest | ui | components/ui/google-maps-test.tsx | ❌ | ❌ | ❌ |
| GoogleMapsTest | ui | backups/components/ui/google-maps-test.tsx | ❌ | ❌ | ❌ |
| GoogleMapsWithAutocomplete | ui | components/ui/google-maps-with-autocomplete.tsx | ❌ | ❌ | ✅ |
| GoogleMapsWithAutocomplete | ui | backups/components/ui/google-maps-with-autocomplete.tsx | ❌ | ❌ | ✅ |
| header-with-profiles | layout | components/layout/header-with-profiles.tsx | ❌ | ❌ | ✅ |
| header-with-profiles | layout | backups/components/layout/header-with-profiles.tsx | ❌ | ❌ | ✅ |
| HeroActionPanel | component | components/dashboard/hero-action-panel.tsx | ❌ | ❌ | ✅ |
| hover-card | ui | components/ui/hover-card.tsx | ❌ | ❌ | ❌ |
| hover-card | ui | backups/components/ui/hover-card.tsx | ❌ | ❌ | ❌ |
| ImportUsersModal | component | components/admin/dashboard/import-users-modal.tsx | ❌ | ❌ | ✅ |
| InlineSkillLevelEditor | component | components/settings/inline-skill-level-editor.tsx | ❌ | ❌ | ✅ |
| InlineSkillLevelEditor | component | backups/components/settings/inline-skill-level-editor.tsx | ❌ | ❌ | ✅ |
| input | ui | components/ui/input.tsx | ❌ | ❌ | ❌ |
| input | ui | deviceapps-standalone/components/ui/input.tsx | ❌ | ❌ | ✅ |
| input | ui | backups/components/ui/input.tsx | ❌ | ❌ | ❌ |
| input-otp | ui | components/ui/input-otp.tsx | ❌ | ❌ | ❌ |
| input-otp | ui | backups/components/ui/input-otp.tsx | ❌ | ❌ | ❌ |
| InspirationRow | component | components/learning/inspiration-row.tsx | ❌ | ❌ | ✅ |
| InspirationRow | component | backups/components/learning/inspiration-row.tsx | ❌ | ❌ | ✅ |
| InstrumentCard | component | components/settings/instrument-card.tsx | ❌ | ❌ | ✅ |
| InstrumentCard | component | backups/components/settings/instrument-card.tsx | ❌ | ❌ | ✅ |
| InstrumentCompetencies | component | components/settings/instrument-competencies.tsx | ❌ | ❌ | ❌ |
| InstrumentCompetencies | component | backups/components/settings/instrument-competencies.tsx | ❌ | ❌ | ❌ |
| InstrumentCompetenciesImproved | component | components/settings/instrument-competencies-improved.tsx | ❌ | ❌ | ❌ |
| InstrumentCompetenciesImproved | component | backups/components/settings/instrument-competencies-improved.tsx | ❌ | ❌ | ❌ |
| InstrumentCompetenciesLocal | component | components/settings/instrument-competencies-local.tsx | ❌ | ❌ | ❌ |
| InstrumentCompetenciesLocal | component | backups/components/settings/instrument-competencies-local.tsx | ❌ | ❌ | ❌ |
| InstrumentCompetenciesTable | component | components/settings/instrument-competencies-table.tsx | ❌ | ❌ | ✅ |
| InstrumentCompetenciesV | component | components/settings/instrument-competencies-v2.tsx | ❌ | ❌ | ❌ |
| InstrumentCompetenciesV | component | backups/components/settings/instrument-competencies-v2.tsx | ❌ | ❌ | ❌ |
| InstrumentEditMenu | component | components/settings/instrument-edit-menu.tsx | ❌ | ❌ | ✅ |
| InstrumentSelector | component | components/settings/instrument-selector.tsx | ❌ | ❌ | ✅ |
| InstrumentSelector | component | components/practice/instrument-selector.tsx | ❌ | ❌ | ✅ |
| InstrumentSelector | component | components/practice/instrument-selector-db.tsx | ❌ | ❌ | ✅ |
| InstrumentSelector | component | backups/components/practice/instrument-selector.tsx | ❌ | ❌ | ✅ |
| InstrumentSelector | component | backups/components/practice/instrument-selector-db.tsx | ❌ | ❌ | ✅ |
| InstrumentSelector | component | backups/components/settings/instrument-selector.tsx | ❌ | ❌ | ✅ |
| KidsDashboardLayout | layout | deviceapps-standalone/components/kids/dashboard-layout.tsx | ❌ | ❌ | ✅ |
| KidsPracticeRecorder | component | components/practice/kids-practice-recorder.tsx | ❌ | ❌ | ✅ |
| label | ui | components/ui/label.tsx | ❌ | ❌ | ✅ |
| label | ui | deviceapps-standalone/components/ui/label.tsx | ❌ | ❌ | ✅ |
| label | ui | backups/components/ui/label.tsx | ❌ | ❌ | ✅ |
| LearningShortcuts | component | components/dashboard/learning-shortcuts.tsx | ❌ | ❌ | ✅ |
| LessonPlanEditor | component | components/curriculum/lesson-plan-editor.tsx | ❌ | ❌ | ✅ |
| LessonPlanEditor | component | backups/components/curriculum/lesson-plan-editor.tsx | ❌ | ❌ | ✅ |
| LoadingScreen | ui | components/ui/loading-screen.tsx | ❌ | ❌ | ❌ |
| LoadingSpinner | ui | components/ui/loading-spinner.tsx | ❌ | ❌ | ✅ |
| LoadingSpinner | ui | backups/components/ui/loading-spinner.tsx | ❌ | ❌ | ✅ |
| LoginApprovalDialog | component | components/children/login-approval-dialog.tsx | ❌ | ❌ | ✅ |
| LoginApprovalDialog | component | backups/components/children/login-approval-dialog.tsx | ❌ | ❌ | ✅ |
| LoginCodeDialog | component | components/children/login-code-dialog.tsx | ❌ | ❌ | ✅ |
| LoginCodeDialog | component | backups/components/children/login-code-dialog.tsx | ❌ | ❌ | ✅ |
| LoginForm | shared | components/deviceapps/shared/login-form.tsx | ❌ | ❌ | ✅ |
| LoginForm | shared | deviceapps-standalone/components/deviceapps/shared/login-form.tsx | ❌ | ❌ | ✅ |
| LoginForm | shared | backups/components/deviceapps/shared/login-form.tsx | ❌ | ❌ | ✅ |
| ManualSongForm | component | components/songs/manual-song-form.tsx | ❌ | ❌ | ✅ |
| MediaShowcase | component | components/public-profile/media-showcase.tsx | ❌ | ❌ | ✅ |
| menubar | ui | components/ui/menubar.tsx | ❌ | ❌ | ❌ |
| menubar | ui | backups/components/ui/menubar.tsx | ❌ | ❌ | ❌ |
| MigrationHelper | component | components/dashboard/migration-helper.tsx | ❌ | ❌ | ❌ |
| MoneyCard | component | components/receipts/cards/money-card.tsx | ❌ | ❌ | ✅ |
| MoneyCard | component | backups/components/receipts/cards/money-card.tsx | ❌ | ❌ | ✅ |
| MoneyDashboard | component | components/finances/money-dashboard.tsx | ❌ | ❌ | ❌ |
| MoneyDashboard | component | backups/components/finances/money-dashboard.tsx | ❌ | ❌ | ❌ |
| MoneyTab | component | components/calendar/event-tabs/money-tab.tsx | ❌ | ❌ | ✅ |
| MoneyTab | component | backups/components/calendar/event-tabs/money-tab.tsx | ❌ | ❌ | ✅ |
| MoneyTransactionList | component | components/finances/money-transaction-list.tsx | ❌ | ❌ | ✅ |
| MoneyTransactionList | component | backups/components/finances/money-transaction-list.tsx | ❌ | ❌ | ✅ |
| MusicLoadingMessage | component | components/auth/MusicLoadingMessage.tsx | ❌ | ❌ | ❌ |
| MusicTab | component | components/calendar/event-tabs/music-tab.tsx | ❌ | ❌ | ✅ |
| MusicTab | component | backups/components/calendar/event-tabs/music-tab.tsx | ❌ | ❌ | ✅ |
| navigation-menu | ui | components/ui/navigation-menu.tsx | ❌ | ❌ | ❌ |
| navigation-menu | ui | backups/components/ui/navigation-menu.tsx | ❌ | ❌ | ❌ |
| NavigationTabs | shared | components/deviceapps/shared/navigation-tabs.tsx | ❌ | ❌ | ✅ |
| NavigationTabs | shared | backups/components/deviceapps/shared/navigation-tabs.tsx | ❌ | ❌ | ✅ |
| NotificationPopover | ui | components/ui/notification.tsx | ❌ | ❌ | ❌ |
| NotificationPopover | ui | backups/components/ui/notification.tsx | ❌ | ❌ | ❌ |
| NotificationSettings | component | components/settings/NotificationSettings.tsx | ❌ | ❌ | ❌ |
| pagination | ui | components/ui/pagination.tsx | ❌ | ❌ | ✅ |
| pagination | ui | backups/components/ui/pagination.tsx | ❌ | ❌ | ✅ |
| ParentAuthForm | component | components/deviceapps/parent-auth-form.tsx | ❌ | ❌ | ✅ |
| ParentAuthForm | component | backups/components/deviceapps/parent-auth-form.tsx | ❌ | ❌ | ✅ |
| ParsingScreen | component | components/receipts/parsing-screen.tsx | ❌ | ❌ | ✅ |
| ParsingScreen | component | backups/components/receipts/parsing-screen.tsx | ❌ | ❌ | ✅ |
| PasswordStrengthIndicator | component | components/auth/password-strength-indicator.tsx | ❌ | ❌ | ✅ |
| PendingLoginRequests | component | components/children/pending-login-requests.tsx | ❌ | ❌ | ❌ |
| PendingLoginRequests | component | backups/components/children/pending-login-requests.tsx | ❌ | ❌ | ❌ |
| PeopleTab | component | components/calendar/event-tabs/people-tab.tsx | ❌ | ❌ | ✅ |
| PeopleTab | component | backups/components/calendar/event-tabs/people-tab.tsx | ❌ | ❌ | ✅ |
| PerformanceAchievements | component | components/public-profile/performance-achievements.tsx | ❌ | ❌ | ✅ |
| PermissionDebugger | component | components/debug/PermissionDebugger.tsx | ❌ | ❌ | ❌ |
| PermissionFixer | component | components/debug/PermissionFixer.tsx | ❌ | ❌ | ❌ |
| PlanBoxes | component | components/admin/dashboard/plan-boxes.tsx | ❌ | ❌ | ✅ |
| popover | ui | components/ui/popover.tsx | ❌ | ❌ | ❌ |
| popover | ui | backups/components/ui/popover.tsx | ❌ | ❌ | ❌ |
| PracticeAssignmentForm | component | components/practice/assignment/practice-assignment-form.tsx | ❌ | ❌ | ✅ |
| PracticeAssignmentForm | component | backups/components/practice/assignment/practice-assignment-form.tsx | ❌ | ❌ | ✅ |
| PracticeAssignmentList | component | components/practice/assignment/practice-assignment-list.tsx | ❌ | ❌ | ✅ |
| PracticeAssignmentList | component | backups/components/practice/assignment/practice-assignment-list.tsx | ❌ | ❌ | ✅ |
| PracticeAssignments | component | deviceapps-standalone/components/kids/practice-assignments.tsx | ❌ | ❌ | ✅ |
| PracticeCard | shared | components/deviceapps/shared/practice-card.tsx | ❌ | ❌ | ✅ |
| PracticeCard | shared | backups/components/deviceapps/shared/practice-card.tsx | ❌ | ❌ | ✅ |
| PracticeContributionGraph | context | components/practice/practice-contribution-graph.tsx | ❌ | ❌ | ✅ |
| PracticeContributionGraph | context | backups/components/practice/practice-contribution-graph.tsx | ❌ | ❌ | ✅ |
| PracticeDashboardChart | component | components/children/practice-dashboard-chart.tsx | ❌ | ❌ | ✅ |
| PracticeDetails | component | components/practice/practice-details.tsx | ❌ | ❌ | ✅ |
| PracticeDetails | component | backups/components/practice/practice-details.tsx | ❌ | ❌ | ✅ |
| PracticeForm | component | components/practice/practice-form.tsx | ❌ | ❌ | ✅ |
| PracticeForm | component | backups/components/practice/practice-form.tsx | ❌ | ❌ | ✅ |
| PracticeHistory | component | components/practice/practice-history.tsx | ❌ | ❌ | ❌ |
| PracticeHistory | component | backups/components/practice/practice-history.tsx | ❌ | ❌ | ❌ |
| PracticeRatings | component | components/practice/practice-ratings.tsx | ❌ | ❌ | ✅ |
| PracticeRatings | component | backups/components/practice/practice-ratings.tsx | ❌ | ❌ | ✅ |
| PracticeSections | component | components/practice/practice-sections.tsx | ❌ | ❌ | ✅ |
| PracticeSections | component | backups/components/practice/practice-sections.tsx | ❌ | ❌ | ✅ |
| PracticeStatsCard | component | components/practice/practice-stats.tsx | ❌ | ❌ | ❌ |
| PracticeStatsCard | component | backups/components/practice/practice-stats.tsx | ❌ | ❌ | ❌ |
| PracticeStatsCards | component | components/practice/practice-stats-cards.tsx | ❌ | ❌ | ✅ |
| PracticeStatsCards | component | backups/components/practice/practice-stats-cards.tsx | ❌ | ❌ | ✅ |
| PracticeTimer | component | components/practice/practice-timer.tsx | ❌ | ❌ | ✅ |
| PracticeTimer | component | backups/components/practice/practice-timer.tsx | ❌ | ❌ | ✅ |
| PracticeTips | component | components/practice/practice-tips.tsx | ❌ | ❌ | ✅ |
| PracticeTips | component | backups/components/practice/practice-tips.tsx | ❌ | ❌ | ✅ |
| PracticeTracker | component | deviceapps-standalone/components/kids/practice-tracker.tsx | ❌ | ❌ | ✅ |
| PracticeTypeSelector | component | components/practice/practice-type-selector.tsx | ❌ | ❌ | ✅ |
| PracticeTypeSelector | component | backups/components/practice/practice-type-selector.tsx | ❌ | ❌ | ✅ |
| ProfessionalOverview | component | components/public-profile/professional-overview.tsx | ❌ | ❌ | ✅ |
| ProfileBasedChildCard | component | components/children/profile-based-child-card.tsx | ❌ | ❌ | ✅ |
| ProfileBasedChildCard | component | backups/components/children/profile-based-child-card.tsx | ❌ | ❌ | ✅ |
| ProfileBasedChildrenList | context | components/children/profile-based-children-list.tsx | ❌ | ❌ | ❌ |
| ProfileBasedChildrenList | context | backups/components/children/profile-based-children-list.tsx | ❌ | ❌ | ❌ |
| ProfileForm | component | components/profile/ProfileForm.tsx | ❌ | ❌ | ✅ |
| ProfileForm | component | backups/components/profile/ProfileForm.tsx | ❌ | ❌ | ✅ |
| ProfileHeader | component | components/public-profile/profile-header.tsx | ❌ | ❌ | ✅ |
| ProfileSelector | context | components/profile/ProfileSelector.tsx | ❌ | ❌ | ✅ |
| ProfileSelector | context | backups/components/profile/ProfileSelector.tsx | ❌ | ❌ | ✅ |
| ProfileSettings | component | components/settings/ProfileSettings.tsx | ❌ | ❌ | ❌ |
| ProfileSwitcher | context | components/profile/ProfileSwitcher.tsx | ❌ | ❌ | ✅ |
| ProfileSwitcher | component | components/deviceapps/profile-switcher.tsx | ❌ | ❌ | ✅ |
| ProfileSwitcher | component | deviceapps-standalone/components/kids/account-switcher.tsx | ❌ | ❌ | ✅ |
| ProfileSwitcher | shared | components/deviceapps/shared/profile-switcher.tsx | ❌ | ❌ | ✅ |
| ProfileSwitcher | context | backups/components/profile/ProfileSwitcher.tsx | ❌ | ❌ | ✅ |
| ProfileSwitcher | component | backups/components/deviceapps/profile-switcher.tsx | ❌ | ❌ | ✅ |
| ProfileSwitcher | shared | backups/components/deviceapps/shared/profile-switcher.tsx | ❌ | ❌ | ✅ |
| progress | ui | components/ui/progress.tsx | ❌ | ❌ | ✅ |
| progress | ui | backups/components/ui/progress.tsx | ❌ | ❌ | ❌ |
| ProgressCircle | ui | components/ui/progress-circle.tsx | ❌ | ❌ | ✅ |
| ProgressStreaks | component | components/dashboard/progress-streaks.tsx | ❌ | ❌ | ✅ |
| PublicLayout | layout | components/layout/public-layout.tsx | ❌ | ❌ | ✅ |
| PublicProfileDisplay | component | components/public-profile/public-profile-display.tsx | ❌ | ❌ | ✅ |
| PublicProfileSettings | component | components/settings/PublicProfileSettings.tsx | ❌ | ❌ | ❌ |
| PushNotificationSetup | component | components/notifications/push-notification-setup.tsx | ❌ | ❌ | ❌ |
| QRActivation | component | components/account/qr-activation.tsx | ❌ | ❌ | ✅ |
| QRActivation | component | backups/components/account/qr-activation.tsx | ❌ | ❌ | ✅ |
| QRScanner | component | components/account/qr-scanner.tsx | ❌ | ❌ | ✅ |
| QRScanner | component | backups/components/account/qr-scanner.tsx | ❌ | ❌ | ✅ |
| QuickActions | component | deviceapps-standalone/components/kids/quick-actions.tsx | ❌ | ❌ | ✅ |
| radio-group | ui | components/ui/radio-group.tsx | ❌ | ❌ | ❌ |
| radio-group | ui | backups/components/ui/radio-group.tsx | ❌ | ❌ | ❌ |
| RangeSlider | component | components/setlists/wizard/range-slider.tsx | ❌ | ❌ | ✅ |
| ReceiptCapture | context | components/receipts/receipt-capture.tsx | ❌ | ❌ | ❌ |
| ReceiptCapture | context | backups/components/receipts/receipt-capture.tsx | ❌ | ❌ | ❌ |
| ReceiptDetail | component | components/receipts/receipt-detail.tsx | ❌ | ❌ | ✅ |
| ReceiptDetail | component | backups/components/receipts/receipt-detail.tsx | ❌ | ❌ | ✅ |
| ReceiptEdit | component | components/receipts/receipt-edit.tsx | ❌ | ❌ | ✅ |
| ReceiptEdit | component | backups/components/receipts/receipt-edit.tsx | ❌ | ❌ | ✅ |
| ReceiptLibrary | component | components/receipts/receipt-library.tsx | ❌ | ❌ | ❌ |
| ReceiptLibrary | component | backups/components/receipts/receipt-library.tsx | ❌ | ❌ | ❌ |
| ReceiptPreview | component | components/receipts/receipt-preview.tsx | ❌ | ❌ | ✅ |
| ReceiptPreview | component | backups/components/receipts/receipt-preview.tsx | ❌ | ❌ | ✅ |
| RequirementsWizard | component | components/dashboard/enterprise-plan-selector.tsx | ❌ | ❌ | ✅ |
| resizable | ui | components/ui/resizable.tsx | ❌ | ❌ | ❌ |
| resizable | ui | backups/components/ui/resizable.tsx | ❌ | ❌ | ❌ |
| RobustAuthProvider | provider | components/auth/robust-auth-provider.tsx | ❌ | ❌ | ❌ |
| RoleConfirmationDialog | component | components/settings/role-confirmation-dialog.tsx | ❌ | ❌ | ✅ |
| RoleConfirmationDialog | component | backups/components/settings/role-confirmation-dialog.tsx | ❌ | ❌ | ✅ |
| RoleSettings | component | components/settings/role-settings.tsx | ❌ | ❌ | ❌ |
| RoleSettings | component | backups/components/settings/role-settings.tsx | ❌ | ❌ | ❌ |
| RolesStep | component | components/children/add-steps/roles-step.tsx | ❌ | ❌ | ✅ |
| RudimentOptions | component | components/practice/rudiment-options.tsx | ❌ | ❌ | ✅ |
| RudimentOptions | component | backups/components/practice/rudiment-options.tsx | ❌ | ❌ | ✅ |
| SCALE | component | components/practice/simple-scale-selector.tsx | ❌ | ❌ | ✅ |
| SCALE | component | components/practice/scale-selector.tsx | ❌ | ❌ | ✅ |
| SCALE | component | backups/components/practice/simple-scale-selector.tsx | ❌ | ❌ | ✅ |
| SCALE | component | backups/components/practice/scale-selector.tsx | ❌ | ❌ | ✅ |
| scroll-area | ui | components/ui/scroll-area.tsx | ❌ | ❌ | ❌ |
| scroll-area | ui | backups/components/ui/scroll-area.tsx | ❌ | ❌ | ❌ |
| Section | ui | components/web/ui/styled-components.tsx | ❌ | ❌ | ❌ |
| select | ui | components/ui/select.tsx | ❌ | ❌ | ❌ |
| select | ui | backups/components/ui/select.tsx | ❌ | ❌ | ❌ |
| separator | ui | components/ui/separator.tsx | ❌ | ❌ | ❌ |
| separator | ui | backups/components/ui/separator.tsx | ❌ | ❌ | ❌ |
| SetlistBuilder | context | components/setlists/wizard/setlist-builder.tsx | ❌ | ❌ | ✅ |
| SetlistFlowChart | component | components/setlists/setlist-flow-chart.tsx | ❌ | ❌ | ✅ |
| SetlistFlowChart | component | components/setlists/wizard/setlist-flow-chart.tsx | ❌ | ❌ | ✅ |
| SetlistGenerator | component | components/setlists/wizard/setlist-generator.tsx | ❌ | ❌ | ✅ |
| SetlistManager | component | components/setlists/setlist-manager.tsx | ❌ | ❌ | ✅ |
| SetlistManager | component | backups/components/setlists/setlist-manager.tsx | ❌ | ❌ | ✅ |
| SetlistWizard | context | components/setlists/wizard/setlist-wizard.tsx | ❌ | ❌ | ❌ |
| SharedDeviceAccess | component | components/children/shared-device-access.tsx | ❌ | ❌ | ✅ |
| SharedDeviceAccess | component | backups/components/children/shared-device-access.tsx | ❌ | ❌ | ✅ |
| sheet | ui | components/ui/sheet.tsx | ❌ | ❌ | ✅ |
| sheet | ui | backups/components/ui/sheet.tsx | ❌ | ❌ | ✅ |
| sidebar | ui | components/ui/sidebar.tsx | ❌ | ❌ | ❌ |
| sidebar | ui | backups/components/ui/sidebar.tsx | ❌ | ❌ | ❌ |
| Sidebar | layout | components/layout/sidebar.tsx | ❌ | ❌ | ❌ |
| Sidebar | layout | backups/components/layout/sidebar.tsx | ❌ | ❌ | ❌ |
| SignupStats | component | components/admin/dashboard/signup-stats.tsx | ❌ | ❌ | ✅ |
| SimpleEventSelector | component | components/finances/simple-event-selector.tsx | ❌ | ❌ | ✅ |
| SimpleEventSelector | component | backups/components/finances/simple-event-selector.tsx | ❌ | ❌ | ✅ |
| SimpleGearSelector | component | components/finances/simple-gear-selector.tsx | ❌ | ❌ | ✅ |
| SimpleGearSelector | component | backups/components/finances/simple-gear-selector.tsx | ❌ | ❌ | ✅ |
| SimpleOTPInput | ui | components/ui/simple-otp-input.tsx | ❌ | ❌ | ✅ |
| SimpleOTPInput | ui | deviceapps-standalone/components/ui/simple-otp-input.tsx | ❌ | ❌ | ✅ |
| SimpleOTPInput | ui | backups/components/ui/simple-otp-input.tsx | ❌ | ❌ | ✅ |
| SimplifiedPractice | component | components/kids/practice/simplified-practice.tsx | ❌ | ❌ | ✅ |
| SimplifiedPractice | component | backups/components/kids/practice/simplified-practice.tsx | ❌ | ❌ | ✅ |
| SimplifiedTransactionDetail | component | components/finances/simplified-transaction-detail.tsx | ❌ | ❌ | ✅ |
| SimplifiedTransactionDetail | component | backups/components/finances/simplified-transaction-detail.tsx | ❌ | ❌ | ✅ |
| SimplifiedTransactionForm | component | components/finances/simplified-transaction-form.tsx | ❌ | ❌ | ✅ |
| SimplifiedTransactionForm | component | backups/components/finances/simplified-transaction-form.tsx | ❌ | ❌ | ✅ |
| skeleton | ui | components/ui/skeleton.tsx | ❌ | ❌ | ❌ |
| skeleton | ui | backups/components/ui/skeleton.tsx | ❌ | ❌ | ❌ |
| SkillLevelBadge | context | components/settings/skill-level-badge.tsx | ❌ | ❌ | ✅ |
| SkillLevelBadge | context | backups/components/settings/skill-level-badge.tsx | ❌ | ❌ | ✅ |
| SkillLevelEditor | component | components/settings/skill-level-editor.tsx | ❌ | ❌ | ✅ |
| SkillLevelEditor | component | backups/components/settings/skill-level-editor.tsx | ❌ | ❌ | ✅ |
| SkillLevelSelector | component | components/settings/skill-level-selector.tsx | ❌ | ❌ | ✅ |
| SkillLevelSelector | component | backups/components/settings/skill-level-selector.tsx | ❌ | ❌ | ✅ |
| slider | ui | components/ui/slider.tsx | ❌ | ❌ | ❌ |
| slider | ui | backups/components/ui/slider.tsx | ❌ | ❌ | ❌ |
| SocialLinks | component | components/public-profile/social-links.tsx | ❌ | ❌ | ✅ |
| SongCard | shared | components/deviceapps/shared/song-card.tsx | ❌ | ❌ | ✅ |
| SongCard | shared | backups/components/deviceapps/shared/song-card.tsx | ❌ | ❌ | ✅ |
| SongDetail | context | components/songs/song-detail.tsx | ❌ | ❌ | ✅ |
| SongDetail | component | backups/components/songs/song-detail.tsx | ❌ | ❌ | ✅ |
| SongFilter | component | components/practice/song-filter.tsx | ❌ | ❌ | ✅ |
| SongFilter | component | backups/components/practice/song-filter.tsx | ❌ | ❌ | ✅ |
| SongLibrary | component | components/setlists/wizard/song-library.tsx | ❌ | ❌ | ✅ |
| SongList | component | components/songs/song-list.tsx | ❌ | ❌ | ✅ |
| SongList | component | backups/components/songs/song-list.tsx | ❌ | ❌ | ✅ |
| SongSelector | component | components/practice/song-selector.tsx | ❌ | ❌ | ✅ |
| SongSelector | component | backups/components/practice/song-selector.tsx | ❌ | ❌ | ✅ |
| SongVariantForm | component | components/songs/song-variant-form.tsx | ❌ | ❌ | ✅ |
| SongVariantForm | component | backups/components/songs/song-variant-form.tsx | ❌ | ❌ | ✅ |
| sonner | ui | components/ui/sonner.tsx | ❌ | ❌ | ✅ |
| sonner | ui | backups/components/ui/sonner.tsx | ❌ | ❌ | ✅ |
| SpotifyButton | ui | components/ui/spotify-button.tsx | ❌ | ❌ | ✅ |
| SpotifyButtonDemo | ui | components/ui/spotify-button-demo.tsx | ❌ | ❌ | ❌ |
| SpotifyIcon | ui | components/ui/icons.tsx | ❌ | ❌ | ✅ |
| SpotifyImportDialog | component | components/setlists/spotify-import-dialog.tsx | ❌ | ❌ | ✅ |
| SpotifyStatus | ui | components/ui/spotify-status.tsx | ❌ | ❌ | ✅ |
| SubscriptionAnalytics | component | components/admin/dashboard/subscription-analytics.tsx | ❌ | ❌ | ✅ |
| SubscriptionManager | component | components/dashboard/subscription-manager.tsx | ❌ | ❌ | ✅ |
| SubscriptionManager | component | components/admin/dashboard/subscription-manager.tsx | ❌ | ❌ | ✅ |
| SummaryTab | component | components/calendar/event-tabs/summary-tab.tsx | ❌ | ❌ | ✅ |
| SummaryTab | component | backups/components/calendar/event-tabs/summary-tab.tsx | ❌ | ❌ | ✅ |
| SupabaseProvider | provider | components/SupabaseProvider.tsx | ❌ | ❌ | ❌ |
| switch | ui | components/ui/switch.tsx | ❌ | ❌ | ❌ |
| switch | ui | backups/components/ui/switch.tsx | ❌ | ❌ | ❌ |
| table | ui | components/ui/table.tsx | ❌ | ❌ | ❌ |
| table | ui | backups/components/ui/table.tsx | ❌ | ❌ | ❌ |
| tabs | ui | components/ui/tabs.tsx | ❌ | ❌ | ❌ |
| tabs | ui | backups/components/ui/tabs.tsx | ❌ | ❌ | ❌ |
| TeachingLearning | component | components/public-profile/teaching-learning.tsx | ❌ | ❌ | ✅ |
| textarea | ui | components/ui/textarea.tsx | ❌ | ❌ | ❌ |
| textarea | ui | backups/components/ui/textarea.tsx | ❌ | ❌ | ❌ |
| ThemeProvider | provider | components/theme-provider.tsx | ❌ | ❌ | ✅ |
| ThemeProvider | provider | backups/components/theme-provider.tsx | ❌ | ❌ | ✅ |
| toast | ui | components/ui/toast.tsx | ❌ | ❌ | ✅ |
| toast | ui | backups/components/ui/toast.tsx | ❌ | ❌ | ✅ |
| Toaster | ui | components/ui/toaster.tsx | ❌ | ❌ | ❌ |
| Toaster | ui | backups/components/ui/toaster.tsx | ❌ | ❌ | ❌ |
| toggle | ui | components/ui/toggle.tsx | ❌ | ❌ | ✅ |
| toggle | ui | backups/components/ui/toggle.tsx | ❌ | ❌ | ✅ |
| toggle-group | ui | components/ui/toggle-group.tsx | ❌ | ❌ | ✅ |
| toggle-group | ui | backups/components/ui/toggle-group.tsx | ❌ | ❌ | ✅ |
| tooltip | ui | components/ui/tooltip.tsx | ❌ | ❌ | ❌ |
| tooltip | ui | backups/components/ui/tooltip.tsx | ❌ | ❌ | ❌ |
| TransactionDetail | component | components/finances/transaction-detail.tsx | ❌ | ❌ | ✅ |
| TransactionDetail | component | backups/components/finances/transaction-detail.tsx | ❌ | ❌ | ✅ |
| TravelTab | component | components/calendar/event-tabs/travel-tab.tsx | ❌ | ❌ | ✅ |
| TravelTab | component | backups/components/calendar/event-tabs/travel-tab.tsx | ❌ | ❌ | ✅ |
| TypeSelector | component | components/gear/type-selector.tsx | ❌ | ❌ | ✅ |
| TypeSelector | component | backups/components/gear/type-selector.tsx | ❌ | ❌ | ✅ |
| UpcomingEvents | component | components/dashboard/upcoming-events.tsx | ❌ | ❌ | ✅ |
| UserActivityModal | component | components/admin/dashboard/user-activity-modal.tsx | ❌ | ❌ | ✅ |
| UserManagementModal | component | components/admin/dashboard/user-management-modal.tsx | ❌ | ❌ | ✅ |
| UserProfileCard | component | components/profiles/user-profile-card.tsx | ❌ | ❌ | ✅ |
| UserProfileCard | component | backups/components/profiles/user-profile-card.tsx | ❌ | ❌ | ✅ |
| WrapUpTab | component | components/calendar/event-tabs/wrap-up-tab.tsx | ❌ | ❌ | ✅ |
| WrapUpTab | component | backups/components/calendar/event-tabs/wrap-up-tab.tsx | ❌ | ❌ | ✅ |
