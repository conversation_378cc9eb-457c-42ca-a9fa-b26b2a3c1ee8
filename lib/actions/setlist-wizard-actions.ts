"use server";

import { createServerActionClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import type {
  Song as SetlistWizardSong,
  Setlist as SetlistWizardSetlist,
  SetlistItem as SetlistWizardSetlistItem,
} from "@/lib/types/setlist-wizard";
import { createSetlist as createSetlistQuery } from "@/lib/queries/setlistQueries";
import { generateSlug } from "@/lib/utils/generate-slug";
import type { SupabaseClient } from "@supabase/supabase-js";
import type { Database } from "@/lib/database.types";
import type { Setlist, SetlistItem } from "@/lib/types/setlist"; // Core application types
import { v4 as generateUuid } from "uuid";

// We need a way to get the absolute URL for fetch in server actions
function getAbsoluteUrl(path: string): string {
  if (process.env.NODE_ENV === "development") {
    const port = process.env.PORT || "3000";
    return `http://localhost:${port}${path}`;
  }
  const baseUrl =
    process.env.NEXT_PUBLIC_APP_URL ||
    process.env.VERCEL_URL ||
    "http://localhost:3000";
  return `${baseUrl.startsWith("http") ? baseUrl : `https://${baseUrl}`}${path}`;
}

interface ApiSongResult {
  id: string;
  title: string;
  primary_artist_name?: string | null;
  artist?: string | null;
  album_name?: string | null;
  album?: string | null;
  album_cover_url?: string | null;
  imageUrl?: string | null;
  duration_ms?: number | null;
  provider: "database" | "spotify" | "genius" | string;
  provider_id: string;
  genre?: string;
  year?: number;
  valence?: number;
  danceability?: number;
  energy?: number;
  bpm?: number;
  key?: number;
  mode?: number;
  popularity?: number;
  isrc?: string;
  spotify_id?: string;
  genius_id?: string;
}

export async function fetchSongsForSetlistWizard(
  userId: string,
): Promise<SetlistWizardSong[]> {
  console.log(
    `[fetchSongsForSetlistWizard] Calling API to fetch songs for user ${userId}...`,
  );

  try {
    const apiUrl = getAbsoluteUrl("/api/songs?fetch_mode=library");
    console.log(`[fetchSongsForSetlistWizard] Fetching from: ${apiUrl}`);

    // To ensure authentication works when a server action calls a route handler,
    // we pass along the cookies. The `fetch` API in Next.js automatically forwards cookies
    // for same-origin requests by default, but explicitly creating a headers object
    // and passing the cookie header is more robust, especially if origins/ports differ in complex setups.
    const cookieStore = await cookies();
    const cookieHeader = Array.from(cookieStore.entries())
      .map(([name, value]) => `${name}=${value}`)
      .join("; ");

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        Cookie: cookieHeader,
      },
      cache: "no-store", // Ensure fresh data
    });

    if (!response.ok) {
      const errorBody = await response.text();
      console.error(
        `[fetchSongsForSetlistWizard] API call failed with status ${response.status}: ${errorBody}`,
      );
      throw new Error(
        `Failed to fetch songs from API: ${response.status} ${response.statusText}`,
      );
    }

    const apiResults: ApiSongResult[] = await response.json();

    if (!apiResults || apiResults.length === 0) {
      console.log(
        `[fetchSongsForSetlistWizard] No songs returned from API for user ${userId}.`,
      );
      return [];
    }

    // Map ApiSongResult to SetlistWizardSong
    const wizardSongs: SetlistWizardSong[] = apiResults.map((apiSong) => ({
      id: apiSong.id, // Assuming API returns DB song id as main id when provider is 'database'
      title: apiSong.title,
      artist: apiSong.artist || apiSong.primary_artist_name,
      album: apiSong.album || apiSong.album_name,
      duration: apiSong.duration_ms
        ? Math.round(apiSong.duration_ms / 1000)
        : 0,
      genre: apiSong.genre,
      year: apiSong.year,
      imageUrl: apiSong.imageUrl || apiSong.album_cover_url,
      valence: apiSong.valence,
      danceability: apiSong.danceability,
      energy: apiSong.energy,
      bpm: apiSong.bpm,
      key: apiSong.key,
      mode: apiSong.mode,
      popularity: apiSong.popularity,
      isrc: apiSong.isrc,
      spotify_id: apiSong.spotify_id,
      genius_id: apiSong.genius_id,
      defaultLineup: "", // Add this if available from API or set a default
    }));

    console.log(
      `[fetchSongsForSetlistWizard] Successfully fetched and mapped ${wizardSongs.length} songs from API for user ${userId}.`,
    );
    return wizardSongs;
  } catch (err) {
    console.error(
      "[fetchSongsForSetlistWizard] Error calling API or mapping songs:",
      err,
    );
    return []; // Return empty on error to prevent UI crash
  }
}

async function findUniqueSlug(
  supabase: SupabaseClient<Database>,
  initialName: string,
): Promise<string> {
  let baseSlug = generateSlug(initialName);
  let currentSlug = baseSlug;
  let count = 0;
  let unique = false;

  while (!unique) {
    const { data, error } = await supabase
      .from("setlists")
      .select("slug")
      .eq("slug", currentSlug)
      .maybeSingle();

    if (error) {
      console.error("[findUniqueSlug] Error checking for existing slug:", {
        slug: currentSlug,
        error,
      });
      // Fallback to a more random slug to avoid infinite loops on persistent DB errors
      return `${baseSlug}-${generateUuid().slice(0, 8)}`;
    }

    if (!data) {
      // Slug is unique
      unique = true;
    } else {
      // Slug exists, try appending a number
      count++;
      currentSlug = `${baseSlug}-${count}`;
    }
  }
  console.log(
    `[findUniqueSlug] Determined unique slug: ${currentSlug} for name: ${initialName}`,
  );
  return currentSlug;
}

// UUID generation is now handled by the imported generateUuid function

export async function saveSetlist(
  setlistData: Partial<
    Omit<SetlistWizardSetlist, "id" | "slug" | "created_at" | "updated_at">
  > & {
    list_items: SetlistWizardSetlistItem[];
    name: string;
    creator_profile_id: string;
    description?: string | null;
    purpose?: string;
    is_public?: boolean;
  },
): Promise<Setlist | { error: string }> {
  try {
    const supabase = createServerActionClient<Database>({ cookies });

    console.log("[saveSetlist] Received data for saving:", {
      name: setlistData.name,
      itemCount: setlistData.list_items?.length,
    });

    const uniqueSlug = await findUniqueSlug(supabase, setlistData.name);

    const mappedListItems: SetlistItem[] = Array.isArray(setlistData.list_items)
      ? setlistData.list_items.map((item, index) => {
          const baseDbItem: Partial<SetlistItem> & {
            order: number;
            type: string;
            notes?: string;
          } = {
            order: index,
            type: item.type,
            notes: item.notes || "",
          };

          if (item.type === "song" && item.song) {
            // item.song.id is from SetlistWizardSong which is a string.
            // It should be the database song's ID (number) if fetched correctly by fetchSongsForSetlistWizard.
            const songIdNum = parseInt(item.song.id, 10);
            if (isNaN(songIdNum)) {
              console.warn(
                `[saveSetlist] Invalid numeric song ID for wizard song title '${item.song.title}': ${item.song.id}. Storing with fallback title only.`,
              );
              return {
                ...baseDbItem,
                type: "song",
                title: item.song.title || "Untitled Song",
                // songId will be undefined, meaning it's a "placeholder" song in the setlist
              } as SetlistItem;
            }
            return {
              ...baseDbItem,
              type: "song",
              songId: songIdNum,
              // title for song type in DB SetlistItem is usually a fallback if song details can't be fetched.
              // The actual title comes from the 'songs' table via 'songId'.
              // Storing the wizard's song title here is fine as a placeholder.
              title: item.song.title || "Untitled Song",
            } as SetlistItem;
          } else {
            // 'break', 'speech', 'custom'
            return {
              ...baseDbItem,
              type: item.type as "break" | "speech" | "custom", // Ensure type matches DB enum/type
              title:
                item.title ||
                `${item.type.charAt(0).toUpperCase() + item.type.slice(1)}`,
              duration: item.duration, // in seconds
            } as SetlistItem;
          }
        })
      : [];

    console.log("[saveSetlist] Mapped list_items for DB:", {
      count: mappedListItems.length,
      sample: mappedListItems[0],
    });

    const finalPayloadForDb = {
      creator_profile_id: setlistData.creator_profile_id,
      list_items: mappedListItems,
      purpose: setlistData.purpose || "practice",
      is_public:
        setlistData.is_public !== undefined ? setlistData.is_public : false,
      title: setlistData.name,
      slug: uniqueSlug,
      description: setlistData.description || null,
    };

    console.log("[saveSetlist] Final payload for createSetlistQuery:", {
      title: finalPayloadForDb.title,
      slug: finalPayloadForDb.slug,
    });

    const newSetlist = await createSetlistQuery(
      supabase,
      finalPayloadForDb as any,
    ); // Using 'as any' for now due to potential complexities in matching SetlistCreationData exactly.

    if (!newSetlist) {
      console.error(
        "[saveSetlist] createSetlistQuery returned null. Payload was:",
        finalPayloadForDb,
      );
      return {
        error:
          "Failed to create setlist in the database. The operation returned no data.",
      };
    }

    console.log("[saveSetlist] Setlist created successfully:", {
      id: newSetlist.id,
      slug: newSetlist.slug,
    });
    return newSetlist;
  } catch (error: any) {
    console.error("[saveSetlist] Error during setlist save:", error);
    const errorMessage =
      error.message || "An unknown error occurred while saving the setlist.";
    if (
      error.code === "23505" ||
      (error.message && error.message.includes("unique constraint"))
    ) {
      // More robust check for unique constraint
      console.warn(
        "[saveSetlist] Unique constraint violation despite unique slug generation attempt. This might indicate a race condition or issue with findUniqueSlug logic under high concurrency / specific DB states.",
        { slugTried: "N/A here, see findUniqueSlug logs" },
      );
      return {
        error:
          "A setlist with a similar name might already exist or a conflict occurred. Please try a slightly different name or try again.",
      };
    }
    return { error: errorMessage };
  }
}
