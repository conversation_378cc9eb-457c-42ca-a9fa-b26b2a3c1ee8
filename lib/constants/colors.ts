import { ColorScheme } from "@/lib/types/child";

export const COLOR_MAP: Record<ColorScheme, string> = {
  // New standardized Tailwind colors (8 diverse options)
  red: "#ef4444", // red-500
  orange: "#f97316", // orange-500
  yellow: "#eab308", // yellow-500
  green: "#22c55e", // green-500
  blue: "#3b82f6", // blue-500
  purple: "#a855f7", // purple-500
  pink: "#ec4899", // pink-500
  slate: "#64748b", // slate-500

  // Legacy colours (deprecated but still supported)
  teal: "#14b8a6",
  plum: "#8e44ad",
  aqua: "#1abc9c",
  lime: "#2ecc71",
  amber: "#f39c12",
  rose: "#e74c3c",
  sky: "#3498db",
  indigo: "#3f51b5",
};

// New preferred colors for selection (8 diverse Tailwind colors)
export const PREFERRED_COLORS: ColorScheme[] = [
  "red",
  "orange",
  "yellow",
  "green",
  "blue",
  "purple",
  "pink",
  "slate",
];

// Legacy colors (still supported but not offered in UI)
export const LEGACY_COLORS: ColorScheme[] = [
  "teal",
  "plum",
  "aqua",
  "lime",
  "amber",
  "rose",
  "sky",
  "indigo",
];

// Helper function to get color hex value
export function getColorHex(colorScheme: ColorScheme): string {
  return COLOR_MAP[colorScheme] || COLOR_MAP.purple;
}

// Helper function to check if color is legacy
export function isLegacyColor(colorScheme: ColorScheme): boolean {
  return LEGACY_COLORS.includes(colorScheme);
}

// Helper function to get Tailwind classes for a color scheme
export function getColorClasses(colorScheme: ColorScheme) {
  // Map to standardized Tailwind colors
  switch (colorScheme) {
    // New preferred colors (standardized Tailwind)
    case "red":
      return {
        bg: "bg-red-100",
        border: "border-red-300",
        text: "text-red-800",
        accent: "bg-red-500",
        tailwindColor: "red",
      };
    case "orange":
      return {
        bg: "bg-orange-100",
        border: "border-orange-300",
        text: "text-orange-800",
        accent: "bg-orange-500",
        tailwindColor: "orange",
      };
    case "yellow":
      return {
        bg: "bg-yellow-100",
        border: "border-yellow-300",
        text: "text-yellow-800",
        accent: "bg-yellow-500",
        tailwindColor: "yellow",
      };
    case "green":
      return {
        bg: "bg-green-100",
        border: "border-green-300",
        text: "text-green-800",
        accent: "bg-green-500",
        tailwindColor: "green",
      };
    case "blue":
      return {
        bg: "bg-blue-100",
        border: "border-blue-300",
        text: "text-blue-800",
        accent: "bg-blue-500",
        tailwindColor: "blue",
      };
    case "purple":
      return {
        bg: "bg-purple-100",
        border: "border-purple-300",
        text: "text-purple-800",
        accent: "bg-purple-500",
        tailwindColor: "purple",
      };
    case "pink":
      return {
        bg: "bg-pink-100",
        border: "border-pink-300",
        text: "text-pink-800",
        accent: "bg-pink-500",
        tailwindColor: "pink",
      };
    case "slate":
      return {
        bg: "bg-slate-100",
        border: "border-slate-300",
        text: "text-slate-800",
        accent: "bg-slate-500",
        tailwindColor: "slate",
      };

    // Legacy colors (still supported)
    case "teal":
      return {
        bg: "bg-teal-100",
        border: "border-teal-300",
        text: "text-teal-800",
        accent: "bg-teal-500",
        tailwindColor: "teal",
      };
    case "plum":
      return {
        bg: "bg-purple-100",
        border: "border-purple-300",
        text: "text-purple-800",
        accent: "bg-purple-500",
        tailwindColor: "purple",
      };
    case "aqua":
      return {
        bg: "bg-cyan-100",
        border: "border-cyan-300",
        text: "text-cyan-800",
        accent: "bg-cyan-500",
        tailwindColor: "cyan",
      };
    case "lime":
      return {
        bg: "bg-lime-100",
        border: "border-lime-300",
        text: "text-lime-800",
        accent: "bg-lime-500",
        tailwindColor: "lime",
      };
    case "amber":
      return {
        bg: "bg-amber-100",
        border: "border-amber-300",
        text: "text-amber-800",
        accent: "bg-amber-500",
        tailwindColor: "amber",
      };
    case "rose":
      return {
        bg: "bg-rose-100",
        border: "border-rose-300",
        text: "text-rose-800",
        accent: "bg-rose-500",
        tailwindColor: "rose",
      };
    case "sky":
      return {
        bg: "bg-sky-100",
        border: "border-sky-300",
        text: "text-sky-800",
        accent: "bg-sky-500",
        tailwindColor: "sky",
      };
    case "indigo":
      return {
        bg: "bg-indigo-100",
        border: "border-indigo-300",
        text: "text-indigo-800",
        accent: "bg-indigo-500",
        tailwindColor: "indigo",
      };

    default:
      return {
        bg: "bg-purple-100",
        border: "border-purple-300",
        text: "text-purple-800",
        accent: "bg-purple-500",
        tailwindColor: "purple",
      };
  }
}
