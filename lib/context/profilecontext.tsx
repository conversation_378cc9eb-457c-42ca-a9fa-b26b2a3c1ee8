import React, { createContext, useContext, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { useAuth } from "@/lib/hooks/useAuth";
import {
  Profile,
  ProfileType,
  ProfileMetadata,
  InstrumentCapability,
  ProfileContext as ProfileContextType,
} from "@/lib/types/profile";
import {
  fetchUserProfiles,
  fetchPersonalProfile,
} from "@/lib/queries/profileBasedQueries";
import { fetchUserInstruments } from "@/lib/queries/unifiedInstrumentQueries";

// Create the context with a default value
const ProfileContext = createContext<ProfileContextType>({
  currentProfile: null,
  profiles: [],
  setCurrentProfile: () => {},
  isLoading: true,
  error: null,
});

// Custom hook to use the profile context
export const useProfile = () => useContext(ProfileContext);

// Helper function to validate profile metadata
const validateProfileMetadata = (metadata: any): ProfileMetadata => {
  if (!metadata) return {};

  // Ensure instruments is an array of valid instrument capabilities
  if (metadata.instruments && Array.isArray(metadata.instruments)) {
    metadata.instruments = metadata.instruments.filter(
      (instrument: any) =>
        instrument &&
        typeof instrument === "object" &&
        "instrument" in instrument &&
        "proficiency" in instrument,
    );
  } else {
    metadata.instruments = [];
  }

  // Ensure roles is an array
  if (metadata.roles && !Array.isArray(metadata.roles)) {
    metadata.roles = [];
  }

  return metadata;
};

// Provider component
export const ProfileProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [currentProfile, setCurrentProfile] = useState<Profile | null>(null);
  const [profiles, setProfiles] = useState<Profile[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const supabase = createClientComponentClient();
  const { user } = useAuth();
  const router = useRouter();

  // Load profiles when the user changes
  useEffect(() => {
    const loadProfiles = async () => {
      if (!user) {
        setCurrentProfile(null);
        setProfiles([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Fetch all profiles for the user
        const userProfiles = await fetchUserProfiles();

        // Validate and clean up profile metadata
        const cleanedProfiles = userProfiles.map((profile) => ({
          ...profile,
          metadata: validateProfileMetadata(profile.metadata),
        }));

        setProfiles(cleanedProfiles);

        // Check if we have a current profile in localStorage
        const storedProfileId = localStorage.getItem("currentProfileId");

        if (storedProfileId) {
          // Find the profile in the fetched profiles
          const storedProfile = cleanedProfiles.find(
            (p) => p.id === storedProfileId,
          );
          if (storedProfile) {
            setCurrentProfile(storedProfile);
            setIsLoading(false);
            return;
          }
        }

        // If no stored profile or it wasn't found, use the personal profile
        try {
          const personalProfile = await fetchPersonalProfile();
          const cleanedPersonalProfile = {
            ...personalProfile,
            metadata: validateProfileMetadata(personalProfile.metadata),
          };
          setCurrentProfile(cleanedPersonalProfile);
          localStorage.setItem("currentProfileId", cleanedPersonalProfile.id);
        } catch (personalError) {
          console.error("Error fetching personal profile:", personalError);

          // If we can't get the personal profile but have other profiles, use the first one
          if (cleanedProfiles.length > 0) {
            setCurrentProfile(cleanedProfiles[0]);
            localStorage.setItem("currentProfileId", cleanedProfiles[0].id);
          } else {
            setError(new Error("No profiles available"));
          }
        }
      } catch (err) {
        console.error("Error loading profiles:", err);
        setError(
          err instanceof Error ? err : new Error("Failed to load profiles"),
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadProfiles();
  }, [user]);

  // Fetch instrument capabilities for the current profile
  useEffect(() => {
    if (!currentProfile) return;
    const loadInstruments = async () => {
      try {
        // Fetch instruments based on profile type
        const instrumentData =
          currentProfile.profile_type === "child"
            ? await fetchUserInstruments(undefined, currentProfile.id)
            : await fetchUserInstruments(currentProfile.user_id);

        // Map to InstrumentCapability
        const capabilities: InstrumentCapability[] = instrumentData.map(
          (item) => ({
            instrument: item.instrument?.name || "",
            proficiency:
              item.skill_level as InstrumentCapability["proficiency"],
            yearsOfExperience: item.metadata?.yearsOfExperience,
            notes: item.metadata?.notes,
          }),
        );

        // Update currentProfile metadata with instruments
        setCurrentProfile((prev) =>
          prev
            ? {
                ...prev,
                metadata: {
                  ...prev.metadata,
                  instruments: capabilities,
                },
              }
            : null,
        );
      } catch (err) {
        console.error("Error loading instruments for profile:", err);
      }
    };

    loadInstruments();
  }, [currentProfile]);

  // Handle profile changes
  const handleSetCurrentProfile = (profile: Profile) => {
    // Validate and clean up profile metadata before setting
    const cleanedProfile = {
      ...profile,
      metadata: validateProfileMetadata(profile.metadata),
    };
    setCurrentProfile(cleanedProfile);
    localStorage.setItem("currentProfileId", cleanedProfile.id);
  };

  // Subscribe to profile changes
  useEffect(() => {
    if (!user) return;

    const profilesSubscription = supabase
      .channel("profiles-changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "profiles",
          filter: user ? `user_id=eq.${user.id}` : undefined,
        },
        async (payload) => {
          // Refresh profiles when there's a change
          try {
            const updatedProfiles = await fetchUserProfiles();
            const cleanedProfiles = updatedProfiles.map((profile) => ({
              ...profile,
              metadata: validateProfileMetadata(profile.metadata),
            }));
            setProfiles(cleanedProfiles);

            // If the current profile was updated, refresh it
            if (
              currentProfile &&
              payload.new &&
              payload.new.id === currentProfile.id
            ) {
              const cleanedProfile = {
                ...payload.new,
                metadata: validateProfileMetadata(payload.new.metadata),
              };
              setCurrentProfile(cleanedProfile as Profile);
            }
          } catch (err) {
            console.error("Error refreshing profiles after change:", err);
          }
        },
      )
      .subscribe();

    return () => {
      supabase.removeChannel(profilesSubscription);
    };
  }, [user, supabase, currentProfile]);

  return (
    <ProfileContext.Provider
      value={{
        currentProfile,
        profiles,
        setCurrentProfile: handleSetCurrentProfile,
        isLoading,
        error,
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
};

// Higher-order component to require a profile
export function withProfile<P extends object>(
  Component: React.ComponentType<P & { profile: Profile }>,
): React.FC<P> {
  const WrappedComponent = (props: P) => {
    const { currentProfile, isLoading, error } = useProfile();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading && !currentProfile && !error) {
        // Redirect to profile selection if no profile is selected
        router.push("/profiles");
      }
    }, [isLoading, currentProfile, error, router]);

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          Loading profile...
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen">
          <h1 className="text-xl font-bold text-red-500">
            Error loading profile
          </h1>
          <p className="text-gray-600">{error.message}</p>
          <button
            className="mt-4 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            onClick={() => router.push("/profiles")}
          >
            Select a Profile
          </button>
        </div>
      );
    }

    if (!currentProfile) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen">
          <h1 className="text-xl font-bold">No Profile Selected</h1>
          <p className="text-gray-600">Please select a profile to continue</p>
          <button
            className="mt-4 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            onClick={() => router.push("/profiles")}
          >
            Select a Profile
          </button>
        </div>
      );
    }

    return <Component {...props} profile={currentProfile} />;
  };

  WrappedComponent.displayName = `withProfile(${Component.displayName || Component.name || "Component"})`;

  return WrappedComponent;
}
