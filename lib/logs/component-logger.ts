"use client";

import { useEffect, useRef } from "react";
import { LogLevel } from "./logger"; // Import LogLevel from the main logger

// Simple browser-compatible logger for client components
export function createComponentLogger(componentName: string) {
  const formatLog = (level: LogLevel, message: string, data?: any) => {
    return {
      timestamp: new Date().toISOString(),
      level,
      component: componentName,
      message,
      data,
    };
  };

  const logToConsole = (level: LogLevel, message: string, data?: any) => {
    const formattedMessage = `[${level}] [${componentName}] ${message}`;

    switch (level) {
      case LogLevel.ERROR:
        console.error(formattedMessage, data || "");
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage, data || "");
        break;
      case LogLevel.INFO:
        console.info(formattedMessage, data || "");
        break;
      default:
        console.log(formattedMessage, data || "");
    }

    // In development, we could send logs to server for aggregation
    if (process.env.NODE_ENV === "development") {
      try {
        // Optional: Send logs to server endpoint
        // fetch('/api/logs', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(formatLog(level, message, data)),
        // });
      } catch (err) {
        // Ignore fetch errors for logging
      }
    }
  };

  return {
    debug: (message: string, data?: any) =>
      logToConsole(LogLevel.DEBUG, message, data),
    info: (message: string, data?: any) =>
      logToConsole(LogLevel.INFO, message, data),
    warn: (message: string, data?: any) =>
      logToConsole(LogLevel.WARN, message, data),
    error: (message: string, data?: any) =>
      logToConsole(LogLevel.ERROR, message, data),
  };
}

// React hook for component logging with lifecycle events
export function useComponentLogger(componentName: string) {
  const logger = useRef(createComponentLogger(componentName));

  useEffect(() => {
    // Copy logger.current to a variable to use in cleanup
    const currentLogger = logger.current;
    
    currentLogger.debug("Component mounted");
    return () => {
      currentLogger.debug("Component unmounted");
    };
  }, []);

  return logger.current;
}

// Default logger for general client-side use
export const clientLogger = createComponentLogger("client");
