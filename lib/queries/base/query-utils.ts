import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { Database } from "@/lib/supabase-types";

export const supabase = createClientComponentClient<Database>();

/**
 * Base error class for query operations
 */
export class QueryError extends Error {
  constructor(
    message: string,
    public readonly operation: string,
    public readonly originalError?: any,
    public readonly context?: Record<string, any>
  ) {
    super(message);
    this.name = "QueryError";
  }
}

/**
 * Get the current authenticated user session
 */
export async function getCurrentSession() {
  const { data: session, error } = await supabase.auth.getSession();
  
  if (error) {
    throw new QueryError(
      "Failed to get current session",
      "getCurrentSession",
      error
    );
  }
  
  if (!session.session) {
    throw new QueryError(
      "No authenticated session found",
      "getCurrentSession"
    );
  }
  
  return session.session;
}

/**
 * Get the current authenticated user ID
 */
export async function getCurrentUserId(): Promise<string> {
  const session = await getCurrentSession();
  return session.user.id;
}

/**
 * Check if a profile is a child profile
 */
export async function isProfileAChild(profileId: string): Promise<boolean> {
  if (!profileId) return false;

  try {
    // First check the children table
    const { data: childData, error: childError } = await supabase
      .from("children")
      .select("id")
      .eq("id", profileId)
      .single();

    if (!childError && childData) {
      console.log(`Profile ${profileId} found in children table`);
      return true;
    }

    // Then check the profiles table for profile_type = 'child'
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .select("profile_type")
      .eq("id", profileId)
      .single();

    if (!profileError && profileData && profileData.profile_type === "child") {
      console.log(
        `Profile ${profileId} found in profiles table with profile_type = 'child'`,
      );
      return true;
    }

    console.log(`Profile ${profileId} is not a child profile`);
    return false;
  } catch (err) {
    console.error(`Error checking if profile ${profileId} is a child:`, err);
    return false;
  }
}

/**
 * Validate required parameters for query operations
 */
export function validateRequiredParams(params: Record<string, any>, operation: string) {
  const missing = Object.entries(params)
    .filter(([key, value]) => !value)
    .map(([key]) => key);

  if (missing.length > 0) {
    throw new QueryError(
      `Missing required parameters: ${missing.join(", ")}`,
      operation,
      null,
      { missingParams: missing, providedParams: params }
    );
  }
}

/**
 * Generate a fallback ID for custom instruments
 */
export function generateFallbackId(prefix: string = "fallback"): string {
  return `${prefix}-${Math.random().toString(36).substring(2, 15)}`;
}

/**
 * Check if a string is a valid UUID
 */
export function isValidUUID(str: string): boolean {
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(str);
}

/**
 * Safe JSON stringify for logging
 */
export function safeStringify(obj: any, space?: number): string {
  try {
    return JSON.stringify(obj, null, space);
  } catch (err) {
    return `[Circular or non-serializable object: ${String(obj)}]`;
  }
}

/**
 * Log query operation with consistent formatting
 */
export function logQueryOperation(
  operation: string,
  details: Record<string, any> = {},
  level: "info" | "error" | "warn" = "info"
) {
  const message = `[${operation}] ${safeStringify(details)}`;
  
  switch (level) {
    case "error":
      console.error(message);
      break;
    case "warn":
      console.warn(message);
      break;
    default:
      console.log(message);
  }
}

/**
 * Handle query errors with consistent logging and error transformation
 */
export function handleQueryError(
  error: any,
  operation: string,
  context?: Record<string, any>
): never {
  logQueryOperation(operation, { error, context }, "error");
  
  if (error instanceof QueryError) {
    throw error;
  }
  
  throw new QueryError(
    error.message || "Unknown query error",
    operation,
    error,
    context
  );
}

/**
 * Retry a query operation with exponential backoff
 */
export async function retryQuery<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.warn(`Query attempt ${attempt} failed, retrying in ${delay}ms:`, error);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * Build query filters for profile-based operations
 */
export function buildProfileFilters(
  query: any,
  profileId: string,
  userId: string,
  isChildProfile: boolean
) {
  if (isChildProfile) {
    return query.eq("user_id", userId).eq("child_id", profileId);
  } else {
    return query.eq("user_id", userId).is("child_id", null);
  }
}

/**
 * Common select fields for user instruments
 */
export const USER_INSTRUMENT_SELECT_FIELDS = `
  id,
  profile_id,
  child_id,
  user_id,
  instrument_id,
  skill_level,
  is_primary,
  metadata,
  created_at,
  updated_at
`;

/**
 * Common select fields for instruments
 */
export const INSTRUMENT_SELECT_FIELDS = `
  id,
  name,
  type,
  category,
  icon
`;
