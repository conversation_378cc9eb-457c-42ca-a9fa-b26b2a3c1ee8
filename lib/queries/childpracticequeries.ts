import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { Database } from "@/lib/supabase-types";
import { fetchPracticeStats as fetchUnifiedPracticeStats } from "./unifiedPracticeQueries";

const supabase = createClientComponentClient<Database>();

export interface ChildPracticeSession {
  id: string;
  child_id: string;
  start_time: string;
  end_time?: string;
  duration_minutes?: number;
  instrument_id?: string;
  instrument?: string;
  notes?: string;
  mood?: "great" | "good" | "okay" | "difficult";
  practice_type?: string[];
  songs?: { title: string; artist?: string }[];
  skills?: string[];
  lifetime_minutes?: number;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface ChildPracticeStats {
  totalSessions: number;
  totalMinutes: number;
  averageDuration: number;
  currentStreak: number;
  longestStreak: number;
  sessionsThisWeek: number;
  minutesThisWeek: number;
  lifetimeMinutes: number;
}

// Fetch child practice sessions
export async function fetchChildPracticeSessions(
  childId: string,
  limit: number = 50,
  offset: number = 0,
): Promise<ChildPracticeSession[]> {
  try {
    const { data, error } = await supabase
      .from("practice_sessions")
      .select("*")
      .eq("child_id", childId)
      .order("start_time", { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error("Error fetching child practice sessions:", error);
      throw error;
    }

    return data as ChildPracticeSession[];
  } catch (error: any) {
    console.error("Exception in fetchChildPracticeSessions:", error);
    throw error;
  }
}

// Fetch child practice stats (using the unified practice queries)
export async function fetchChildPracticeStats(
  childId: string,
): Promise<ChildPracticeStats> {
  try {
    // Use the unified practice stats function
    const stats = await fetchUnifiedPracticeStats(undefined, childId);

    // Convert the unified stats to the child stats format
    return {
      totalSessions: stats.totalSessions,
      totalMinutes: stats.totalMinutes,
      averageDuration: stats.averageDuration,
      currentStreak: stats.currentStreak,
      longestStreak: stats.longestStreak,
      sessionsThisWeek: stats.sessionsThisWeek,
      minutesThisWeek: stats.minutesThisWeek,
      lifetimeMinutes: stats.totalMinutes, // Use totalMinutes as lifetimeMinutes
    };
  } catch (error: any) {
    console.error("Exception in fetchChildPracticeStats:", error);
    throw error;
  }
}
