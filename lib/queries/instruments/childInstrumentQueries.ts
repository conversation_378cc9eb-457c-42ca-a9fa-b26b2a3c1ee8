import { UserInstrument } from "@/lib/types/instrument";
import { 
  supabase, 
  QueryError, 
  validateRequiredParams, 
  logQueryOperation, 
  handleQueryError,
  getCurrentUserId,
  USER_INSTRUMENT_SELECT_FIELDS,
  INSTRUMENT_SELECT_FIELDS
} from "../base/query-utils";
import { findOrCreateInstrument } from "./instrumentQueries";

/**
 * Fetch all instruments for all children of the current user
 */
export async function fetchAllChildrenInstruments(): Promise<UserInstrument[]> {
  logQueryOperation("fetchAllChildrenInstruments", { action: "starting" });

  try {
    const currentUserId = await getCurrentUserId();
    
    logQueryOperation("fetchAllChildrenInstruments", { currentUserId });

    // Query for all child instruments owned by the current user
    const { data: instrumentsData, error: instrumentsError } = await supabase
      .from("user_instruments")
      .select(USER_INSTRUMENT_SELECT_FIELDS)
      .eq("user_id", currentUserId)
      .not("child_id", "is", null) // Only child instruments
      .order("is_primary", { ascending: false });

    if (instrumentsError) {
      handleQueryError(instrumentsError, "fetchAllChildrenInstruments", { currentUserId });
    }

    // If no instruments found, return empty array
    if (!instrumentsData || instrumentsData.length === 0) {
      logQueryOperation("fetchAllChildrenInstruments", { 
        result: "no_instruments_found" 
      });
      return [];
    }

    // Get all the instrument IDs
    const instrumentIds = instrumentsData.map((item) => item.instrument_id);

    // Fetch the instruments
    const { data: instrumentDetails, error: instrumentDetailsError } =
      await supabase
        .from("instruments")
        .select(INSTRUMENT_SELECT_FIELDS)
        .in("id", instrumentIds);

    if (instrumentDetailsError) {
      logQueryOperation("fetchAllChildrenInstruments", { 
        error: instrumentDetailsError, 
        action: "continuing_with_partial_data" 
      }, "warn");
    }

    // Create a map of instrument IDs to instruments
    const instrumentsMap: Record<string, any> = {};
    if (instrumentDetails) {
      instrumentDetails.forEach((instrument) => {
        instrumentsMap[instrument.id] = instrument;
      });
    }

    // Combine the data
    const data = instrumentsData.map((instrumentData) => ({
      ...instrumentData,
      instrument: instrumentsMap[instrumentData.instrument_id] || null,
    }));

    logQueryOperation("fetchAllChildrenInstruments", { 
      count: data.length, 
      success: true 
    });
    
    return data as UserInstrument[];
  } catch (err) {
    logQueryOperation("fetchAllChildrenInstruments", { 
      error: err 
    }, "error");
    
    // Return empty array instead of throwing to prevent UI from breaking
    return [];
  }
}

/**
 * Fetch instruments for a specific child
 */
export async function fetchChildInstruments(childId: string): Promise<UserInstrument[]> {
  validateRequiredParams({ childId }, "fetchChildInstruments");
  
  logQueryOperation("fetchChildInstruments", { childId });

  try {
    const currentUserId = await getCurrentUserId();
    
    logQueryOperation("fetchChildInstruments", { childId, currentUserId });

    // Query for child instruments
    const { data: instrumentsData, error: instrumentsError } = await supabase
      .from("user_instruments")
      .select(USER_INSTRUMENT_SELECT_FIELDS)
      .eq("user_id", currentUserId) // Must be owned by current user
      .eq("child_id", childId) // Must be for this specific child
      .order("is_primary", { ascending: false });

    if (instrumentsError) {
      handleQueryError(instrumentsError, "fetchChildInstruments", { 
        childId, 
        currentUserId 
      });
    }

    // If no instruments found, return empty array
    if (!instrumentsData || instrumentsData.length === 0) {
      logQueryOperation("fetchChildInstruments", { 
        childId, 
        result: "no_instruments_found" 
      });
      return [];
    }

    // Get all the instrument IDs
    const instrumentIds = instrumentsData.map((item) => item.instrument_id);

    // Fetch the instruments
    const { data: instrumentDetails, error: instrumentDetailsError } =
      await supabase
        .from("instruments")
        .select(INSTRUMENT_SELECT_FIELDS)
        .in("id", instrumentIds);

    if (instrumentDetailsError) {
      logQueryOperation("fetchChildInstruments", { 
        error: instrumentDetailsError, 
        action: "continuing_with_partial_data" 
      }, "warn");
    }

    // Create a map of instrument IDs to instruments
    const instrumentsMap: Record<string, any> = {};
    if (instrumentDetails) {
      instrumentDetails.forEach((instrument) => {
        instrumentsMap[instrument.id] = instrument;
      });
    }

    // Combine the data
    const data = instrumentsData.map((instrumentData) => ({
      ...instrumentData,
      instrument: instrumentsMap[instrumentData.instrument_id] || null,
    }));

    logQueryOperation("fetchChildInstruments", { 
      childId, 
      count: data.length, 
      success: true 
    });
    
    return data as UserInstrument[];
  } catch (err) {
    logQueryOperation("fetchChildInstruments", { 
      childId, 
      error: err 
    }, "error");
    
    // Return empty array instead of throwing to prevent UI from breaking
    return [];
  }
}

/**
 * Add an instrument for a child
 */
export async function addChildInstrument(
  childId: string,
  instrumentId: string,
  skillLevel?: string,
  isPrimary: boolean = false,
  initialMetadata: Record<string, any> = {}
): Promise<UserInstrument> {
  validateRequiredParams({ childId, instrumentId }, "addChildInstrument");
  
  logQueryOperation("addChildInstrument", { 
    childId, 
    instrumentId, 
    skillLevel, 
    isPrimary, 
    initialMetadata 
  });

  try {
    const currentUserId = await getCurrentUserId();

    // If this is set as primary, first unset any existing primary for this child
    if (isPrimary) {
      await unsetExistingChildPrimaryInstruments(childId, currentUserId);
    }

    // Handle custom instruments or non-UUID IDs
    const { instrumentId: finalInstrumentId, metadata } = await findOrCreateInstrument(
      instrumentId, 
      initialMetadata
    );

    // Prepare the data to insert
    const insertData = {
      instrument_id: finalInstrumentId,
      skill_level: skillLevel,
      is_primary: isPrimary,
      metadata: metadata,
      user_id: currentUserId, // Always set the user_id to the authenticated user (parent)
      child_id: childId, // Set child_id for child instruments
    };

    logQueryOperation("addChildInstrument", { 
      childId, 
      currentUserId, 
      insertData 
    });

    // Insert the instrument into the database
    const { data: insertedData, error } = await supabase
      .from("user_instruments")
      .insert(insertData)
      .select(USER_INSTRUMENT_SELECT_FIELDS)
      .single();

    if (error) {
      handleQueryError(error, "addChildInstrument", { 
        childId, 
        instrumentId, 
        insertData 
      });
    }

    // Fetch the instrument details
    const { data: instrumentData, error: instrumentError } = await supabase
      .from("instruments")
      .select(INSTRUMENT_SELECT_FIELDS)
      .eq("id", finalInstrumentId)
      .single();

    if (instrumentError) {
      logQueryOperation("addChildInstrument", { 
        error: instrumentError, 
        action: "continuing_with_partial_data" 
      }, "warn");
    }

    // Combine the data
    const data = {
      ...insertedData,
      instrument: instrumentData || null,
    };

    logQueryOperation("addChildInstrument", { 
      childId, 
      instrumentId: finalInstrumentId, 
      success: true 
    });
    
    return data as UserInstrument;
  } catch (err) {
    handleQueryError(err, "addChildInstrument", { childId, instrumentId });
  }
}

/**
 * Update an instrument for a child
 */
export async function updateChildInstrument(
  id: string,
  childId: string,
  updates: Partial<UserInstrument>
): Promise<UserInstrument> {
  validateRequiredParams({ id, childId }, "updateChildInstrument");
  
  logQueryOperation("updateChildInstrument", { id, childId, updates });

  try {
    const currentUserId = await getCurrentUserId();

    // If this is set as primary, first unset any existing primary for this child
    if (updates.is_primary) {
      await unsetExistingChildPrimaryInstruments(childId, currentUserId, id);
    }

    // Handle metadata updates specially
    if (updates.metadata) {
      updates.metadata = await mergeChildMetadata(id, childId, updates.metadata);
    }

    // Update the instrument in the database
    const { data: updatedData, error } = await supabase
      .from("user_instruments")
      .update(updates)
      .eq("id", id)
      .eq("user_id", currentUserId)
      .eq("child_id", childId)
      .select(USER_INSTRUMENT_SELECT_FIELDS)
      .single();

    if (error) {
      handleQueryError(error, "updateChildInstrument", { id, childId, updates });
    }

    // Fetch the instrument details
    const { data: instrumentData, error: instrumentError } = await supabase
      .from("instruments")
      .select(INSTRUMENT_SELECT_FIELDS)
      .eq("id", updatedData.instrument_id)
      .single();

    if (instrumentError) {
      logQueryOperation("updateChildInstrument", { 
        error: instrumentError, 
        action: "continuing_with_partial_data" 
      }, "warn");
    }

    // Combine the data
    const data = {
      ...updatedData,
      instrument: instrumentData || null,
    };

    logQueryOperation("updateChildInstrument", { 
      id, 
      childId, 
      success: true 
    });
    
    return data as UserInstrument;
  } catch (err) {
    handleQueryError(err, "updateChildInstrument", { id, childId });
  }
}

/**
 * Remove an instrument for a child
 */
export async function removeChildInstrument(id: string, childId: string): Promise<void> {
  validateRequiredParams({ id, childId }, "removeChildInstrument");
  
  logQueryOperation("removeChildInstrument", { id, childId });

  try {
    const currentUserId = await getCurrentUserId();

    // Delete the child instrument
    const { error } = await supabase
      .from("user_instruments")
      .delete()
      .eq("id", id)
      .eq("user_id", currentUserId)
      .eq("child_id", childId);

    if (error) {
      handleQueryError(error, "removeChildInstrument", { id, childId });
    }

    logQueryOperation("removeChildInstrument", { 
      id, 
      childId, 
      success: true 
    });
  } catch (err) {
    handleQueryError(err, "removeChildInstrument", { id, childId });
  }
}

/**
 * Helper function to unset existing primary instruments for a child
 */
async function unsetExistingChildPrimaryInstruments(
  childId: string,
  userId: string,
  excludeId?: string
): Promise<void> {
  try {
    logQueryOperation("unsetExistingChildPrimaryInstruments", { 
      childId, 
      userId, 
      excludeId 
    });

    let query = supabase
      .from("user_instruments")
      .update({ is_primary: false })
      .eq("user_id", userId)
      .eq("child_id", childId)
      .eq("is_primary", true);

    if (excludeId) {
      query = query.neq("id", excludeId);
    }

    const { error } = await query;

    if (error) {
      logQueryOperation("unsetExistingChildPrimaryInstruments", { 
        error, 
        action: "continuing_anyway" 
      }, "warn");
    } else {
      logQueryOperation("unsetExistingChildPrimaryInstruments", { success: true });
    }
  } catch (err) {
    logQueryOperation("unsetExistingChildPrimaryInstruments", { 
      error: err, 
      action: "continuing_anyway" 
    }, "warn");
  }
}

/**
 * Helper function to merge metadata with existing metadata for child instruments
 */
async function mergeChildMetadata(
  id: string,
  childId: string,
  newMetadata: Record<string, any>
): Promise<Record<string, any>> {
  try {
    logQueryOperation("mergeChildMetadata", { id, childId, newMetadata });

    const { data: currentData, error } = await supabase
      .from("user_instruments")
      .select("metadata")
      .eq("id", id)
      .eq("child_id", childId)
      .single();

    if (error) {
      logQueryOperation("mergeChildMetadata", { 
        error, 
        action: "using_new_metadata_only" 
      }, "warn");
      return newMetadata;
    }

    const mergedMetadata = {
      ...(currentData?.metadata || {}),
      ...newMetadata,
    };

    logQueryOperation("mergeChildMetadata", { 
      currentMetadata: currentData?.metadata, 
      newMetadata, 
      mergedMetadata 
    });

    return mergedMetadata;
  } catch (err) {
    logQueryOperation("mergeChildMetadata", { 
      error: err, 
      action: "using_new_metadata_only" 
    }, "warn");
    return newMetadata;
  }
}
