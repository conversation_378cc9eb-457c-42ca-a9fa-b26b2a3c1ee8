/**
 * Refactored instrument queries module
 * 
 * This module replaces the monolithic profileBasedInstrumentQueries.ts file
 * with a more maintainable, modular structure:
 * 
 * - instrumentQueries.ts: Core instrument operations
 * - userInstrumentQueries.ts: User instrument operations (including profile-based)
 * - childInstrumentQueries.ts: Child-specific instrument operations
 * - base/query-utils.ts: Shared utilities and error handling
 */

// Export all instrument queries
export {
  fetchInstruments,
  fetchInstrumentsByCategory,
  fetchInstrumentById,
  createCustomInstrument,
  findFallbackInstrument,
  verifyInstrumentExists,
} from "./instrumentQueries";

// Export all user instrument queries
export {
  fetchProfileInstruments,
  addProfileInstrument,
  updateProfileInstrument,
  removeProfileInstrument,
} from "./userInstrumentQueries";

// Export all child instrument queries
export {
  fetchAllChildrenInstruments,
  fetchChildInstruments,
  addChildInstrument,
  updateChildInstrument,
  removeChildInstrument,
} from "./childInstrumentQueries";

// Export base utilities for advanced usage
export {
  QueryError,
  getCurrentSession,
  getCurrentUserId,
  isProfileAChild,
  validateRequiredParams,
  logQueryOperation,
  handleQueryError,
  retryQuery,
  buildProfileFilters,
  USER_INSTRUMENT_SELECT_FIELDS,
  INSTRUMENT_SELECT_FIELDS,
} from "../base/query-utils";

// Backward compatibility exports
// These maintain the same API as the original profileBasedInstrumentQueries.ts
export { fetchInstruments as fetchInstrumentsLegacy } from "./instrumentQueries";
export { fetchInstrumentById as fetchInstrumentByIdLegacy } from "./instrumentQueries";
export { fetchProfileInstruments as fetchProfileInstrumentsLegacy } from "./userInstrumentQueries";
export { addProfileInstrument as addProfileInstrumentLegacy } from "./userInstrumentQueries";
export { updateProfileInstrument as updateProfileInstrumentLegacy } from "./userInstrumentQueries";
export { removeProfileInstrument as removeProfileInstrumentLegacy } from "./userInstrumentQueries";

/**
 * Migration guide from profileBasedInstrumentQueries.ts:
 * 
 * OLD: import { fetchInstruments } from "@/lib/queries/profileBasedInstrumentQueries"
 * NEW: import { fetchInstruments } from "@/lib/queries/instruments"
 * 
 * OLD: import { fetchProfileInstruments } from "@/lib/queries/profileBasedInstrumentQueries"
 * NEW: import { fetchProfileInstruments } from "@/lib/queries/instruments"
 * 
 * All function signatures remain the same for backward compatibility.
 * 
 * Benefits of the refactored structure:
 * - Better separation of concerns
 * - Improved error handling and logging
 * - More testable code with smaller, focused functions
 * - Better type safety
 * - Consistent query patterns
 * - Easier to maintain and extend
 */
