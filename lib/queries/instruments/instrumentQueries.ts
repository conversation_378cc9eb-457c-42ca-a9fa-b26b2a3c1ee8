import { Instrument } from "@/lib/types/instrument";
import { INSTRUMENTS } from "@/lib/constants/instruments";
import { 
  supabase, 
  QueryError, 
  validateRequiredParams, 
  logQueryOperation, 
  handleQueryError,
  retryQuery,
  INSTRUMENT_SELECT_FIELDS,
  isValidUUID,
  generateFallbackId
} from "../base/query-utils";

/**
 * Fetch all instruments from the database with fallback to constants
 */
export async function fetchInstruments(): Promise<Instrument[]> {
  logQueryOperation("fetchInstruments", { action: "starting" });
  
  try {
    const { data, error } = await supabase
      .from("instruments")
      .select(INSTRUMENT_SELECT_FIELDS)
      .order("name");

    if (error) {
      logQueryOperation("fetchInstruments", { error }, "error");
      throw error;
    }

    if (data && data.length > 0) {
      logQueryOperation("fetchInstruments", { 
        count: data.length, 
        source: "database" 
      });
      return data as Instrument[];
    } else {
      logQueryOperation("fetchInstruments", { 
        message: "No instruments found in database, using fallback" 
      }, "warn");
      
      // If no instruments in database, use constants as fallback
      const instruments: Instrument[] = INSTRUMENTS.map((instrument) => ({
        id: instrument.id,
        name: instrument.name,
        type: instrument.type as any,
        category: instrument.category as any,
        icon: null,
      }));

      logQueryOperation("fetchInstruments", { 
        count: instruments.length, 
        source: "constants" 
      });
      return instruments;
    }
  } catch (err) {
    logQueryOperation("fetchInstruments", { 
      error: err, 
      fallbackAction: "using constants" 
    }, "error");

    // Use constants as fallback if database query fails
    const instruments: Instrument[] = INSTRUMENTS.map((instrument) => ({
      id: instrument.id,
      name: instrument.name,
      type: instrument.type as any,
      category: instrument.category as any,
      icon: null,
    }));

    logQueryOperation("fetchInstruments", { 
      count: instruments.length, 
      source: "constants_after_error" 
    });
    return instruments;
  }
}

/**
 * Fetch instruments by category
 */
export async function fetchInstrumentsByCategory(category: string): Promise<Instrument[]> {
  validateRequiredParams({ category }, "fetchInstrumentsByCategory");
  
  logQueryOperation("fetchInstrumentsByCategory", { category });
  
  try {
    const { data, error } = await supabase
      .from("instruments")
      .select(INSTRUMENT_SELECT_FIELDS)
      .eq("category", category)
      .order("name");

    if (error) {
      handleQueryError(error, "fetchInstrumentsByCategory", { category });
    }

    logQueryOperation("fetchInstrumentsByCategory", { 
      category, 
      count: data?.length || 0 
    });
    
    return (data as Instrument[]) || [];
  } catch (err) {
    handleQueryError(err, "fetchInstrumentsByCategory", { category });
  }
}

/**
 * Fetch a single instrument by ID with fallback to constants
 */
export async function fetchInstrumentById(id: string): Promise<Instrument> {
  validateRequiredParams({ id }, "fetchInstrumentById");
  
  logQueryOperation("fetchInstrumentById", { id });
  
  try {
    const { data, error } = await supabase
      .from("instruments")
      .select(INSTRUMENT_SELECT_FIELDS)
      .eq("id", id)
      .single();

    if (error) {
      logQueryOperation("fetchInstrumentById", { id, error }, "error");
      throw error;
    }

    logQueryOperation("fetchInstrumentById", { id, found: true });
    return data as Instrument;
  } catch (err) {
    logQueryOperation("fetchInstrumentById", { 
      id, 
      error: err, 
      fallbackAction: "trying constants" 
    }, "warn");

    // Try to find the instrument in the constants file as a fallback
    const fallbackInstrument = INSTRUMENTS.find(
      (instrument) => instrument.id === id,
    );

    if (fallbackInstrument) {
      logQueryOperation("fetchInstrumentById", { 
        id, 
        found: true, 
        source: "constants" 
      });
      return {
        id: fallbackInstrument.id,
        name: fallbackInstrument.name,
        type: fallbackInstrument.type as any,
        category: fallbackInstrument.category as any,
        icon: null,
      };
    }

    // If we can't find it in the fallback either, throw the original error
    handleQueryError(err, "fetchInstrumentById", { id });
  }
}

/**
 * Create a custom instrument in the database
 */
export async function createCustomInstrument(
  name: string,
  type: string = "other",
  category: string = "Other"
): Promise<Instrument> {
  validateRequiredParams({ name }, "createCustomInstrument");
  
  logQueryOperation("createCustomInstrument", { name, type, category });
  
  try {
    const { data, error } = await supabase
      .from("instruments")
      .insert({
        name,
        type,
        category,
      })
      .select(INSTRUMENT_SELECT_FIELDS)
      .single();

    if (error) {
      handleQueryError(error, "createCustomInstrument", { name, type, category });
    }

    logQueryOperation("createCustomInstrument", { 
      name, 
      id: data.id, 
      success: true 
    });
    
    return data as Instrument;
  } catch (err) {
    handleQueryError(err, "createCustomInstrument", { name, type, category });
  }
}

/**
 * Find a fallback instrument when the requested one doesn't exist
 */
export async function findFallbackInstrument(): Promise<Instrument> {
  logQueryOperation("findFallbackInstrument", { action: "searching" });
  
  try {
    const { data, error } = await supabase
      .from("instruments")
      .select(INSTRUMENT_SELECT_FIELDS)
      .limit(1);

    if (error || !data || data.length === 0) {
      logQueryOperation("findFallbackInstrument", { 
        action: "no_instruments_found" 
      }, "warn");
      
      // Use the first instrument from constants as ultimate fallback
      const fallback = INSTRUMENTS[0];
      return {
        id: fallback.id,
        name: fallback.name,
        type: fallback.type as any,
        category: fallback.category as any,
        icon: null,
      };
    }

    logQueryOperation("findFallbackInstrument", { 
      id: data[0].id, 
      name: data[0].name 
    });
    
    return data[0] as Instrument;
  } catch (err) {
    logQueryOperation("findFallbackInstrument", { 
      error: err, 
      action: "using_constants_fallback" 
    }, "error");
    
    // Ultimate fallback to constants
    const fallback = INSTRUMENTS[0];
    return {
      id: fallback.id,
      name: fallback.name,
      type: fallback.type as any,
      category: fallback.category as any,
      icon: null,
    };
  }
}

/**
 * Verify that an instrument ID exists in the database
 */
export async function verifyInstrumentExists(instrumentId: string): Promise<boolean> {
  validateRequiredParams({ instrumentId }, "verifyInstrumentExists");
  
  try {
    const { data, error } = await supabase
      .from("instruments")
      .select("id")
      .eq("id", instrumentId)
      .single();

    return !error && !!data;
  } catch (err) {
    logQueryOperation("verifyInstrumentExists", { 
      instrumentId, 
      error: err 
    }, "warn");
    return false;
  }
}
