import { UserInstrument } from "@/lib/types/instrument";
import { 
  supabase, 
  QueryError, 
  validateRequiredParams, 
  logQueryOperation, 
  handleQueryError,
  getCurrentUserId,
  isProfileAChild,
  buildProfileFilters,
  USER_INSTRUMENT_SELECT_FIELDS,
  INSTRUMENT_SELECT_FIELDS
} from "../base/query-utils";
import { findOrCreateInstrument } from "./instrumentQueries";

/**
 * Fetch instruments for a specific profile (user or child)
 */
export async function fetchProfileInstruments(profileId: string): Promise<UserInstrument[]> {
  validateRequiredParams({ profileId }, "fetchProfileInstruments");
  
  logQueryOperation("fetchProfileInstruments", { profileId });

  try {
    const currentUserId = await getCurrentUserId();
    const isChildProfile = await isProfileAChild(profileId);
    
    logQueryOperation("fetchProfileInstruments", { 
      profileId, 
      currentUserId, 
      isChildProfile 
    });

    // Build the query based on profile type
    let query = supabase
      .from("user_instruments")
      .select(USER_INSTRUMENT_SELECT_FIELDS)
      .order("is_primary", { ascending: false });

    query = buildProfileFilters(query, profileId, currentUserId, isChildProfile);

    // Execute the query
    const { data: instrumentsData, error: instrumentsError } = await query;

    if (instrumentsError) {
      handleQueryError(instrumentsError, "fetchProfileInstruments", { 
        profileId, 
        currentUserId, 
        isChildProfile 
      });
    }

    // If no instruments found, return empty array
    if (!instrumentsData || instrumentsData.length === 0) {
      logQueryOperation("fetchProfileInstruments", { 
        profileId, 
        result: "no_instruments_found" 
      });
      return [];
    }

    // Get all the instrument IDs
    const instrumentIds = instrumentsData.map((item) => item.instrument_id);

    // Fetch the instruments
    const { data: instrumentDetails, error: instrumentDetailsError } =
      await supabase
        .from("instruments")
        .select(INSTRUMENT_SELECT_FIELDS)
        .in("id", instrumentIds);

    if (instrumentDetailsError) {
      logQueryOperation("fetchProfileInstruments", { 
        error: instrumentDetailsError, 
        action: "continuing_with_partial_data" 
      }, "warn");
    }

    // Create a map of instrument IDs to instruments
    const instrumentsMap: Record<string, any> = {};
    if (instrumentDetails) {
      instrumentDetails.forEach((instrument) => {
        instrumentsMap[instrument.id] = instrument;
      });
    }

    // Combine the data
    const data = instrumentsData.map((instrumentData) => ({
      ...instrumentData,
      instrument: instrumentsMap[instrumentData.instrument_id] || null,
    }));

    logQueryOperation("fetchProfileInstruments", { 
      profileId, 
      count: data.length, 
      success: true 
    });
    
    return data as UserInstrument[];
  } catch (err) {
    logQueryOperation("fetchProfileInstruments", { 
      profileId, 
      error: err 
    }, "error");
    
    // Return empty array instead of throwing to prevent UI from breaking
    return [];
  }
}

/**
 * Add an instrument for a profile
 */
export async function addProfileInstrument(
  profileId: string,
  instrumentId: string,
  skillLevel?: string,
  isPrimary: boolean = false,
  initialMetadata: Record<string, any> = {}
): Promise<UserInstrument> {
  validateRequiredParams({ profileId, instrumentId }, "addProfileInstrument");
  
  logQueryOperation("addProfileInstrument", { 
    profileId, 
    instrumentId, 
    skillLevel, 
    isPrimary, 
    initialMetadata 
  });

  try {
    const currentUserId = await getCurrentUserId();
    const isChildProfile = await isProfileAChild(profileId);

    // If this is set as primary, first unset any existing primary
    if (isPrimary) {
      await unsetExistingPrimaryInstruments(profileId, currentUserId, isChildProfile);
    }

    // Handle custom instruments or non-UUID IDs
    const { instrumentId: finalInstrumentId, metadata } = await findOrCreateInstrument(
      instrumentId, 
      initialMetadata
    );

    // Prepare the data to insert
    const insertData = {
      instrument_id: finalInstrumentId,
      skill_level: skillLevel,
      is_primary: isPrimary,
      metadata: metadata,
      user_id: currentUserId, // Always set the user_id to the authenticated user
      child_id: isChildProfile ? profileId : null, // Set child_id for child profiles
    };

    logQueryOperation("addProfileInstrument", { 
      profileId, 
      isChildProfile, 
      currentUserId, 
      insertData 
    });

    // Insert the instrument into the database
    const { data: insertedData, error } = await supabase
      .from("user_instruments")
      .insert(insertData)
      .select(USER_INSTRUMENT_SELECT_FIELDS)
      .single();

    if (error) {
      handleQueryError(error, "addProfileInstrument", { 
        profileId, 
        instrumentId, 
        insertData 
      });
    }

    // Fetch the instrument details
    const { data: instrumentData, error: instrumentError } = await supabase
      .from("instruments")
      .select(INSTRUMENT_SELECT_FIELDS)
      .eq("id", finalInstrumentId)
      .single();

    if (instrumentError) {
      logQueryOperation("addProfileInstrument", { 
        error: instrumentError, 
        action: "continuing_with_partial_data" 
      }, "warn");
    }

    // Combine the data
    const data = {
      ...insertedData,
      instrument: instrumentData || null,
    };

    logQueryOperation("addProfileInstrument", { 
      profileId, 
      instrumentId: finalInstrumentId, 
      success: true 
    });
    
    return data as UserInstrument;
  } catch (err) {
    handleQueryError(err, "addProfileInstrument", { profileId, instrumentId });
  }
}

/**
 * Update an instrument for a profile
 */
export async function updateProfileInstrument(
  id: string,
  profileId: string,
  updates: Partial<UserInstrument>
): Promise<UserInstrument> {
  validateRequiredParams({ id, profileId }, "updateProfileInstrument");
  
  logQueryOperation("updateProfileInstrument", { id, profileId, updates });

  try {
    const currentUserId = await getCurrentUserId();
    const isChildProfile = await isProfileAChild(profileId);

    // If this is set as primary, first unset any existing primary
    if (updates.is_primary) {
      await unsetExistingPrimaryInstruments(profileId, currentUserId, isChildProfile, id);
    }

    // Handle metadata updates specially
    if (updates.metadata) {
      updates.metadata = await mergeMetadata(id, profileId, updates.metadata, isChildProfile);
    }

    // Update the instrument in the database
    let query = supabase
      .from("user_instruments")
      .update(updates)
      .eq("id", id);

    query = buildProfileFilters(query, profileId, currentUserId, isChildProfile);

    const { data: updatedData, error } = await query
      .select(USER_INSTRUMENT_SELECT_FIELDS)
      .single();

    if (error) {
      handleQueryError(error, "updateProfileInstrument", { id, profileId, updates });
    }

    // Fetch the instrument details
    const { data: instrumentData, error: instrumentError } = await supabase
      .from("instruments")
      .select(INSTRUMENT_SELECT_FIELDS)
      .eq("id", updatedData.instrument_id)
      .single();

    if (instrumentError) {
      logQueryOperation("updateProfileInstrument", { 
        error: instrumentError, 
        action: "continuing_with_partial_data" 
      }, "warn");
    }

    // Combine the data
    const data = {
      ...updatedData,
      instrument: instrumentData || null,
    };

    logQueryOperation("updateProfileInstrument", { 
      id, 
      profileId, 
      success: true 
    });
    
    return data as UserInstrument;
  } catch (err) {
    handleQueryError(err, "updateProfileInstrument", { id, profileId });
  }
}

/**
 * Remove an instrument for a profile
 */
export async function removeProfileInstrument(id: string, profileId: string): Promise<void> {
  validateRequiredParams({ id, profileId }, "removeProfileInstrument");
  
  logQueryOperation("removeProfileInstrument", { id, profileId });

  try {
    const currentUserId = await getCurrentUserId();
    const isChildProfile = await isProfileAChild(profileId);

    // Delete based on profile type
    let query = supabase
      .from("user_instruments")
      .delete()
      .eq("id", id);

    query = buildProfileFilters(query, profileId, currentUserId, isChildProfile);

    const { error } = await query;

    if (error) {
      handleQueryError(error, "removeProfileInstrument", { id, profileId });
    }

    logQueryOperation("removeProfileInstrument", { 
      id, 
      profileId, 
      success: true 
    });
  } catch (err) {
    handleQueryError(err, "removeProfileInstrument", { id, profileId });
  }
}

/**
 * Helper function to unset existing primary instruments
 */
async function unsetExistingPrimaryInstruments(
  profileId: string,
  userId: string,
  isChildProfile: boolean,
  excludeId?: string
): Promise<void> {
  try {
    logQueryOperation("unsetExistingPrimaryInstruments", { 
      profileId, 
      userId, 
      isChildProfile, 
      excludeId 
    });

    let query = supabase
      .from("user_instruments")
      .update({ is_primary: false })
      .eq("is_primary", true);

    if (excludeId) {
      query = query.neq("id", excludeId);
    }

    query = buildProfileFilters(query, profileId, userId, isChildProfile);

    const { error } = await query;

    if (error) {
      logQueryOperation("unsetExistingPrimaryInstruments", { 
        error, 
        action: "continuing_anyway" 
      }, "warn");
    } else {
      logQueryOperation("unsetExistingPrimaryInstruments", { success: true });
    }
  } catch (err) {
    logQueryOperation("unsetExistingPrimaryInstruments", { 
      error: err, 
      action: "continuing_anyway" 
    }, "warn");
  }
}

/**
 * Helper function to merge metadata with existing metadata
 */
async function mergeMetadata(
  id: string,
  profileId: string,
  newMetadata: Record<string, any>,
  isChildProfile: boolean
): Promise<Record<string, any>> {
  try {
    logQueryOperation("mergeMetadata", { id, profileId, newMetadata });

    let query = supabase
      .from("user_instruments")
      .select("metadata")
      .eq("id", id);

    if (isChildProfile) {
      query = query.eq("child_id", profileId);
    } else {
      query = query.eq("user_id", profileId).is("child_id", null);
    }

    const { data: currentData, error } = await query.single();

    if (error) {
      logQueryOperation("mergeMetadata", { 
        error, 
        action: "using_new_metadata_only" 
      }, "warn");
      return newMetadata;
    }

    const mergedMetadata = {
      ...(currentData?.metadata || {}),
      ...newMetadata,
    };

    logQueryOperation("mergeMetadata", { 
      currentMetadata: currentData?.metadata, 
      newMetadata, 
      mergedMetadata 
    });

    return mergedMetadata;
  } catch (err) {
    logQueryOperation("mergeMetadata", { 
      error: err, 
      action: "using_new_metadata_only" 
    }, "warn");
    return newMetadata;
  }
}
