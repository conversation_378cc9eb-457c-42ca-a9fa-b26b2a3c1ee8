import { supabase } from "@/lib/supabase";
import {
  PracticeAssignment,
  CreatePracticeAssignmentParams,
  UpdatePracticeAssignmentParams,
  PracticeAssignmentStats,
} from "@/lib/types/practice-assignment";

/**
 * Fetch practice assignments for a child
 */
export async function fetchPracticeAssignments(
  childId: string,
  options: {
    completed?: boolean;
    limit?: number;
    offset?: number;
    startDate?: string;
    endDate?: string;
    assignedByType?: "parent" | "teacher";
  } = {},
): Promise<PracticeAssignment[]> {
  try {
    const {
      completed,
      limit = 50,
      offset = 0,
      startDate,
      endDate,
      assignedByType,
    } = options;

    // Build the query
    let query = supabase
      .from("lesson_assignments")
      .select(
        `
        *,
        song:song_id (
          id,
          title,
          primary_artist_name,
          album_cover_url
        )
      `,
      )
      .eq("student_profile_id", childId)
      .order("due_date", { ascending: true })
      .range(offset, offset + limit - 1);

    // Add filters if provided
    if (completed !== undefined) {
      query = query.eq("completed", completed);
    }

    if (startDate) {
      query = query.gte("due_date", startDate);
    }

    if (endDate) {
      query = query.lte("due_date", endDate);
    }

    if (assignedByType) {
      query = query.eq("status", assignedByType);
    }

    // Execute the query
    const { data, error } = await query;

    if (error) {
      console.error("Error fetching practice assignments:", error);
      throw error;
    }

    return data as PracticeAssignment[];
  } catch (error: any) {
    console.error("Exception in fetchPracticeAssignments:", error);
    throw error;
  }
}

/**
 * Create a new practice assignment
 */
export async function createPracticeAssignment(
  params: CreatePracticeAssignmentParams,
  userId: string,
): Promise<PracticeAssignment> {
  try {
    // Validate required fields
    if (!params.title) {
      throw new Error("Title is required");
    }
    if (!params.type) {
      throw new Error("Assignment type is required");
    }
    if (!params.student_profile_id) {
      throw new Error("Child ID is required");
    }

    // Prepare the data with defaults
    const assignmentData = {
      ...params,
      assigned_by_user_id: userId,
      assigned_date: new Date().toISOString(),
      completed: false,
      recurring: params.recurring || false,
      status: params.status || "parent",
    };

    console.log("Creating practice assignment with data:", assignmentData);

    const { data, error } = await supabase
      .from("lesson_assignments")
      .insert(assignmentData)
      .select()
      .single();

    if (error) {
      console.error("Error creating practice assignment:", error);
      const errorWithDetails = new Error(
        error.message || "Failed to create practice assignment",
      );
      (errorWithDetails as any).details = error;
      throw errorWithDetails;
    }

    if (!data) {
      throw new Error("No data returned from practice assignment creation");
    }

    return data as PracticeAssignment;
  } catch (error: any) {
    console.error("Exception in createPracticeAssignment:", error);
    throw error;
  }
}

/**
 * Update a practice assignment
 */
export async function updatePracticeAssignment(
  assignmentId: string,
  params: UpdatePracticeAssignmentParams,
  userId: string,
): Promise<PracticeAssignment> {
  try {
    // First verify the user has access to this assignment
    const { data: assignment, error: verifyError } = await supabase
      .from("lesson_assignments")
      .select("id, student_profile_id")
      .eq("id", assignmentId)
      .single();

    if (verifyError) {
      console.error("Error verifying practice assignment:", verifyError);
      throw verifyError;
    }

    // Verify the user has access to the child
    const { data: child, error: childError } = await supabase
      .from("children")
      .select("id")
      .eq("id", assignment.student_profile_id)
      .eq("user_id", userId)
      .single();

    if (childError) {
      console.error("Error verifying child access:", childError);
      throw new Error("You don't have permission to update this assignment");
    }

    // Update the assignment
    const { data, error } = await supabase
      .from("lesson_assignments")
      .update({
        ...params,
        updated_at: new Date().toISOString(),
      })
      .eq("id", assignmentId)
      .select()
      .single();

    if (error) {
      console.error("Error updating practice assignment:", error);
      throw error;
    }

    return data as PracticeAssignment;
  } catch (error: any) {
    console.error("Exception in updatePracticeAssignment:", error);
    throw error;
  }
}

/**
 * Delete a practice assignment
 */
export async function deletePracticeAssignment(
  assignmentId: string,
  userId: string,
): Promise<boolean> {
  try {
    // First verify the user has access to this assignment
    const { data: assignment, error: verifyError } = await supabase
      .from("lesson_assignments")
      .select("id, student_profile_id")
      .eq("id", assignmentId)
      .single();

    if (verifyError) {
      console.error("Error verifying practice assignment:", verifyError);
      throw verifyError;
    }

    // Verify the user has access to the child
    const { data: child, error: childError } = await supabase
      .from("children")
      .select("id")
      .eq("id", assignment.student_profile_id)
      .eq("user_id", userId)
      .single();

    if (childError) {
      console.error("Error verifying child access:", childError);
      throw new Error("You don't have permission to delete this assignment");
    }

    // Delete the assignment
    const { error } = await supabase
      .from("lesson_assignments")
      .delete()
      .eq("id", assignmentId);

    if (error) {
      console.error("Error deleting practice assignment:", error);
      throw error;
    }

    return true;
  } catch (error: any) {
    console.error("Exception in deletePracticeAssignment:", error);
    throw error;
  }
}

/**
 * Mark a practice assignment as completed
 */
export async function markPracticeAssignmentCompleted(
  assignmentId: string,
  userId: string,
  completed: boolean = true,
): Promise<PracticeAssignment> {
  try {
    // First verify the user has access to this assignment
    const { data: assignment, error: verifyError } = await supabase
      .from("lesson_assignments")
      .select("id, student_profile_id")
      .eq("id", assignmentId)
      .single();

    if (verifyError) {
      console.error("Error verifying practice assignment:", verifyError);
      throw verifyError;
    }

    // Verify the user has access to the child
    const { data: child, error: childError } = await supabase
      .from("children")
      .select("id")
      .eq("id", assignment.student_profile_id)
      .eq("user_id", userId)
      .single();

    if (childError) {
      console.error("Error verifying child access:", childError);
      throw new Error("You don't have permission to update this assignment");
    }

    // Update the assignment
    const { data, error } = await supabase
      .from("lesson_assignments")
      .update({
        completed,
        completed_at: completed ? new Date().toISOString() : null,
        updated_at: new Date().toISOString(),
      })
      .eq("id", assignmentId)
      .select()
      .single();

    if (error) {
      console.error("Error marking practice assignment as completed:", error);
      throw error;
    }

    return data as PracticeAssignment;
  } catch (error: any) {
    console.error("Exception in markPracticeAssignmentCompleted:", error);
    throw error;
  }
}

/**
 * Get practice assignment statistics for a child
 */
export async function getPracticeAssignmentStats(
  childId: string,
): Promise<PracticeAssignmentStats> {
  try {
    // Get total assignments
    const { data: totalData, error: totalError } = await supabase
      .from("lesson_assignments")
      .select("id", { count: "exact" })
      .eq("student_profile_id", childId);

    if (totalError) {
      console.error("Error getting total assignments:", totalError);
      throw totalError;
    }

    // Get completed assignments
    const { data: completedData, error: completedError } = await supabase
      .from("lesson_assignments")
      .select("id", { count: "exact" })
      .eq("student_profile_id", childId)
      .eq("completed", true);

    if (completedError) {
      console.error("Error getting completed assignments:", completedError);
      throw completedError;
    }

    // Get overdue assignments
    const today = new Date().toISOString().split("T")[0];
    const { data: overdueData, error: overdueError } = await supabase
      .from("lesson_assignments")
      .select("id", { count: "exact" })
      .eq("student_profile_id", childId)
      .eq("completed", false)
      .lt("due_date", today);

    if (overdueError) {
      console.error("Error getting overdue assignments:", overdueError);
      throw overdueError;
    }

    // Get upcoming assignments
    const { data: upcomingData, error: upcomingError } = await supabase
      .from("lesson_assignments")
      .select("id", { count: "exact" })
      .eq("student_profile_id", childId)
      .eq("completed", false)
      .gte("due_date", today);

    if (upcomingError) {
      console.error("Error getting upcoming assignments:", upcomingError);
      throw upcomingError;
    }

    // Get assignments by type
    const { data: byTypeData, error: byTypeError } = await supabase
      .from("lesson_assignments")
      .select("type")
      .eq("student_profile_id", childId);

    if (byTypeError) {
      console.error("Error getting assignments by type:", byTypeError);
      throw byTypeError;
    }

    // Get assignments by status
    const { data: byAssignedByData, error: byAssignedByError } = await supabase
      .from("lesson_assignments")
      .select("status")
      .eq("student_profile_id", childId);

    if (byAssignedByError) {
      console.error(
        "Error getting assignments by status:",
        byAssignedByError,
      );
      throw byAssignedByError;
    }

    // Calculate statistics
    const total = totalData.length;
    const completed = completedData.length;
    const overdue = overdueData.length;
    const upcoming = upcomingData.length;
    const completionRate = total > 0 ? (completed / total) * 100 : 0;

    // Count assignments by type
    const byType = {
      practice: 0,
      song: 0,
      scales: 0,
      rudiments: 0,
      other: 0,
    };

    byTypeData.forEach((assignment) => {
      byType[assignment.type as keyof typeof byType]++;
    });

    // Count assignments by status
    const byAssignedBy = {
      parent: 0,
      teacher: 0,
    };

    byAssignedByData.forEach((assignment) => {
      byAssignedBy[assignment.status as keyof typeof byAssignedBy]++;
    });

    return {
      total,
      completed,
      overdue,
      upcoming,
      completionRate,
      byType,
      byAssignedBy,
    };
  } catch (error: any) {
    console.error("Exception in getPracticeAssignmentStats:", error);
    throw error;
  }
}

/**
 * Get practice assignments for a date range (for calendar view)
 */
export async function getPracticeAssignmentsByDateRange(
  childId: string,
  startDate: string,
  endDate: string,
): Promise<Record<string, PracticeAssignment[]>> {
  try {
    const { data, error } = await supabase
      .from("lesson_assignments")
      .select(
        `
        *,
        song:song_id (
          id,
          title,
          primary_artist_name,
          album_cover_url
        )
      `,
      )
      .eq("student_profile_id", childId)
      .gte("due_date", startDate)
      .lte("due_date", endDate)
      .order("due_date", { ascending: true });

    if (error) {
      console.error("Error getting practice assignments by date range:", error);
      throw error;
    }

    // Group assignments by date
    const assignmentsByDate: Record<string, PracticeAssignment[]> = {};

    (data as PracticeAssignment[]).forEach((assignment) => {
      if (assignment.due_date) {
        if (!assignmentsByDate[assignment.due_date]) {
          assignmentsByDate[assignment.due_date] = [];
        }
        assignmentsByDate[assignment.due_date].push(assignment);
      }
    });

    return assignmentsByDate;
  } catch (error: any) {
    console.error("Exception in getPracticeAssignmentsByDateRange:", error);
    throw error;
  }
}
