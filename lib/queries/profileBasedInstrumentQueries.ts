/**
 * DEPRECATED: This file has been refactored into smaller, more maintainable modules.
 *
 * The new implementation is in lib/queries/instruments/ directory:
 * - instrumentQueries.ts: Core instrument operations
 * - userInstrumentQueries.ts: User instrument operations (including profile-based)
 * - childInstrumentQueries.ts: Child-specific instrument operations
 * - base/query-utils.ts: Shared utilities and error handling
 *
 * This file is kept for backward compatibility and will be removed in a future version.
 * Please migrate to the new modular structure for better maintainability and type safety.
 */

// Re-export all functions from the refactored modules for backward compatibility
export {
  // Core instrument functions
  fetchInstruments,
  fetchInstrumentsByCategory,
  fetchInstrumentById,

  // Profile-based instrument functions (main API)
  fetchProfileInstruments,
  addProfileInstrument,
  updateProfileInstrument,
  removeProfileInstrument,

  // Child instrument functions
  fetchAllChildrenInstruments,
  fetchChildInstruments,
  addChildInstrument,
  updateChildInstrument,
  removeChildInstrument,

  // Utility functions
  isProfileAChild,
  getCurrentUserId,
  QueryError,
} from "./instruments";


