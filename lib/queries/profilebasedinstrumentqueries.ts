/**
 * DEPRECATED: This file has been refactored into smaller, more maintainable modules.
 *
 * The new implementation is in lib/queries/instruments/ directory:
 * - instrumentQueries.ts: Core instrument operations
 * - userInstrumentQueries.ts: User instrument operations (including profile-based)
 * - childInstrumentQueries.ts: Child-specific instrument operations
 * - base/query-utils.ts: Shared utilities and error handling
 *
 * This file is kept for backward compatibility and will be removed in a future version.
 * Please migrate to the new modular structure for better maintainability and type safety.
 */

// Re-export all functions from the refactored modules for backward compatibility
export {
  // Core instrument functions
  fetchInstruments,
  fetchInstrumentsByCategory,
  fetchInstrumentById,

  // Profile-based instrument functions (main API)
  fetchProfileInstruments,
  addProfileInstrument,
  updateProfileInstrument,
  removeProfileInstrument,

  // Child instrument functions
  fetchAllChildrenInstruments,
  fetchChildInstruments,
  addChildInstrument,
  updateChildInstrument,
  removeChildInstrument,

  // Utility functions
  isProfileAChild,
  getCurrentUserId,
  QueryError,
} from "./instruments";

// Fetch all instruments
export async function fetchInstruments(): Promise<Instrument[]> {
  console.log("Executing fetchInstruments query...");
  try {
    const { data, error } = await supabase
      .from("instruments")
      .select("*")
      .order("name");

    if (error) {
      console.error("Error fetching instruments from database:", error);
      throw error;
    }

    if (data && data.length > 0) {
      console.log(
        `Successfully fetched ${data.length} instruments from database`,
      );
      return data as Instrument[];
    } else {
      console.log("No instruments found in database, using fallback");
      // If no instruments in database, use constants as fallback
      const instruments: Instrument[] = INSTRUMENTS.map((instrument) => ({
        id: instrument.id,
        name: instrument.name,
        type: instrument.type as any,
        category: instrument.category as any,
        icon: null,
      }));

      console.log(
        `Using ${instruments.length} instruments from constants as fallback`,
      );
      return instruments;
    }
  } catch (err) {
    console.error("Exception in fetchInstruments:", err);

    // Use constants as fallback if database query fails
    const instruments: Instrument[] = INSTRUMENTS.map((instrument) => ({
      id: instrument.id,
      name: instrument.name,
      type: instrument.type as any,
      category: instrument.category as any,
      icon: null,
    }));

    console.log(
      `Using ${instruments.length} instruments from constants as fallback after error`,
    );
    return instruments;
  }
}

// Fetch instruments by category
export async function fetchInstrumentsByCategory(
  category: string,
): Promise<Instrument[]> {
  const { data, error } = await supabase
    .from("instruments")
    .select("*")
    .eq("category", category)
    .order("name");

  if (error) throw error;
  return data as Instrument[];
}

// Fetch a single instrument by ID
export async function fetchInstrumentById(id: string): Promise<Instrument> {
  console.log(`Fetching instrument with ID: ${id}`);
  try {
    const { data, error } = await supabase
      .from("instruments")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      console.error("Error fetching instrument by ID:", error);
      throw error;
    }

    console.log("Found instrument:", data);
    return data as Instrument;
  } catch (err) {
    console.error("Exception in fetchInstrumentById, trying fallback:", err);

    // Try to find the instrument in the constants file as a fallback
    const fallbackInstrument = INSTRUMENTS.find(
      (instrument) => instrument.id === id,
    );

    if (fallbackInstrument) {
      console.log("Found instrument in fallback:", fallbackInstrument);
      return {
        id: fallbackInstrument.id,
        name: fallbackInstrument.name,
        type: fallbackInstrument.type as any,
        category: fallbackInstrument.category as any,
        icon: null,
      };
    }

    // If we can't find it in the fallback either, throw the original error
    throw err;
  }
}

// Fetch instruments for a profile
export async function fetchProfileInstruments(
  profileId: string,
): Promise<UserInstrument[]> {
  console.log(`Fetching instruments for profile ${profileId}...`);

  try {
    // Check if profileId is provided
    if (!profileId) {
      console.error("Profile ID must be provided to fetchProfileInstruments");
      return [];
    }

    // Get the current authenticated user
    const { data: session } = await supabase.auth.getSession();
    if (!session.session) {
      console.error("No authenticated session found");
      return [];
    }

    const currentUserId = session.session.user.id;
    console.log(`Current user ID: ${currentUserId}`);

    // Determine if this is a child profile or a user profile
    const isChildProfile = await isProfileAChild(profileId);
    console.log(`Profile ${profileId} is child profile: ${isChildProfile}`);

    // Build the query based on profile type
    let query;
    if (isChildProfile) {
      // For child profiles: query by user_id (authenticated user) AND child_id (specific child)
      console.log(
        `Querying for child profile: ${profileId} owned by user: ${currentUserId}`,
      );
      query = supabase
        .from("user_instruments")
        .select(
          `
          id,
          profile_id,
          child_id,
          user_id,
          instrument_id,
          skill_level,
          is_primary,
          metadata,
          created_at,
          updated_at
        `,
        )
        .eq("user_id", currentUserId) // Must be owned by current user
        .eq("child_id", profileId) // Must be for this specific child
        .order("is_primary", { ascending: false });
    } else {
      // For user profiles: query by user_id (authenticated user) AND child_id is NULL
      console.log(`Querying for user profile: ${profileId}`);
      query = supabase
        .from("user_instruments")
        .select(
          `
          id,
          profile_id,
          child_id,
          user_id,
          instrument_id,
          skill_level,
          is_primary,
          metadata,
          created_at,
          updated_at
        `,
        )
        .eq("user_id", currentUserId) // Must be owned by current user
        .is("child_id", null) // Must be user's own instruments (not child instruments)
        .order("is_primary", { ascending: false });
    }

    // Execute the query
    const { data: instrumentsData, error: instrumentsError } = await query;

    if (instrumentsError) {
      console.error("Error fetching instruments:", instrumentsError);
      console.error(
        "Error details:",
        JSON.stringify(instrumentsError, null, 2),
      );
      throw instrumentsError;
    }

    // If no instruments found, return empty array
    if (!instrumentsData || instrumentsData.length === 0) {
      console.log("No instruments found, returning empty array");
      return [];
    }

    // Get all the instrument IDs
    const instrumentIds = instrumentsData.map((item) => item.instrument_id);

    // Fetch the instruments
    const { data: instrumentDetails, error: instrumentDetailsError } =
      await supabase.from("instruments").select("*").in("id", instrumentIds);

    if (instrumentDetailsError) {
      console.error(
        "Error fetching instrument details:",
        instrumentDetailsError,
      );
      console.error(
        "Error details:",
        JSON.stringify(instrumentDetailsError, null, 2),
      );
      // Continue with partial data
    }

    // Create a map of instrument IDs to instruments
    const instrumentsMap = {};
    if (instrumentDetails) {
      instrumentDetails.forEach((instrument) => {
        instrumentsMap[instrument.id] = instrument;
      });
    }

    // Combine the data
    const data = instrumentsData.map((instrumentData) => {
      return {
        ...instrumentData,
        instrument: instrumentsMap[instrumentData.instrument_id] || null,
      };
    });

    console.log(
      `Successfully fetched ${data.length} instruments with their details`,
    );
    return data as UserInstrument[];
  } catch (err) {
    console.error("Exception in fetchProfileInstruments:", err);
    console.error("Error details:", JSON.stringify(err, null, 2));
    // Return empty array instead of throwing to prevent UI from breaking
    return [];
  }
}

// Add an instrument for a profile
export async function addProfileInstrument(
  profileId: string,
  instrumentId: string,
  skillLevel?: string,
  isPrimary: boolean = false,
  initialMetadata: Record<string, any> = {},
): Promise<UserInstrument> {
  console.log(`Adding instrument ${instrumentId} for profile ${profileId}...`);
  console.log("Initial metadata:", JSON.stringify(initialMetadata, null, 2));

  try {
    // Check if profileId is valid
    if (!profileId) {
      console.error("Invalid profileId provided to addProfileInstrument");
      throw new Error("Invalid profileId");
    }

    // Check if instrumentId is valid
    if (!instrumentId) {
      console.error("Invalid instrumentId provided to addProfileInstrument");
      throw new Error("Invalid instrumentId");
    }

    // Get the current session
    const { data: session } = await supabase.auth.getSession();
    if (!session.session) {
      throw new Error("No active session. Please log in again.");
    }

    console.log(
      `Adding instrument for profile ${profileId} as user ${session.session.user.id}`,
    );

    // Skip profile verification - we trust that the component has already verified access
    // This is consistent with how addUserInstrument works

    // If this is set as primary, first unset any existing primary
    if (isPrimary) {
      try {
        console.log("Unsetting any existing primary instruments...");
        // Determine if this is a child profile or a user profile
        const isChildProfile = await isProfileAChild(profileId);

        // Update based on profile type
        let query = supabase
          .from("user_instruments")
          .update({ is_primary: false })
          .eq("is_primary", true);

        // Get current user ID for proper filtering
        const currentUserId = session.session.user.id;

        if (isChildProfile) {
          query = query.eq("user_id", currentUserId).eq("child_id", profileId);
        } else {
          query = query.eq("user_id", currentUserId).is("child_id", null);
        }

        const { error: updateError } = await query;

        if (updateError) {
          console.error("Error unsetting primary instrument:", updateError);
          console.error("Error details:", JSON.stringify(updateError, null, 2));
          // Continue anyway, this is not critical
        } else {
          console.log("Successfully unset existing primary instruments");
        }
      } catch (updateErr) {
        console.error("Exception unsetting primary instrument:", updateErr);
        console.error("Error details:", JSON.stringify(updateErr, null, 2));
        // Continue anyway, this is not critical
      }
    }

    // Handle custom instruments or non-UUID IDs
    let finalInstrumentId = instrumentId;
    let metadata: Record<string, any> = { ...initialMetadata };
    console.log("Processing instrument ID and metadata...");

    // Check if it's a custom instrument
    if (instrumentId === "custom") {
      console.log("Creating custom instrument...");
      // For custom instruments, create a new entry in the instruments table
      try {
        // Get the custom name from the metadata if it exists
        const customName = metadata?.custom_name || "Custom Instrument";
        console.log(`Creating custom instrument with name: ${customName}`);

        const { data: newInstrument, error: createError } = await supabase
          .from("instruments")
          .insert({
            name: customName,
            type: "other",
            category: "Other",
          })
          .select()
          .single();

        if (createError) {
          console.error("Error creating custom instrument:", createError);
          console.error("Error details:", JSON.stringify(createError, null, 2));

          // Try to find an existing instrument with a similar name
          console.log(
            "Trying to find an existing instrument with a similar name...",
          );
          const { data: existingInstruments, error: searchError } =
            await supabase.from("instruments").select("*").order("name");

          if (searchError) {
            console.error(
              "Error searching for existing instruments:",
              searchError,
            );
          } else if (existingInstruments && existingInstruments.length > 0) {
            // Use the first instrument as a fallback
            finalInstrumentId = existingInstruments[0].id;
            console.log(
              `Using existing instrument with ID ${finalInstrumentId} as fallback`,
            );
          } else {
            // Generate a fallback ID if we can't find any instruments
            finalInstrumentId = `custom-${Math.random().toString(36).substring(2, 15)}`;
            console.log(
              `Using generated fallback ID for custom instrument: ${finalInstrumentId}`,
            );
          }

          metadata.is_custom = true;
          metadata.custom_name = customName;
        } else if (newInstrument) {
          finalInstrumentId = newInstrument.id;
          console.log(
            `Created custom instrument in database with ID ${finalInstrumentId}`,
          );
          // Store the name in metadata for reference
          metadata.custom_name = customName;
        }
      } catch (createErr) {
        console.error("Exception creating custom instrument:", createErr);
        console.error("Error details:", JSON.stringify(createErr, null, 2));

        // Try to find any instrument to use as a fallback
        try {
          console.log("Trying to find any instrument to use as a fallback...");
          const { data: anyInstruments, error: searchError } = await supabase
            .from("instruments")
            .select("*")
            .limit(1);

          if (searchError) {
            console.error(
              "Error searching for fallback instruments:",
              searchError,
            );
          } else if (anyInstruments && anyInstruments.length > 0) {
            finalInstrumentId = anyInstruments[0].id;
            console.log(
              `Using instrument with ID ${finalInstrumentId} as fallback`,
            );
          } else {
            // Generate a fallback ID if we can't find any instruments
            finalInstrumentId = `custom-${Math.random().toString(36).substring(2, 15)}`;
            console.log(
              `Using generated fallback ID after error: ${finalInstrumentId}`,
            );
          }
        } catch (searchErr) {
          console.error(
            "Exception searching for fallback instruments:",
            searchErr,
          );
          finalInstrumentId = `custom-${Math.random().toString(36).substring(2, 15)}`;
          console.log(
            `Using generated fallback ID after search error: ${finalInstrumentId}`,
          );
        }

        metadata.is_custom = true;
        metadata.custom_name = metadata?.custom_name || "Custom Instrument";
      }
    }
    // Check if it's a valid UUID
    else if (
      !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
        instrumentId,
      )
    ) {
      console.log(
        `Instrument ID ${instrumentId} is not a valid UUID, trying to find a valid instrument...`,
      );

      // Try to find the instrument by ID first
      try {
        const { data: foundInstrument, error: findError } = await supabase
          .from("instruments")
          .select("*")
          .eq("id", instrumentId)
          .single();

        if (findError || !foundInstrument) {
          console.log(
            `No instrument found with ID ${instrumentId}, looking for any instrument...`,
          );

          // If not found, try to get any instrument as a fallback
          const { data: anyInstruments, error: searchError } = await supabase
            .from("instruments")
            .select("*")
            .limit(1);

          if (searchError || !anyInstruments || anyInstruments.length === 0) {
            console.error(
              "Error finding fallback instrument:",
              searchError || "No instruments found",
            );
            // Generate a fallback ID
            finalInstrumentId = `fallback-${Math.random().toString(36).substring(2, 15)}`;
            metadata.original_id = instrumentId;
            console.log(`Using generated fallback ID: ${finalInstrumentId}`);
          } else {
            finalInstrumentId = anyInstruments[0].id;
            metadata.original_id = instrumentId;
            console.log(
              `Using fallback instrument with ID ${finalInstrumentId}`,
            );
          }
        } else {
          console.log(`Found instrument with ID ${instrumentId}, using it`);
          // The ID is valid, keep using it
          finalInstrumentId = instrumentId;
        }
      } catch (err) {
        console.error("Exception finding instrument:", err);
        // Generate a fallback ID
        finalInstrumentId = `fallback-${Math.random().toString(36).substring(2, 15)}`;
        metadata.original_id = instrumentId;
        console.log(
          `Using generated fallback ID after error: ${finalInstrumentId}`,
        );
      }
    }

    console.log("Final instrument ID:", finalInstrumentId);
    console.log("Final metadata:", JSON.stringify(metadata, null, 2));

    // Verify that the instrument_id exists in the instruments table
    console.log(
      `Verifying instrument ID ${finalInstrumentId} exists in the instruments table...`,
    );
    let validInstrumentId = finalInstrumentId;

    try {
      // Check if the instrument exists
      const { data: instrumentCheck, error: checkError } = await supabase
        .from("instruments")
        .select("id")
        .eq("id", finalInstrumentId);

      if (checkError || !instrumentCheck || instrumentCheck.length === 0) {
        console.log(
          `Instrument ID ${finalInstrumentId} not found in instruments table, finding a fallback...`,
        );

        // Get any instrument as a fallback
        const { data: anyInstrument, error: anyError } = await supabase
          .from("instruments")
          .select("id")
          .limit(1);

        if (anyError || !anyInstrument || anyInstrument.length === 0) {
          console.error(
            "Could not find any instrument to use as fallback:",
            anyError || "No instruments found",
          );
          throw new Error("No valid instrument ID available");
        }

        validInstrumentId = anyInstrument[0].id;
        console.log(`Using fallback instrument ID: ${validInstrumentId}`);

        // Update metadata to reflect the original ID
        metadata.original_instrument_id = finalInstrumentId;
      } else {
        console.log(
          `Instrument ID ${finalInstrumentId} verified in instruments table`,
        );
      }
    } catch (verifyErr) {
      console.error("Error verifying instrument ID:", verifyErr);
      throw new Error("Failed to verify instrument ID");
    }

    // Determine if this is a child profile or a user profile
    const isChildProfile = await isProfileAChild(profileId);

    // Get the current user ID from the session
    const userId = session.session.user.id;

    // Prepare the data to insert
    const insertData = {
      instrument_id: validInstrumentId,
      skill_level: skillLevel,
      is_primary: isPrimary,
      metadata: metadata,
      user_id: userId, // Always set the user_id to the authenticated user (parent/account owner)
      // Set child_id for child profiles, leave as NULL for user profiles
      child_id: isChildProfile ? profileId : null,
    };

    console.log("Is child profile:", isChildProfile);
    console.log("Current user ID:", userId);
    console.log("Insert data:", insertData);

    // Insert the instrument into the database
    console.log("Inserting instrument into database...");
    const { data: insertedData, error } = await supabase
      .from("user_instruments")
      .insert(insertData)
      .select(
        `
        id,
        profile_id,
        child_id,
        user_id,
        instrument_id,
        skill_level,
        is_primary,
        metadata,
        created_at,
        updated_at
      `,
      )
      .single();

    if (error) {
      console.error("Error adding instrument:", error);
      console.error("Error details:", JSON.stringify(error, null, 2));
      console.error("Request data:", JSON.stringify(insertData, null, 2));
      throw error;
    }

    // Fetch the instrument details
    const { data: instrumentData, error: instrumentError } = await supabase
      .from("instruments")
      .select("*")
      .eq("id", validInstrumentId)
      .single();

    if (instrumentError) {
      console.error("Error fetching instrument details:", instrumentError);
      // Continue with partial data
    }

    // Combine the data
    const data = {
      ...insertedData,
      instrument: instrumentData || null,
    };

    console.log(
      "Successfully added instrument:",
      JSON.stringify(data, null, 2),
    );
    return data as UserInstrument;
  } catch (err) {
    console.error("Exception in addProfileInstrument:", err);
    console.error("Error details:", JSON.stringify(err, null, 2));
    throw err;
  }
}

// Update an instrument for a profile
export async function updateProfileInstrument(
  id: string,
  profileId: string,
  updates: Partial<UserInstrument>,
): Promise<UserInstrument> {
  console.log(`Updating instrument ${id} for profile ${profileId}...`);
  console.log("Updates:", JSON.stringify(updates, null, 2));

  try {
    // Check if id is valid
    if (!id) {
      console.error("Invalid id provided to updateProfileInstrument");
      throw new Error("Invalid id");
    }

    // Check if profileId is valid
    if (!profileId) {
      console.error("Invalid profileId provided to updateProfileInstrument");
      throw new Error("Invalid profileId");
    }

    // Get the current session
    const { data: session } = await supabase.auth.getSession();
    if (!session.session) {
      throw new Error("No active session. Please log in again.");
    }

    console.log(
      `Updating instrument for profile ${profileId} as user ${session.session.user.id}`,
    );

    // Skip profile verification - we trust that the component has already verified access
    // This is consistent with how updateUserInstrument works

    // If this is set as primary, first unset any existing primary
    if (updates.is_primary) {
      try {
        console.log("Unsetting any existing primary instruments...");
        // Determine if this is a child profile or a user profile
        const isChildProfile = await isProfileAChild(profileId);

        // Update based on profile type
        let query = supabase
          .from("user_instruments")
          .update({ is_primary: false })
          .neq("id", id)
          .eq("is_primary", true);

        // Get current user ID for proper filtering
        const { data: sessionData } = await supabase.auth.getSession();
        const currentUserId = sessionData?.session?.user?.id;

        if (isChildProfile) {
          query = query.eq("user_id", currentUserId).eq("child_id", profileId);
        } else {
          query = query.eq("user_id", currentUserId).is("child_id", null);
        }

        const { error: updateError } = await query;

        if (updateError) {
          console.error(
            "Error unsetting other primary instruments:",
            updateError,
          );
          console.error("Error details:", JSON.stringify(updateError, null, 2));
          // Continue anyway, this is not critical
        } else {
          console.log("Successfully unset existing primary instruments");
        }
      } catch (updateErr) {
        console.error(
          "Exception unsetting other primary instruments:",
          updateErr,
        );
        console.error("Error details:", JSON.stringify(updateErr, null, 2));
        // Continue anyway, this is not critical
      }
    }

    // Handle metadata updates specially
    if (updates.metadata) {
      console.log("Handling metadata updates...");
      // First get the current metadata
      try {
        console.log(`Fetching current metadata for instrument ${id}...`);
        // Determine if this is a child profile or a user profile
        const isChildProfile = await isProfileAChild(profileId);

        // Fetch based on profile type
        let query = supabase
          .from("user_instruments")
          .select("metadata")
          .eq("id", id);

        if (isChildProfile) {
          query = query.eq("child_id", profileId);
        } else {
          query = query.eq("user_id", profileId).is("child_id", null);
        }

        const { data: currentData, error: fetchError } = await query.single();

        if (fetchError) {
          console.error("Error fetching current metadata:", fetchError);
          console.error("Error details:", JSON.stringify(fetchError, null, 2));
          // Continue with the update anyway
        } else if (currentData) {
          console.log(
            "Current metadata:",
            JSON.stringify(currentData.metadata, null, 2),
          );
          // Merge the current metadata with the updates
          updates.metadata = {
            ...(currentData.metadata || {}),
            ...updates.metadata,
          };
          console.log(
            "Merged metadata:",
            JSON.stringify(updates.metadata, null, 2),
          );
        } else {
          console.log("No current metadata found, using provided metadata");
        }
      } catch (fetchErr) {
        console.error("Exception fetching current metadata:", fetchErr);
        console.error("Error details:", JSON.stringify(fetchErr, null, 2));
        // Continue with the update anyway
      }
    }

    // Update the instrument in the database
    console.log(`Updating instrument ${id} in database...`);
    console.log("Final updates:", JSON.stringify(updates, null, 2));

    // Determine if this is a child profile or a user profile
    const isChildProfile = await isProfileAChild(profileId);

    // Get current user ID for proper filtering
    const { data: sessionData } = await supabase.auth.getSession();
    const currentUserId = sessionData?.session?.user?.id;

    // Update based on profile type
    let query = supabase.from("user_instruments").update(updates).eq("id", id);

    if (isChildProfile) {
      query = query.eq("user_id", currentUserId).eq("child_id", profileId);
    } else {
      query = query.eq("user_id", currentUserId).is("child_id", null);
    }

    const { data: updatedData, error } = await query
      .select(
        `
        id,
        profile_id,
        child_id,
        user_id,
        instrument_id,
        skill_level,
        is_primary,
        metadata,
        created_at,
        updated_at
      `,
      )
      .single();

    if (error) {
      console.error("Error updating instrument:", error);
      console.error("Error details:", JSON.stringify(error, null, 2));
      throw error;
    }

    // Fetch the instrument details
    const { data: instrumentData, error: instrumentError } = await supabase
      .from("instruments")
      .select("*")
      .eq("id", updatedData.instrument_id)
      .single();

    if (instrumentError) {
      console.error("Error fetching instrument details:", instrumentError);
      // Continue with partial data
    }

    // Combine the data
    const data = {
      ...updatedData,
      instrument: instrumentData || null,
    };

    console.log(
      "Successfully updated instrument:",
      JSON.stringify(data, null, 2),
    );
    return data as UserInstrument;
  } catch (err) {
    console.error("Exception in updateProfileInstrument:", err);
    console.error("Error details:", JSON.stringify(err, null, 2));
    throw err;
  }
}

// Remove an instrument for a profile
export async function removeProfileInstrument(
  id: string,
  profileId: string,
): Promise<void> {
  console.log(`Removing instrument ${id} for profile ${profileId}...`);
  try {
    // Check if id is valid
    if (!id) {
      console.error("Invalid id provided to removeProfileInstrument");
      throw new Error("Invalid id");
    }

    // Check if profileId is valid
    if (!profileId) {
      console.error("Invalid profileId provided to removeProfileInstrument");
      throw new Error("Invalid profileId");
    }

    // Get the current session
    const { data: session } = await supabase.auth.getSession();
    if (!session.session) {
      throw new Error("No active session. Please log in again.");
    }

    console.log(
      `Removing instrument for profile ${profileId} as user ${session.session.user.id}`,
    );

    // Skip profile verification - we trust that the component has already verified access
    // This is consistent with how removeUserInstrument works

    console.log(`Deleting instrument with id ${id}...`);
    // Determine if this is a child profile or a user profile
    const isChildProfile = await isProfileAChild(profileId);

    // Get current user ID for proper filtering
    const currentUserId = session.session.user.id;

    // Delete based on profile type
    let query = supabase.from("user_instruments").delete().eq("id", id);

    if (isChildProfile) {
      query = query.eq("user_id", currentUserId).eq("child_id", profileId);
    } else {
      query = query.eq("user_id", currentUserId).is("child_id", null);
    }

    const { error } = await query;

    if (error) {
      console.error("Error removing instrument:", error);
      console.error("Error details:", JSON.stringify(error, null, 2));
      throw error;
    }

    console.log("Successfully removed instrument");
  } catch (err) {
    console.error("Exception in removeProfileInstrument:", err);
    console.error("Error details:", JSON.stringify(err, null, 2));
    throw err;
  }
}
