import { supabase } from "../supabase";
import type {
  Receipt,
  ReceiptItem,
  ReceiptFormData,
  ReceiptItemFormData,
} from "../types/receipt";

// Fetch all receipts for the current user
export async function fetchReceipts() {
  // Get the current user first
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    throw new Error("User not authenticated");
  }

  const { data, error } = await supabase
    .from("receipts")
    .select("*")
    .eq("profile_id", user.id)
    .order("date", { ascending: false });

  if (error) throw error;
  return data as Receipt[];
}

// Fetch a single receipt by ID
export async function fetchReceiptById(receiptId: string) {
  const { data, error } = await supabase
    .from("receipts")
    .select("*")
    .eq("id", receiptId)
    .single();

  if (error) throw error;

  // Transform the database row to Receipt type
  const receiptData = data.data as any;
  return {
    id: data.id,
    created_at: data.created_at,
    updated_at: data.updated_at,
    ...receiptData,
  } as Receipt;
}

// Fetch receipt items for a receipt (items are stored in the receipt's data.items array)
export async function fetchReceiptItems(receiptId: string) {
  const receipt = await fetchReceiptById(receiptId);
  return (receipt.items || []) as ReceiptItem[];
}

// Fetch a receipt with its items (items are already included in the receipt data)
export async function fetchReceiptWithItems(receiptId: string) {
  return await fetchReceiptById(receiptId);
}

// Create a new receipt (store all data in JSONB data column)
export async function createReceipt(
  receiptData: ReceiptFormData & { profile_id: string },
) {
  const { data, error } = await supabase
    .from("receipts")
    .insert([{
      id: crypto.randomUUID(),
      data: receiptData,
    }])
    .select()
    .single();

  if (error) throw error;

  // Transform back to Receipt type
  const result = data.data as any;
  return {
    id: data.id,
    created_at: data.created_at || "",
    updated_at: data.updated_at || "",
    ...result,
  } as Receipt;
}

// Update a receipt (update the JSONB data column)
export async function updateReceipt(
  receiptId: string,
  receiptData: Partial<ReceiptFormData>,
) {
  // First get the current receipt to merge with updates
  const currentReceipt = await fetchReceiptById(receiptId);
  const updatedData = { ...currentReceipt, ...receiptData };

  const { data, error } = await supabase
    .from("receipts")
    .update({ data: updatedData })
    .eq("id", receiptId)
    .select()
    .single();

  if (error) throw error;

  // Transform back to Receipt type
  const result = data.data as any;
  return {
    id: data.id,
    created_at: data.created_at || "",
    updated_at: data.updated_at || "",
    ...result,
  } as Receipt;
}

// Delete a receipt
export async function deleteReceipt(receiptId: string) {
  const { error } = await supabase
    .from("receipts")
    .delete()
    .eq("id", receiptId);

  if (error) throw error;
  return true;
}

// Receipt item operations are handled within the receipt data
// Items are stored as an array in the receipt's data.items field

// Add an item to a receipt
export async function addReceiptItem(
  receiptId: string,
  itemData: ReceiptItemFormData,
) {
  const receipt = await fetchReceiptById(receiptId);
  const items = receipt.items || [];
  const newItem = {
    ...itemData,
    id: crypto.randomUUID(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const updatedReceipt = {
    ...receipt,
    items: [...items, newItem],
  };

  return await updateReceipt(receiptId, updatedReceipt);
}

// Update a receipt item
export async function updateReceiptItem(
  receiptId: string,
  itemId: string,
  itemData: Partial<ReceiptItemFormData>,
) {
  const receipt = await fetchReceiptById(receiptId);
  const items = receipt.items || [];
  const updatedItems = items.map(item =>
    item.id === itemId
      ? { ...item, ...itemData, updated_at: new Date().toISOString() }
      : item
  );

  const updatedReceipt = {
    ...receipt,
    items: updatedItems,
  };

  return await updateReceipt(receiptId, updatedReceipt);
}

// Delete a receipt item
export async function deleteReceiptItem(receiptId: string, itemId: string) {
  const receipt = await fetchReceiptById(receiptId);
  const items = receipt.items || [];
  const updatedItems = items.filter(item => item.id !== itemId);

  const updatedReceipt = {
    ...receipt,
    items: updatedItems,
  };

  await updateReceipt(receiptId, updatedReceipt);
  return true;
}

// Create a receipt with items (store everything in JSONB data column)
export async function createReceiptWithItems(
  receiptData: ReceiptFormData & { profile_id: string },
  items: Omit<ReceiptItemFormData, "receipt_id">[],
) {
  // Add items to the receipt data with generated IDs
  const itemsWithIds = items.map(item => ({
    ...item,
    id: crypto.randomUUID(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }));

  const completeReceiptData = {
    ...receiptData,
    items: itemsWithIds,
  };

  return await createReceipt(completeReceiptData);
}

// Upload receipt image to storage
export async function uploadReceiptImage(
  file: File,
  profileId: string,
): Promise<string> {
  const fileExt = file.name.split(".").pop();
  const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
  const filePath = `receipts/${profileId}/${fileName}`;

  const { error } = await supabase.storage
    .from("receipts")
    .upload(filePath, file);

  if (error) throw error;

  const { data } = supabase.storage.from("receipts").getPublicUrl(filePath);

  return data.publicUrl;
}

// Convert data URL to File object
export function dataURLtoFile(dataUrl: string, filename: string): File {
  const arr = dataUrl.split(",");
  const mime = arr[0].match(/:(.*?);/)?.[1] || "image/jpeg";
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new File([u8arr], filename, { type: mime });
}
