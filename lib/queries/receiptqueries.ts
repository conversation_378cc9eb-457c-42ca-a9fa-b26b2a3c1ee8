import { supabase } from "../supabase";
import type {
  Receipt,
  ReceiptItem,
  ReceiptFormData,
  ReceiptItemFormData,
} from "../types/receipt";

// Fetch all receipts for the current user
export async function fetchReceipts() {
  // Get the current user first
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    throw new Error("User not authenticated");
  }

  const { data, error } = await supabase
    .from("receipts")
    .select("*")
    .eq("profile_id", user.id)
    .order("date", { ascending: false });

  if (error) throw error;
  return data as Receipt[];
}

// Fetch a single receipt by ID
export async function fetchReceiptById(receiptId: string) {
  const { data, error } = await supabase
    .from("receipts")
    .select("*")
    .eq("id", receiptId)
    .single();

  if (error) throw error;
  return data as Receipt;
}

// Fetch receipt items for a receipt
export async function fetchReceiptItems(receiptId: string) {
  const { data, error } = await supabase
    .from("receipt_items")
    .select("*")
    .eq("receipt_id", receiptId)
    .order("created_at", { ascending: true });

  if (error) throw error;
  return data as ReceiptItem[];
}

// Fetch a receipt with its items
export async function fetchReceiptWithItems(receiptId: string) {
  // First fetch the receipt
  const receipt = await fetchReceiptById(receiptId);

  // Then fetch the items
  const items = await fetchReceiptItems(receiptId);

  // Combine them
  return {
    ...receipt,
    items,
  } as Receipt;
}

// Create a new receipt
export async function createReceipt(
  receiptData: ReceiptFormData & { profile_id: string },
) {
  const { data, error } = await supabase
    .from("receipts")
    .insert([receiptData])
    .select()
    .single();

  if (error) throw error;
  return data as Receipt;
}

// Update a receipt
export async function updateReceipt(
  receiptId: string,
  receiptData: Partial<ReceiptFormData>,
) {
  const { data, error } = await supabase
    .from("receipts")
    .update(receiptData)
    .eq("id", receiptId)
    .select()
    .single();

  if (error) throw error;
  return data as Receipt;
}

// Delete a receipt
export async function deleteReceipt(receiptId: string) {
  const { error } = await supabase
    .from("receipts")
    .delete()
    .eq("id", receiptId);

  if (error) throw error;
  return true;
}

// Create a receipt item
export async function createReceiptItem(
  itemData: ReceiptItemFormData & { receipt_id: string },
) {
  const { data, error } = await supabase
    .from("receipt_items")
    .insert([itemData])
    .select()
    .single();

  if (error) throw error;
  return data as ReceiptItem;
}

// Update a receipt item
export async function updateReceiptItem(
  itemId: string,
  itemData: Partial<ReceiptItemFormData>,
) {
  const { data, error } = await supabase
    .from("receipt_items")
    .update(itemData)
    .eq("id", itemId)
    .select()
    .single();

  if (error) throw error;
  return data as ReceiptItem;
}

// Delete a receipt item
export async function deleteReceiptItem(itemId: string) {
  const { error } = await supabase
    .from("receipt_items")
    .delete()
    .eq("id", itemId);

  if (error) throw error;
  return true;
}

// Create a receipt with items in a transaction
export async function createReceiptWithItems(
  receiptData: ReceiptFormData & { profile_id: string },
  items: Omit<ReceiptItemFormData, "receipt_id">[],
) {
  // Start a transaction
  const { data: receipt, error: receiptError } = await supabase
    .from("receipts")
    .insert([receiptData])
    .select()
    .single();

  if (receiptError) throw receiptError;

  // If there are items, insert them
  if (items.length > 0) {
    const itemsWithReceiptId = items.map((item) => ({
      ...item,
      receipt_id: receipt.id,
    }));

    const { error: itemsError } = await supabase
      .from("receipt_items")
      .insert(itemsWithReceiptId);

    if (itemsError) throw itemsError;
  }

  // Fetch the receipt with items
  return fetchReceiptWithItems(receipt.id);
}

// Upload receipt image to storage
export async function uploadReceiptImage(
  file: File,
  profileId: string,
): Promise<string> {
  const fileExt = file.name.split(".").pop();
  const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
  const filePath = `receipts/${profileId}/${fileName}`;

  const { error } = await supabase.storage
    .from("receipts")
    .upload(filePath, file);

  if (error) throw error;

  const { data } = supabase.storage.from("receipts").getPublicUrl(filePath);

  return data.publicUrl;
}

// Convert data URL to File object
export function dataURLtoFile(dataUrl: string, filename: string): File {
  const arr = dataUrl.split(",");
  const mime = arr[0].match(/:(.*?);/)?.[1] || "image/jpeg";
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new File([u8arr], filename, { type: mime });
}
