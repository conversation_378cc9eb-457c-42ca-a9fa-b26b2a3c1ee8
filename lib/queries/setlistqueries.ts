// import { supabase as globalSupabaseInstance } from "../supabase"; // No longer use global instance for most functions
import type { SupabaseClient } from "@supabase/supabase-js";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import type {
  Setlist,
  SetlistItem,
  SetlistCreationData,
  SetlistUpdateData,
} from "@/lib/types/setlist"; // Import new types

// Re-export types for convenience
export type { Setlist, SetlistItem, SetlistCreationData, SetlistUpdateData };

// Server-side functions that require client parameter
export async function fetchSetlists(
  supabaseClient?: SupabaseClient,
): Promise<Setlist[]> {
  // If no client provided, create a client-side one
  const client = supabaseClient || createClientComponentClient();
  
  const { data, error } = await client
    .from("setlists")
    .select("*") // Selects all columns, list_items will be JSON
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching setlists:", error);
    throw error;
  }
  return data as Setlist[]; // Casting, ensure DB schema matches Setlist type
}

export async function fetchSetlistById(
  supabaseClient: SupabaseClient,
  id: number,
): Promise<Setlist | null> {
  const { data, error } = await supabaseClient
    .from("setlists")
    .select("*")
    .eq("id", id)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      // PostgREST error for "No rows found"
      return null;
    }
    console.error(`Error fetching setlist by ID ${id}:`, error);
    throw error;
  }
  return data as Setlist;
}

export async function fetchSetlistBySlug(
  supabaseClient: SupabaseClient,
  slug: string,
): Promise<Setlist | null> {
  const { data, error } = await supabaseClient
    .from("setlists")
    .select("*")
    .eq("slug", slug)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      // PostgREST error for "No rows found"
      return null;
    }
    console.error(`Error fetching setlist by slug ${slug}:`, error);
    throw error;
  }
  return data as Setlist;
}

export async function createSetlist(
  supabaseClient: SupabaseClient,
  setlistData: SetlistCreationData,
): Promise<Setlist> {
  // Ensure list_items is at least an empty array if not provided
  const payload = {
    ...setlistData,
    list_items: setlistData.list_items || [],
  };

  const { data, error } = await supabaseClient
    .from("setlists")
    .insert([payload]) // insert expects an array of objects
    .select()
    .single();

  if (error) {
    console.error("Error creating setlist:", error);
    throw error;
  }
  return data as Setlist;
}

export async function updateSetlist(
  supabaseClient: SupabaseClient,
  setlistId: number, // Use ID for updates
  setlistData: SetlistUpdateData,
): Promise<Setlist | null> {
  const { data, error } = await supabaseClient
    .from("setlists")
    .update(setlistData)
    .eq("id", setlistId)
    .select()
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      return null; // Setlist not found to update
    }
    console.error(`Error updating setlist ${setlistId}:`, error);
    throw error;
  }
  return data as Setlist;
}

export async function deleteSetlist(
  supabaseClient: SupabaseClient,
  setlistId: number,
): Promise<boolean> {
  const { error } = await supabaseClient
    .from("setlists")
    .delete()
    .eq("id", setlistId);

  if (error) {
    console.error(`Error deleting setlist ${setlistId}:`, error);
    throw error;
  }
  return true;
}

export async function fetchSetlistsByCreator(
  supabaseClient: SupabaseClient,
  creatorId: string,
): Promise<Setlist[]> {
  const { data, error } = await supabaseClient
    .from("setlists")
    .select("*")
    .eq("creator_profile_id", creatorId) // Changed from owner_id
    .order("created_at", { ascending: false });

  if (error) {
    console.error(`Error fetching setlists for creator ${creatorId}:`, error);
    throw error;
  }
  return data as Setlist[];
}

// Obsolete functions related to setlist_songs table are removed:
// fetchSetlistSongs, addSongToSetlist, updateSetlistSong, removeSetlistSong, fetchSetlistWithSongs

// The alias for fetchSetlistById is also removed as we now have a dedicated fetchSetlistById.
