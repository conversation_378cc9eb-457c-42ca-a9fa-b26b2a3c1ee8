/**
 * Admin Service
 *
 * This service provides specialized functionality for admin users,
 * including direct admin access and admin role verification.
 */

import { supabase } from "@/lib/supabase";
import { useAppStore } from "@/lib/store";
import { getAuthState } from "./auth-service";

// Types
export type AdminAccessResult = {
  success: boolean;
  error?: string;
  data?: any;
};

/**
 * Check if the current user has admin privileges
 * This performs a direct database check rather than relying on cached state
 */
export async function checkAdminStatus(userId: string): Promise<boolean> {
  try {
    // First check the user_roles table
    const { data: roleData, error: roleError } = await supabase
      .from("user_roles")
      .select("admin")
      .eq("id", userId)
      .single();

    if (!roleError && roleData?.admin === true) {
      return true;
    }

    // Admin status is only stored in user_roles table, not in profiles
    // If not found in user_roles, the user is not an admin

    return false;
  } catch (err) {
    console.error("Error checking admin status:", err);
    return false;
  }
}

/**
 * Get admin bypass information from localStorage
 * This is used by the middleware to allow admin access without a session
 */
export function getAdminBypass(): {
  id: string | null;
  timestamp: number | null;
} {
  if (typeof window === "undefined") {
    return { id: null, timestamp: null };
  }

  const id = localStorage.getItem("admin_bypass_id");
  const timestamp = localStorage.getItem("admin_bypass_timestamp");

  return {
    id,
    timestamp: timestamp ? parseInt(timestamp) : null,
  };
}

/**
 * Check if the admin bypass is valid (not expired)
 * Admin bypass is valid for 1 hour
 */
export function isAdminBypassValid(): boolean {
  const { id, timestamp } = getAdminBypass();

  if (!id || !timestamp) {
    return false;
  }

  // Check if the bypass is less than 1 hour old
  const now = Date.now();
  const expirationTime = 60 * 60 * 1000; // 1 hour in milliseconds

  return now - timestamp < expirationTime;
}

/**
 * Clear the admin bypass
 */
export function clearAdminBypass(): void {
  if (typeof window === "undefined") {
    return;
  }

  localStorage.removeItem("admin_bypass_id");
  localStorage.removeItem("admin_bypass_timestamp");
}

/**
 * Set up direct admin access
 * This is used by the admin-direct page to bypass normal authentication
 */
export async function setupDirectAdminAccess(
  userId: string,
): Promise<AdminAccessResult> {
  const store = useAppStore.getState();
  store.setIsLoading(true);

  try {
    // Check if user exists and has admin role
    const isAdmin = await checkAdminStatus(userId);

    if (!isAdmin) {
      throw new Error("This user does not have admin privileges");
    }

    // Get user profile
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();

    if (profileError) {
      throw new Error(`Profile not found: ${profileError.message}`);
    }

    // Get user roles
    const { data: roleData, error: roleError } = await supabase
      .from("user_roles")
      .select("*")
      .eq("id", userId)
      .single();

    if (roleError) {
      console.warn(`User roles not found: ${roleError.message}`);
    }

    // Get children if any
    const { data: childrenData } = await supabase
      .from("children")
      .select("*")
      .eq("user_id", userId);

    // Create a mock user object
    const mockUser = {
      id: userId,
      email: profileData.email || "<EMAIL>",
      app_metadata: { provider: "direct", is_admin: true },
      user_metadata: { name: profileData.display_name || "Admin User" },
      is_admin: true,
    };

    // Update store with direct data
    store.setCurrentAuthUser(mockUser as any);
    store.setCurrentProfile(profileData);
    store.setCoreUserData({
      profile: profileData,
      roles: roleData || {
        id: userId,
        admin: true,
        parent: false,
        teacher: false,
        student: false,
        performer: false,
        created_at: new Date().toISOString()
      },
      children: childrenData || [],
    });

    // Store admin bypass in localStorage for middleware
    if (typeof window !== "undefined") {
      localStorage.setItem("admin_bypass_id", userId);
      localStorage.setItem("admin_bypass_timestamp", Date.now().toString());
    }

    return {
      success: true,
      data: {
        user: mockUser,
        profile: profileData,
        roles: roleData || {
          id: userId,
          admin: true,
          parent: false,
          teacher: false,
          student: false,
          performer: false,
          created_at: new Date().toISOString()
        },
      },
    };
  } catch (err) {
    console.error("Direct admin access error:", err);
    return {
      success: false,
      error: err instanceof Error ? err.message : String(err),
    };
  } finally {
    store.setIsLoading(false);
  }
}

/**
 * Check if the current user is an admin
 * This uses both the store state and a direct database check
 */
export async function verifyAdminAccess(): Promise<AdminAccessResult> {
  const { userId, isAdmin: isAdminFromState } = getAuthState();

  // If we already know the user is an admin from the state, return success
  if (isAdminFromState) {
    return { success: true };
  }

  // If we don't have a userId, check for admin bypass
  if (!userId) {
    if (isAdminBypassValid()) {
      const bypassId = getAdminBypass().id;
      if (bypassId) {
        const isAdmin = await checkAdminStatus(bypassId);
        if (isAdmin) {
          return { success: true };
        }
      }
    }

    return {
      success: false,
      error: "Not authenticated as an admin user",
    };
  }

  // Perform a direct database check
  const isAdmin = await checkAdminStatus(userId);

  if (isAdmin) {
    return { success: true };
  }

  return {
    success: false,
    error: "User does not have admin privileges",
  };
}

/**
 * Get all users (admin only)
 */
export async function getAllUsers(): Promise<AdminAccessResult> {
  // Verify admin access first
  const adminCheck = await verifyAdminAccess();
  if (!adminCheck.success) {
    return adminCheck;
  }

  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      throw error;
    }

    return { success: true, data };
  } catch (err) {
    console.error("Error fetching users:", err);
    return {
      success: false,
      error: err instanceof Error ? err.message : String(err),
    };
  }
}

/**
 * Get all users with their roles (admin only)
 * This is used for the admin dashboard
 */
export async function fetchUsers(): Promise<AdminAccessResult> {
  // Verify admin access first
  const adminCheck = await verifyAdminAccess();
  if (!adminCheck.success) {
    return adminCheck;
  }

  try {
    const { data, error } = await supabase
      .from("profiles")
      .select(
        `
        *,
        user_roles (*)
      `,
      )
      .order("created_at", { ascending: false });

    if (error) {
      throw error;
    }

    return { success: true, data };
  } catch (err) {
    console.error("Error fetching users with roles:", err);
    return {
      success: false,
      error: err instanceof Error ? err.message : String(err),
    };
  }
}

/**
 * Remove a user (admin only)
 */
export async function removeUser(userId: string): Promise<AdminAccessResult> {
  // Verify admin access first
  const adminCheck = await verifyAdminAccess();
  if (!adminCheck.success) {
    return adminCheck;
  }

  try {
    // First remove user roles
    const { error: rolesError } = await supabase
      .from("user_roles")
      .delete()
      .eq("id", userId);

    if (rolesError) {
      console.warn("Error deleting user roles:", rolesError);
    }

    // Then remove profile
    const { error: profileError } = await supabase
      .from("profiles")
      .delete()
      .eq("id", userId);

    if (profileError) {
      throw profileError;
    }

    return { success: true };
  } catch (err) {
    console.error("Error removing user:", err);
    return {
      success: false,
      error: err instanceof Error ? err.message : String(err),
    };
  }
}

/**
 * Update user roles (admin only)
 */
export async function updateUserRoles(
  userId: string,
  roles: {
    admin?: boolean;
    parent?: boolean;
    teacher?: boolean;
    student?: boolean;
    performer?: boolean;
  },
): Promise<AdminAccessResult> {
  // Verify admin access first
  const adminCheck = await verifyAdminAccess();
  if (!adminCheck.success) {
    return adminCheck;
  }

  try {
    const { data, error } = await supabase
      .from("user_roles")
      .update(roles)
      .eq("id", userId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { success: true, data };
  } catch (err) {
    console.error("Error updating user roles:", err);
    return {
      success: false,
      error: err instanceof Error ? err.message : String(err),
    };
  }
}
