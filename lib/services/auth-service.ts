/**
 * Centralized Authentication Service
 *
 * This service provides a single source of truth for authentication state
 * and handles synchronization between Supabase sessions and the application store.
 *
 * It follows SOLID principles:
 * - Single Responsibility: Handles only authentication concerns
 * - Open/Closed: Extensible for different auth methods without modification
 * - Liskov Substitution: Consistent interface for auth operations
 * - Interface Segregation: Focused API for auth operations
 * - Dependency Inversion: Depends on abstractions, not concrete implementations
 */

import { supabase } from "@/lib/supabase";
import { useAppStore } from "@/lib/store";
import { fetchCoreUserData } from "@/lib/queries/userData";
import { User } from "@supabase/supabase-js";
import { Tables } from "@/lib/supabase-types";

// Types
export type AuthState = {
  isAuthenticated: boolean;
  isLoading: boolean;
  userId: string | null;
  user: User | null;
  profile: Tables<"profiles"> | null;
  roles: Tables<"user_roles"> | null;
  isAdmin: boolean;
  error: string | null;
};

export type AuthResult = {
  success: boolean;
  error?: string;
  data?: any;
};

/**
 * Get the current authentication state
 * This is a synchronous function that returns the current state from the store
 */
export function getAuthState(): AuthState {
  const store = useAppStore.getState();

  return {
    isAuthenticated: !!store.authUser,
    isLoading: store.isLoading,
    userId: store.authUser?.id || null,
    user: store.authUser,
    profile: store.currentProfile,
    roles: store.roles,
    isAdmin:
      store.roles?.admin === true ||
      (Array.isArray(store.roles) && store.roles.includes("admin")),
    error: store.error,
  };
}

/**
 * Initialize authentication state
 * This should be called on application startup
 */
export async function initializeAuth(): Promise<AuthResult> {
  const store = useAppStore.getState();
  store.setIsLoading(true);

  try {
    console.log("[AuthService] Initializing auth state");

    // Get current session
    const { data: sessionData, error: sessionError } =
      await supabase.auth.getSession();

    if (sessionError) {
      throw new Error(`Session error: ${sessionError.message}`);
    }

    if (!sessionData.session) {
      console.log("[AuthService] No session found");
      store.clearCoreUserData();
      store.setIsLoading(false);
      return { success: false, error: "No session found" };
    }

    const userId = sessionData.session.user.id;
    console.log(`[AuthService] Session found for user: ${userId}`);

    // Set auth user in store - convert Supabase User to expected format
    const authUser = {
      ...sessionData.session.user,
      email: sessionData.session.user.email || '',
      avatar_url: null,
      created_at: null,
      is_admin: null,
      name: null,
      privacy_accepted_at: null,
      privacy_version: null,
      terms_accepted_at: null,
      terms_version: null,
      updated_at: null,
    };
    store.setCurrentAuthUser(authUser);

    // Fetch user data
    try {
      const userData = await fetchCoreUserData(userId);

      // Update store with user data
      store.setCoreUserData(userData);

      console.log("[AuthService] User data loaded successfully");
      return { success: true, data: userData };
    } catch (err) {
      console.error("[AuthService] Error fetching user data:", err);
      return {
        success: false,
        error: err instanceof Error ? err.message : String(err),
      };
    }
  } catch (err) {
    console.error("[AuthService] Unexpected auth error:", err);
    store.resetStore();
    return {
      success: false,
      error: err instanceof Error ? err.message : String(err),
    };
  } finally {
    store.setIsLoading(false);
  }
}

/**
 * Refresh authentication state
 * This can be called to force a refresh of the auth state
 */
export async function refreshAuth(): Promise<AuthResult> {
  return await initializeAuth();
}

/**
 * Sign in with email and password
 */
export async function signInWithEmail(
  email: string,
  password: string,
): Promise<AuthResult> {
  const store = useAppStore.getState();
  store.setIsLoading(true);

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      throw new Error(`Sign in error: ${error.message}`);
    }

    if (!data.session) {
      throw new Error("Sign in successful but no session returned");
    }

    // Initialize auth state with the new session
    return await initializeAuth();
  } catch (err) {
    console.error("[AuthService] Sign in error:", err);
    store.setError(err instanceof Error ? err.message : String(err));
    return {
      success: false,
      error: err instanceof Error ? err.message : String(err),
    };
  } finally {
    store.setIsLoading(false);
  }
}

/**
 * Sign out the current user
 */
export async function signOut(): Promise<AuthResult> {
  const store = useAppStore.getState();
  store.setIsLoading(true);

  try {
    // Import clearAdminBypass dynamically to avoid circular imports
    const { clearAdminBypass } = await import("./admin-service");

    // Clear admin bypass tokens first
    clearAdminBypass();

    const { error } = await supabase.auth.signOut();

    if (error) {
      throw new Error(`Sign out error: ${error.message}`);
    }

    // Clear auth state
    store.resetStore();

    // Clear localStorage and sessionStorage
    if (typeof window !== "undefined") {
      localStorage.clear();
      sessionStorage.clear();
    }

    return { success: true };
  } catch (err) {
    console.error("[AuthService] Sign out error:", err);
    return {
      success: false,
      error: err instanceof Error ? err.message : String(err),
    };
  } finally {
    store.setIsLoading(false);
  }
}

/**
 * Check if the current user has admin privileges
 * This is a synchronous function that returns the current admin status
 */
export function isAdmin(): boolean {
  const { roles, user } = getAuthState();

  return (
    roles?.admin === true ||
    (Array.isArray(roles) && roles.includes("admin"))
  );
}

/**
 * Direct admin access with user ID
 * This is used for the admin-direct page to bypass normal authentication
 */
export async function directAdminAccess(userId: string): Promise<AuthResult> {
  const store = useAppStore.getState();
  store.setIsLoading(true);

  try {
    // Check if user exists and has admin role
    const { data: roleData, error: roleError } = await supabase
      .from("user_roles")
      .select("*")
      .eq("id", userId)
      .single();

    if (roleError) {
      throw new Error(`User role not found: ${roleError.message}`);
    }

    if (roleData.admin !== true) {
      throw new Error("This user does not have admin privileges");
    }

    // Get user profile
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();

    if (profileError) {
      throw new Error(`Profile not found: ${profileError.message}`);
    }

    // Get children if any
    const { data: childrenData } = await supabase
      .from("children")
      .select("*")
      .eq("user_id", userId);

    // Create a mock user object
    const mockUser = {
      id: userId,
      email: profileData.email || "<EMAIL>",
      app_metadata: { provider: "direct" },
      user_metadata: { name: profileData.display_name || "Admin User" },
    };

    // Update store with direct data
    store.setCurrentAuthUser(mockUser as any);
    store.setCurrentProfile(profileData);
    store.setCoreUserData({
      profile: profileData,
      roles: roleData,
      children: childrenData || [],
    });

    // Store admin bypass in localStorage for middleware
    if (typeof window !== "undefined") {
      localStorage.setItem("admin_bypass_id", userId);
      localStorage.setItem("admin_bypass_timestamp", Date.now().toString());
    }

    return {
      success: true,
      data: {
        user: mockUser,
        profile: profileData,
        roles: roleData,
      },
    };
  } catch (err) {
    console.error("[AuthService] Direct admin access error:", err);
    return {
      success: false,
      error: err instanceof Error ? err.message : String(err),
    };
  } finally {
    store.setIsLoading(false);
  }
}
