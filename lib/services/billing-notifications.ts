"use server";

import { createAdminClient } from "@/lib/supabase";
import type { PostgrestError } from "@supabase/supabase-js";

export type BillingNotificationType =
  | "payment_succeeded"
  | "payment_failed"
  | "subscription_created"
  | "subscription_updated"
  | "subscription_canceled"
  | "trial_ending"
  | "invoice_upcoming";

export interface BillingNotification {
  id: string;
  user_id: string;
  type: BillingNotificationType;
  title: string;
  message: string;
  data: Record<string, any>;
  read: boolean;
  created_at: string;
  updated_at: string;
}

export interface BillingNotificationResponse {
  success: boolean;
  message: string;
  data?: BillingNotification | BillingNotification[];
  error?: PostgrestError;
}

/**
 * Create a billing notification
 */
export async function createBillingNotification(
  userId: string,
  type: BillingNotificationType,
  title: string,
  message: string,
  data: Record<string, any> = {},
): Promise<BillingNotificationResponse> {
  try {
    const supabase = createAdminClient();

    const notificationData = {
      user_id: userId,
      category: 'critical', // Billing notifications are critical
      type,
      title,
      message,
      metadata: data, // Store additional data in metadata field
      read: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const { data: notification, error } = await supabase
      .from("notifications")
      .insert([notificationData])
      .select()
      .single();

    if (error) throw error;

    return {
      success: true,
      message: "Billing notification created successfully",
      data: notification as BillingNotification,
    };
  } catch (error: any) {
    console.error("Error creating billing notification:", error);
    return {
      success: false,
      message: "Failed to create billing notification",
      error: error as PostgrestError,
    };
  }
}

/**
 * Get billing notifications for a user
 */
export async function getBillingNotifications(
  userId: string,
  limit: number = 50,
): Promise<BillingNotificationResponse> {
  try {
    const supabase = createAdminClient();

    const { data, error } = await supabase
      .from("notifications")
      .select("*")
      .eq("user_id", userId)
      .eq("category", "critical") // Filter for billing notifications
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) throw error;

    return {
      success: true,
      message: "Billing notifications retrieved successfully",
      data: data as BillingNotification[],
    };
  } catch (error: any) {
    console.error("Error fetching billing notifications:", error);
    return {
      success: false,
      message: "Failed to retrieve billing notifications",
      error: error as PostgrestError,
    };
  }
}

/**
 * Mark billing notification as read
 */
export async function markBillingNotificationAsRead(
  notificationId: string,
): Promise<BillingNotificationResponse> {
  try {
    const supabase = createAdminClient();

    const { data, error } = await supabase
      .from("notifications")
      .update({
        read: true,
        updated_at: new Date().toISOString(),
      })
      .eq("id", notificationId)
      .select()
      .single();

    if (error) throw error;

    return {
      success: true,
      message: "Notification marked as read",
      data: data as BillingNotification,
    };
  } catch (error: any) {
    console.error("Error marking notification as read:", error);
    return {
      success: false,
      message: "Failed to mark notification as read",
      error: error as PostgrestError,
    };
  }
}

/**
 * Get unread billing notifications count
 */
export async function getUnreadBillingNotificationsCount(
  userId: string,
): Promise<{
  success: boolean;
  message: string;
  count?: number;
  error?: PostgrestError;
}> {
  try {
    const supabase = createAdminClient();

    const { count, error } = await supabase
      .from("notifications")
      .select("*", { count: "exact", head: true })
      .eq("user_id", userId)
      .eq("category", "critical") // Filter for billing notifications
      .eq("read", false);

    if (error) throw error;

    return {
      success: true,
      message: "Unread count retrieved successfully",
      count: count || 0,
    };
  } catch (error: any) {
    console.error("Error getting unread notifications count:", error);
    return {
      success: false,
      message: "Failed to get unread notifications count",
      error: error as PostgrestError,
    };
  }
}

/**
 * Helper functions for common billing notifications
 */

export async function notifyPaymentSucceeded(
  userId: string,
  amount: number,
  currency: string,
  invoiceUrl?: string,
) {
  return createBillingNotification(
    userId,
    "payment_succeeded",
    "Payment Successful",
    `Your payment of ${formatCurrency(amount)} ${currency.toUpperCase()} has been processed successfully.`,
    { amount, currency, invoiceUrl },
  );
}

export async function notifyPaymentFailed(
  userId: string,
  amount: number,
  currency: string,
  reason?: string,
) {
  return createBillingNotification(
    userId,
    "payment_failed",
    "Payment Failed",
    `Your payment of ${formatCurrency(amount)} ${currency.toUpperCase()} could not be processed. ${reason ? `Reason: ${reason}` : "Please update your payment method."}`,
    { amount, currency, reason },
  );
}

export async function notifySubscriptionCreated(
  userId: string,
  planName: string,
  amount: number,
  currency: string,
) {
  return createBillingNotification(
    userId,
    "subscription_created",
    "Subscription Activated",
    `Welcome to ${planName}! Your subscription for ${formatCurrency(amount)} ${currency.toUpperCase()}/month is now active.`,
    { planName, amount, currency },
  );
}

export async function notifySubscriptionCanceled(
  userId: string,
  planName: string,
  endDate: string,
) {
  return createBillingNotification(
    userId,
    "subscription_canceled",
    "Subscription Canceled",
    `Your ${planName} subscription has been canceled and will end on ${new Date(endDate).toLocaleDateString()}.`,
    { planName, endDate },
  );
}

export async function notifyTrialEnding(
  userId: string,
  planName: string,
  daysLeft: number,
) {
  return createBillingNotification(
    userId,
    "trial_ending",
    "Trial Ending Soon",
    `Your ${planName} trial will end in ${daysLeft} day${daysLeft === 1 ? "" : "s"}. Add a payment method to continue your subscription.`,
    { planName, daysLeft },
  );
}

export async function notifyUpcomingInvoice(
  userId: string,
  amount: number,
  currency: string,
  dueDate: string,
) {
  return createBillingNotification(
    userId,
    "invoice_upcoming",
    "Upcoming Payment",
    `Your next payment of ${formatCurrency(amount)} ${currency.toUpperCase()} will be charged on ${new Date(dueDate).toLocaleDateString()}.`,
    { amount, currency, dueDate },
  );
}

// Helper function to format currency
function formatCurrency(amountInCents: number): string {
  return `$${(amountInCents / 100).toFixed(2)}`;
}
