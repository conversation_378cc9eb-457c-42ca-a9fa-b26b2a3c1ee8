import { STRIPE_PLANS } from "@/lib/config/stripe-config";

export type OrganizationType = 
  | "enterprise-band" 
  | "enterprise-music-school" 
  | "enterprise-venue" 
  | "enterprise-agent";

export interface BandRequirements {
  bandMembers: number;
  supportStaff: number;
  hasManager: boolean;
  managerCount: number;
}

export interface MusicSchoolRequirements {
  teachers: number;
  adminStaff: number;
  lessonSpaces: number;
}

export interface VenueRequirements {
  venueType: "performance" | "rehearsal" | "both";
  performanceSpaces: number;
  rehearsalSpaces: number;
}

export interface AgentRequirements {
  representedBands: number;
  managedArtists: number;
  managedRehearsalSpaces: number;
  representedPerformanceSpaces: number;
}

export interface EnterpriseRequirements {
  organizationType: OrganizationType;
  organizationName?: string;
  band?: BandRequirements;
  musicSchool?: MusicSchoolRequirements;
  venue?: VenueRequirements;
  agent?: AgentRequirements;
  step: number;
}

export class EnterprisePricingService {
  /**
   * Calculate the estimated monthly cost for enterprise requirements
   */
  static calculateEstimatedCost(requirements: EnterpriseRequirements): number {
    const { organizationType, step } = requirements;
    
    if (!organizationType) return 0;
    
    const plan = STRIPE_PLANS[organizationType];
    if (!plan) return 0;
    
    const basePriceAmount = plan.prices.monthly.amount; // Already in cents
    let addOns = 0;

    switch (organizationType) {
      case "enterprise-band":
        addOns = this.calculateBandAddOns(requirements.band, step);
        break;
      case "enterprise-music-school":
        addOns = this.calculateMusicSchoolAddOns(requirements.musicSchool, step);
        break;
      case "enterprise-venue":
        addOns = this.calculateVenueAddOns(requirements.venue, step);
        break;
      case "enterprise-agent":
        addOns = this.calculateAgentAddOns(requirements.agent, step);
        break;
    }

    return basePriceAmount + addOns;
  }

  /**
   * Calculate total users for the organization
   */
  static calculateTotalUsers(requirements: EnterpriseRequirements): number {
    const { organizationType } = requirements;

    switch (organizationType) {
      case "enterprise-band":
        return this.calculateBandUsers(requirements.band);
      case "enterprise-music-school":
        return this.calculateMusicSchoolUsers(requirements.musicSchool);
      case "enterprise-venue":
        return this.calculateVenueUsers(requirements.venue);
      case "enterprise-agent":
        return this.calculateAgentUsers(requirements.agent);
      default:
        return 1;
    }
  }

  private static calculateBandAddOns(band?: BandRequirements, step: number = 1): number {
    if (!band) return 0;
    
    let addOns = 0;
    
    // First band member is included in base, additional are $5.00 each
    addOns += Math.max(0, band.bandMembers - 1) * 500; // 500 cents
    
    if (step >= 3) {
      // Support staff only added from step 3
      addOns += band.supportStaff * 400; // 400 cents
    }
    
    if (step >= 4 && band.hasManager) {
      // Manager only added from step 4 if selected
      addOns += band.managerCount * 1000; // 1000 cents
    }
    
    return addOns;
  }

  private static calculateMusicSchoolAddOns(musicSchool?: MusicSchoolRequirements, step: number = 1): number {
    if (!musicSchool) return 0;
    
    let addOns = 0;
    
    // First teacher is included, additional are $5.00 each
    addOns += Math.max(0, musicSchool.teachers - 1) * 500; // 500 cents
    
    if (step >= 3) {
      // Admin staff only added from step 3
      addOns += musicSchool.adminStaff * 400; // 400 cents
    }
    
    if (step >= 4) {
      // Lesson spaces only added from step 4
      addOns += Math.max(0, musicSchool.lessonSpaces - 1) * 1000; // 1000 cents for additional spaces
    }
    
    return addOns;
  }

  private static calculateVenueAddOns(venue?: VenueRequirements, step: number = 1): number {
    if (!venue || step < 3) return 0;
    
    let addOns = 0;
    
    if (venue.venueType === "performance" || venue.venueType === "both") {
      addOns += venue.performanceSpaces * 1000; // 1000 cents per performance space
    }
    
    if (venue.venueType === "rehearsal" || venue.venueType === "both") {
      addOns += venue.rehearsalSpaces * 1000; // 1000 cents per rehearsal space
    }
    
    return addOns;
  }

  private static calculateAgentAddOns(agent?: AgentRequirements, step: number = 1): number {
    if (!agent) return 0;
    
    let addOns = 0;
    
    // Assuming first represented band might be included in base price
    addOns += Math.max(0, agent.representedBands - 1) * 300; // 300 cents
    addOns += agent.managedArtists * 300; // 300 cents
    
    if (step >= 3) {
      // Space management details from step 3
      addOns += agent.managedRehearsalSpaces * 1000; // 1000 cents
      addOns += agent.representedPerformanceSpaces * 1000; // 1000 cents
    }
    
    return addOns;
  }

  private static calculateBandUsers(band?: BandRequirements): number {
    if (!band) return 1;
    
    return band.bandMembers + 
           band.supportStaff + 
           (band.hasManager ? band.managerCount : 0);
  }

  private static calculateMusicSchoolUsers(musicSchool?: MusicSchoolRequirements): number {
    if (!musicSchool) return 1;
    
    return musicSchool.teachers + musicSchool.adminStaff;
  }

  private static calculateVenueUsers(venue?: VenueRequirements): number {
    if (!venue) return 1;
    
    const spaceCount = 
      (venue.venueType === "performance" || venue.venueType === "both" ? venue.performanceSpaces : 0) +
      (venue.venueType === "rehearsal" || venue.venueType === "both" ? venue.rehearsalSpaces : 0);
    
    return Math.max(1, spaceCount);
  }

  private static calculateAgentUsers(agent?: AgentRequirements): number {
    if (!agent) return 1;
    
    return agent.representedBands + agent.managedArtists;
  }

  /**
   * Format currency amount (in cents) to display string
   */
  static formatCurrency(amountInCents: number): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amountInCents / 100);
  }
}
