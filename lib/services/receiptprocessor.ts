import { v4 as uuidv4 } from "uuid";
import type { Receipt, ReceiptItem, ParsedReceipt } from "../types/receipt";
import {
  uploadReceiptImage,
  createReceiptWithItems,
} from "../queries/receiptQueries";

// Convert data URL to Blob
function dataURLToBlob(dataURL: string): Blob {
  const arr = dataURL.split(",");
  const mime = arr[0].match(/:(.*?);/)?.[1] || "image/jpeg";
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new Blob([u8arr], { type: mime });
}

// Create a mock receipt for testing or fallback
export function createMockReceipt(imageData?: string): Receipt {
  const receiptId = uuidv4();
  const now = new Date().toISOString();

  return {
    id: receiptId,
    profile_id: "", // Will be filled in by the caller
    merchant: "Sample Merchant",
    date: now,
    total: 99.99,
    category: "other",
    image_url: imageData,
    created_at: now,
    updated_at: now,
    items: [
      {
        id: uuidv4(),
        receipt_id: receiptId,
        description: "Sample Item 1",
        quantity: 1,
        price: 49.99,
        created_at: now,
        updated_at: now,
      },
      {
        id: uuidv4(),
        receipt_id: receiptId,
        description: "Sample Item 2",
        quantity: 2,
        price: 24.99,
        created_at: now,
        updated_at: now,
      },
    ],
  };
}

// Convert Veryfi parsed receipt to our Receipt format
function convertParsedReceiptToReceipt(
  parsedReceipt: ParsedReceipt,
  imageUrl: string | null,
  profileId: string,
): {
  receiptData: Receipt;
  receiptItems: ReceiptItem[];
} {
  // Create receipt data
  const receiptData: Receipt = {
    id: uuidv4(),
    profile_id: profileId,
    merchant: parsedReceipt.vendor || "Unknown Merchant",
    date: parsedReceipt.dateTime || new Date().toISOString(),
    total: Number(parsedReceipt.totalAmount) || 0,
    category: "other", // Default category
    image_url: imageUrl || undefined,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),

    // Additional fields from parsed receipt
    subtotal: Number(parsedReceipt.subtotal) || 0,
    tax: Number(parsedReceipt.tax) || 0,
    amount_paid:
      Number(parsedReceipt.amountPaid) ||
      Number(parsedReceipt.totalAmount) ||
      0,
    amount_outstanding: Number(parsedReceipt.amountOutstanding) || 0,
    vendor_address: parsedReceipt.vendorAddress || "",
    vendor_phone: parsedReceipt.vendorPhone || "",
    vendor_url: parsedReceipt.vendorUrl || "",
    abn: parsedReceipt.abn || "",
    server: parsedReceipt.server || "",
    invoice_number: parsedReceipt.invoiceNumber || "",
  };

  // Create receipt items
  const receiptItems: ReceiptItem[] = (parsedReceipt.lineItems || []).map(
    (item) => ({
      id: uuidv4(),
      receipt_id: receiptData.id,
      description: item.description || "Unknown Item",
      quantity: item.qty || 1,
      price: item.cost || 0,
      sku: item.sku,
      discount: item.discount,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }),
  );

  return { receiptData, receiptItems };
}

// Process a receipt image with Veryfi API
export async function processReceipt(
  imageData: string,
  profileId: string,
): Promise<Receipt> {
  try {
    console.log("Starting receipt processing...");

    // Upload image to Supabase storage
    const file = new File([dataURLToBlob(imageData)], "receipt.jpg", {
      type: "image/jpeg",
    });
    const imageUrl = await uploadReceiptImage(file, profileId);

    // Process the receipt with Veryfi API
    try {
      console.log("Processing receipt with Veryfi API...");

      // Create a FormData object to send to our API
      const formData = new FormData();
      formData.append("file", file, "receipt.jpg");

      // Create an AbortController to handle timeouts
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      // Call our API endpoint
      const response = await fetch("/api/receipts/process", {
        method: "POST",
        body: formData,
        signal: controller.signal,
      });

      // Clear the timeout
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`API returned status ${response.status}`);
      }

      const apiResponse = await response.json();

      if (apiResponse.error) {
        console.error("API returned an error:", apiResponse.error);
        throw new Error(apiResponse.error);
      }

      if (!apiResponse.parsedReceipt) {
        console.error("No parsedReceipt in response");
        throw new Error("No parsedReceipt in response");
      }

      console.log("Successfully parsed receipt");

      // Convert the parsed receipt to our format
      const { receiptData, receiptItems } = convertParsedReceiptToReceipt(
        apiResponse.parsedReceipt,
        imageUrl,
        profileId,
      );

      // Save the receipt and items to the database
      const receipt = await createReceiptWithItems(receiptData, receiptItems);

      return receipt;
    } catch (processingError) {
      console.error("Error processing receipt:", processingError);

      // Create a mock receipt as fallback
      const mockReceipt = createMockReceipt(imageUrl);
      mockReceipt.profile_id = profileId;

      // Save the mock receipt to the database
      const receipt = await createReceiptWithItems(
        mockReceipt,
        mockReceipt.items || [],
      );

      return receipt;
    }
  } catch (error: any) {
    console.error("Error in processReceipt:", error);
    throw error;
  }
}
