import { create } from "zustand";
import type { Profile } from "./queries/profileQueries";
import type { Tables } from "@/lib/supabase-types";
import type { UserDataPayload } from "./queries/userData";
import type { Subscription } from "./services/subscription-service";
import {} from // fetchProfiles, // Components should fetch directly or use actions
// fetchProfileById,
// createProfile,
// updateProfile,
"./queries/profileQueries";
import {} from // fetchSongs, // Components should fetch directly or use actions
// fetchSongById,
// createSong, // Use server actions
// updateSong, // Use server actions
"./queries/songQueries";
import {} from // fetchSetlists, // Components should fetch directly or use actions
// fetchSetlistById,
// createSetlist, // Use server actions
// updateSetlist, // Use server actions
"./queries/setlistQueries";
import {
  fetchCurricula, // Example: Keep if genuinely global and frequently accessed
  fetchCurriculumById,
  createCurriculum,
  updateCurriculum,
  fetchLessonPlansByCurriculumId,
} from "./queries/curriculumQueries";
import {
  fetchSongVariants,
  createSongVariant,
  updateSongVariant,
} from "./queries/songVariantQueries";
import type { Song } from "./queries/songQueries";
import type { Setlist } from "@/lib/types/setlist"; // Corrected path
import type { Curriculum, LessonPlan } from "./queries/curriculumQueries";
import type { SongVariant } from "./queries/songVariantQueries";

export type UserRoleType =
  | "performer"
  | "student"
  | "teacher"
  | "parent"
  | "admin";
export type UserRole = UserRoleType | UserRoleType[];

type AppState = {
  // User state
  authUser: Tables<"users"> | null;
  setCurrentAuthUser: (user: Tables<"users"> | null) => void;

  currentProfile: Tables<"profiles"> | null;
  setCurrentProfile: (profile: Tables<"profiles"> | null) => void;

  // Alias for currentProfile to match component expectations
  currentUser: Tables<"profiles"> | null;

  children: Tables<"children">[] | null;
  roles: Tables<"user_roles"> | null;
  setRoles: (roles: Tables<"user_roles"> | null) => void;

  // Subscription state
  subscription: Subscription | null;
  setSubscription: (subscription: Subscription | null) => void;
  subscriptionLoading: boolean;
  setSubscriptionLoading: (loading: boolean) => void;

  // Add function to update children
  addChildToStore: (child: Tables<"children">) => void;
  addSong: (song: Song) => void;

  setCoreUserData: (data: UserDataPayload) => void;
  clearCoreUserData: () => void;

  // UI state
  isLoading: boolean;
  setIsLoading: (isLoading: boolean) => void;
  error: string | null;
  setError: (error: string | null) => void;
  darkMode: boolean;
  toggleDarkMode: () => void;

  // Data state
  profiles: Profile[]; // Consider if this needs to be in global store
  songs: Song[];
  setSongs: (songs: Song[]) => void;
  setlists: Setlist[];
  curricula: Curriculum[]; // Example: Keep if genuinely global

  // Data fetching functions (Commented out - prefer direct use or actions)
  // fetchProfiles: () => Promise<Profile[]>;
  // fetchSongs: () => Promise<Song[]>;
  // fetchSetlists: () => Promise<Setlist[]>;
  // fetchCurricula: () => Promise<Curriculum[]>; // Example: Keep if used

  // Single item fetching (Commented out)
  // fetchProfileById: (profileId: string) => Promise<Profile>;
  // fetchSongById: (songId: number) => Promise<Song>;
  // fetchSetlistById: (setlistId: number) => Promise<Setlist>;
  // fetchCurriculumById: (curriculumId: string) => Promise<Curriculum>; // Example: Keep if used

  // Creation functions (Commented out - prefer server actions)
  // createProfile: (profileData: Omit<Profile, "id" | "created_at" | "updated_at">) => Promise<Profile>;
  // createSong: (songData: Omit<Song, "id" | "created_at" | "updated_at">) => Promise<Song>;
  // createSetlist: (setlistData: Omit<Setlist, "id" | "created_at" | "updated_at">) => Promise<Setlist>;
  // createCurriculum: (curriculumData: Omit<Curriculum, "id" | "created_at" | "updated_at">) => Promise<Curriculum>; // Example: Keep

  // Update functions (Commented out - prefer server actions)
  // updateProfile: (profileId: string, profileData: Partial<Omit<Profile, "id" | "created_at" | "updated_at">>) => Promise<Profile>;
  // updateSong: (songId: number, songData: Partial<Omit<Song, "id" | "created_at" | "updated_at">>) => Promise<Song>;
  // updateSetlist: (setlistId: number, setlistData: Partial<Omit<Setlist, "id" | "created_at" | "updated_at">>) => Promise<Setlist>;
  // updateCurriculum: (curriculumId: string, curriculumData: Partial<Omit<Curriculum, "id" | "created_at" | "updated_at">>) => Promise<Curriculum>; // Example: Keep

  // Related data fetching
  fetchLessonPlansByCurriculumId: (
    curriculumId: number,
  ) => Promise<LessonPlan[]>; // Example: Keep
  fetchSongVariants: (songId: number) => Promise<SongVariant[]>; // songId is number

  // Song variant functions
  createSongVariant: (
    variantData: Omit<SongVariant, "id" | "created_at" | "updated_at">,
  ) => Promise<SongVariant>;
  updateSongVariant: (
    variantId: number,
    variantData: Partial<Omit<SongVariant, "id" | "created_at" | "updated_at">>,
  ) => Promise<SongVariant>; // variantId is number

  resetStore: () => void;
};

const initialCoreDataState = {
  authUser: null as Tables<"users"> | null,
  currentProfile: null as Tables<"profiles"> | null,
  currentUser: null as Tables<"profiles"> | null,
  children: null as Tables<"children">[] | null,
  roles: null as Tables<"user_roles"> | null,
  isLoading: true,
  error: null as string | null,
};

export const useAppStore = create<AppState>((set, get) => ({
  // User state
  ...initialCoreDataState,
  setCurrentAuthUser: (user) => {
    console.log(
      "[AppStore] Setting authUser:",
      user ? { id: user.id, email: user.email } : null,
    );
    set({ authUser: user });
  },
  currentProfile: null,
  setCurrentProfile: (profile) => {
    console.log(
      "[AppStore] Setting currentProfile:",
      profile ? { id: profile.id, name: profile.display_name } : null,
    );
    set({ currentProfile: profile, currentUser: profile }); // Also set currentUser alias
    if (!profile && get().authUser) {
      console.warn(
        "[AppStore] currentProfile is null but authUser exists. This might indicate an issue post-login if profile wasn't fetched/found.",
      );
    }
  },

  // Alias for currentProfile to match component expectations
  currentUser: null,
  children: null,
  roles: null,
  setRoles: (roles) => {
    console.log("[AppStore] Setting roles:", roles);
    set({ roles });
  },

  // Subscription state
  subscription: null,
  setSubscription: (subscription) => {
    console.log(
      "[AppStore] Setting subscription:",
      subscription ? { id: subscription.id, plan_name: subscription.plan_name } : null,
    );
    set({ subscription });
  },
  subscriptionLoading: true,
  setSubscriptionLoading: (loading) => {
    console.log("[AppStore] Setting subscriptionLoading:", loading);
    set({ subscriptionLoading: loading });
  },

  // Add function to update children
  addChildToStore: (child) => {
    console.log(
      "[AppStore] Adding child:",
      child ? { id: child.id, name: child.name } : null,
    );
    set((state) => ({
      children: state.children ? [...state.children, child] : [child],
    }));
  },
  addSong: (song) => {
    console.log(`[AppStore] Adding song: ${song.title}`);
    set((state) => ({ songs: [...state.songs, song] }));
  },

  setCoreUserData: (data) => {
    console.log("[AppStore] Setting core user data:", {
      profile: data.profile
        ? { id: data.profile.id, name: data.profile.display_name }
        : null,
      childrenCount: data.children?.length,
      childrenData: data.children
        ?.map((c) => ({ id: c.id, name: c.name, username: c.username }))
        .slice(0, 3), // Limit logging
      roles: data.roles,
    });
    set((state) => {
      const newState = {
        ...state,
        currentProfile: data.profile,
        currentUser: data.profile, // Keep currentUser in sync
        children: data.children,
        roles: data.roles,
        isLoading: false,
        error: null,
      };
      console.log("[AppStore] New state after setCoreUserData:", {
        currentProfileName: newState.currentProfile?.display_name,
        childrenCount: newState.children?.length,
      });
      return newState;
    });
    if (!data.profile && get().authUser) {
      console.warn(
        "[AppStore] Core user data set, but profile is null while authUser exists. Check fetchCoreUserData logic and RLS.",
      );
    }
  },
  clearCoreUserData: () => {
    console.log("[AppStore] Clearing core user data.");
    set((state) => ({
      ...state,
      ...initialCoreDataState,
      songs: [],
      setlists: [],
      curricula: [],
      profiles: [],
    }));
  },

  // UI state
  isLoading: true,
  setIsLoading: (isLoading) => set({ isLoading }),
  error: null,
  setError: (error) => {
    console.log(
      error
        ? `[AppStore] Setting error: ${error}`
        : "[AppStore] Clearing error.",
    );
    set({ error, isLoading: false });
  },
  darkMode: false,
  toggleDarkMode: () => set((state) => ({ darkMode: !state.darkMode })),

  // Data state
  profiles: [], // Consider if this needs to be in global store
  songs: [],
  setSongs: (songs) => {
    console.log(`[AppStore] Setting songs. Count: ${songs.length}`);
    set({ songs });
  },
  setlists: [],
  curricula: [], // Example: Keep if genuinely global and frequently used

  // Data fetching/CRUD functions (Commented out - prefer direct use in components/actions)

  // Example: Keep these if they are genuinely used by multiple components via the store
  fetchCurricula: async () => {
    // set({ isLoading: true });
    try {
      // const data = await fetchCurricula(); // Needs Supabase client
      // set({ curricula: data, isLoading: false });
      // return data;
      console.warn(
        "[AppStore] fetchCurricula called but not implemented with Supabase client. Components should fetch directly.",
      );
      return []; // Placeholder
    } catch (error: any) {
      // set({ error: error.message, isLoading: false });
      throw error;
    }
  },
  fetchCurriculumById: async (curriculumId: string) => {
    try {
      // const data = await fetchCurriculumById(curriculumId); // Needs Supabase client
      // return data;
      console.warn(
        "[AppStore] fetchCurriculumById called but not implemented with Supabase client. Components should fetch directly.",
      );
      return {} as Curriculum; // Placeholder
    } catch (error: any) {
      throw error;
    }
  },
  createCurriculum: async (curriculumData: any) => {
    try {
      // const newCurriculum = await createCurriculum(curriculumData); // Needs Supabase client & auth
      // set((state) => ({ curricula: [...state.curricula, newCurriculum] }));
      // return newCurriculum;
      console.warn(
        "[AppStore] createCurriculum called but not implemented with Supabase client. Components should use actions.",
      );
      return {} as Curriculum; // Placeholder
    } catch (error: any) {
      throw error;
    }
  },
  updateCurriculum: async (curriculumId: string, curriculumData: any) => {
    try {
      // const updatedCurriculum = await updateCurriculum(curriculumId, curriculumData); // Needs Supabase client & auth
      // set((state) => ({
      //   curricula: state.curricula.map((c) =>
      //     c.id === curriculumId ? updatedCurriculum : c
      //   ),
      // }));
      // return updatedCurriculum;
      console.warn(
        "[AppStore] updateCurriculum called but not implemented with Supabase client. Components should use actions.",
      );
      return {} as Curriculum; // Placeholder
    } catch (error: any) {
      throw error;
    }
  },
  fetchLessonPlansByCurriculumId: async (curriculumId: number) => {
    try {
      const data = await fetchLessonPlansByCurriculumId(curriculumId); // This one might be okay if it doesn't need client
      return data;
    } catch (error: any) {
      throw error;
    }
  },
  fetchSongVariants: async (songId: number) => {
    try {
      const data = await fetchSongVariants(songId); // Needs Supabase client
      return data;
    } catch (error: any) {
      throw error;
    }
  },
  createSongVariant: async (variantData: any) => {
    try {
      const newVariant = await createSongVariant(variantData); // Needs Supabase client & auth
      return newVariant;
    } catch (error: any) {
      throw error;
    }
  },
  updateSongVariant: async (variantId: number, variantData: any) => {
    try {
      const updatedVariant = await updateSongVariant(variantId, variantData); // Needs Supabase client & auth
      return updatedVariant;
    } catch (error: any) {
      throw error;
    }
  },

  resetStore: () => {
    console.log("[AppStore] Resetting store to initial state.");
    set((state) => ({
      ...initialCoreDataState,
      darkMode: state.darkMode,
      profiles: [],
      songs: [],
      setlists: [],
      curricula: [],
      subscription: null,
      subscriptionLoading: true,
    }));
  },
}));

// Example of how a component might use the store for fetching (though direct fetching or server actions are often preferred now):
// const { fetchProfiles, profiles, isLoading, error } = useAppStore();
// useEffect(() => {
//   fetchProfiles();
// }, [fetchProfiles]);

// It's often better for components to manage their own data fetching logic
// or use React Query / SWR, or Server Components / Server Actions for data.
// The store is great for global UI state, authenticated user state, and perhaps very globally shared, small datasets.
