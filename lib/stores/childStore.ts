import { create } from "zustand";
import { supabase } from "../supabase";
import { Profile } from "../queries/profileQueries";

interface ChildState {
  children: Profile[];
  selectedChild: Profile | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchChildren: (parentId: string) => Promise<void>;
  selectChild: (child: Profile) => void;
  addChild: (childData: {
    name: string;
    dob: string;
    username?: string;
    bio?: string;
    avatar?: string;
    parentId: string;
  }) => Promise<{ success: boolean; childId?: string; error?: string }>;
}

export const useChildStore = create<ChildState>((set, get) => ({
  children: [],
  selectedChild: null,
  isLoading: false,
  error: null,

  fetchChildren: async (parentId: string) => {
    try {
      set({ isLoading: true, error: null });
      console.log(`Fetching children for parent ${parentId}`);

      // First try with parent_id field
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("parent_id", parentId)
        .order("created_at", { ascending: false });

      if (error) {
        console.error(`Error fetching children:`, error);
        set({ error: error.message, isLoading: false });
        return;
      }

      // Convert to Profile type
      const profiles = data as Profile[];
      console.log(`Found ${profiles.length} children`);

      // If no profiles found with parent_id, try the parent_child table
      if (profiles.length === 0) {
        try {
          console.log(`Trying children table for parent ${parentId}`);
          // Check the children table directly
          const { data: childrenData, error: childrenError } = await supabase
            .from("children")
            .select("id")
            .eq("user_id", parentId);

          if (childrenError) {
            console.error(`Error checking children table:`, childrenError);
          } else if (childrenData && childrenData.length > 0) {
            console.log(
              `Found ${childrenData.length} children in children table`,
            );

            // Get the child IDs
            const childIds = childrenData.map((child) => child.id);

            // Fetch the child profiles
            const { data: childProfiles, error: childError } = await supabase
              .from("profiles")
              .select("*")
              .in("id", childIds)
              .order("created_at", { ascending: false });

            if (childError) {
              console.error(
                `Error fetching child profiles by IDs:`,
                childError,
              );
            } else if (childProfiles && childProfiles.length > 0) {
              console.log(
                `Found ${childProfiles.length} child profiles via children table`,
              );
              // Add these profiles to the result
              profiles.push(...(childProfiles as Profile[]));
            }
          }
        } catch (e) {
          console.warn(`Error checking children table:`, e);
          // Continue with the profiles we have (if any)
        }
      }

      // For each profile, fetch and add roles
      for (const profile of profiles) {
        try {
          const { data: roleData, error: roleError } = await supabase
            .from("user_roles")
            .select("*")
            .eq("id", profile.id)
            .single();

          if (roleError) {
            console.warn(
              `Error fetching roles for child ${profile.id}:`,
              roleError,
            );
            profile.roles = [];
          } else if (roleData) {
            profile.roles = [];
            if (roleData.student) profile.roles.push("student");
            if (roleData.teacher) profile.roles.push("teacher");
            if (roleData.performer) profile.roles.push("performer");
            if (roleData.parent) profile.roles.push("parent");
          }
        } catch (e) {
          console.error(`Error processing roles for child ${profile.id}:`, e);
          profile.roles = [];
        }
      }

      set({
        children: profiles,
        isLoading: false,
        selectedChild: profiles.length > 0 ? profiles[0] : null,
      });
    } catch (error: any) {
      console.error("Error fetching children:", error);
      set({ error: error.message, isLoading: false });
    }
  },

  selectChild: (child: Profile) => {
    set({ selectedChild: child });
  },

  addChild: async (childData) => {
    try {
      set({ isLoading: true, error: null });
      console.log("Adding child:", childData);

      // First check if we have a valid session
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      if (sessionError || !session) {
        console.error("Session error:", sessionError || "No session found");
        set({
          isLoading: false,
          error: "Authentication failed - please log in again",
        });
        return {
          success: false,
          error: "Authentication failed - please log in again",
        };
      }

      // Call the add_child RPC function
      const response = await fetch("/api/parent/add-child", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // Include cookies in the request
        body: JSON.stringify({
          name: childData.name,
          dob: childData.dob,
          username: childData.username || "",
          bio: childData.bio || "",
          avatar: childData.avatar || "",
        }),
      });

      if (!response.ok) {
        let errorMessage = "";
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || "Unknown error";
        } catch (e) {
          const errorText = await response.text();
          errorMessage = errorText || `HTTP error ${response.status}`;
        }
        console.error("Error adding child:", response.status, errorMessage);
        set({ isLoading: false, error: errorMessage });
        return {
          success: false,
          error: errorMessage,
        };
      }

      const data = await response.json();

      if (!data.success) {
        console.error("Error adding child:", data.error);
        set({ isLoading: false, error: data.error });
        return {
          success: false,
          error: data.error,
        };
      }

      const childId = data.childId;

      // Fetch the newly created profile
      try {
        const { data: newProfile, error: fetchError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", childId)
          .single();

        if (fetchError) {
          console.error("Error fetching new child profile:", fetchError);
          // Continue anyway, we'll refresh the list
        } else {
          // Add the new child to the state
          const newChild = newProfile as Profile;
          newChild.roles = ["student"];

          set((state) => ({
            children: [newChild, ...state.children],
            selectedChild: newChild,
            isLoading: false,
          }));
        }
      } catch (fetchError) {
        console.error("Error fetching new child profile:", fetchError);
      }

      // Refresh the children list
      await get().fetchChildren(childData.parentId);
      set({ isLoading: false });

      return { success: true, childId };
    } catch (error: any) {
      console.error("Error adding child:", error);
      set({
        isLoading: false,
        error: error.message || "An unknown error occurred",
      });
      return {
        success: false,
        error: error.message || "An unknown error occurred",
      };
    }
  },
}));
