// Profile types for the profile-based architecture

export type ProfileType =
  | "personal" // User's own profile
  | "child" // Child profile
  | "band" // Band/group profile
  | "studio" // Studio profile
  | "teacher" // Teaching persona
  | "other"; // Other profile types

export interface InstrumentCapability {
  instrument: string;
  proficiency: "beginner" | "intermediate" | "advanced" | "expert";
  yearsOfExperience?: number;
  notes?: string;
}

export interface ProfileMetadata {
  bio?: string;
  roles?: string[];
  instruments?: InstrumentCapability[];
  login_permission?: "none" | "limited" | "full";
  from_children_table?: boolean;
  [key: string]: any;
}

export interface Profile {
  id: string;
  username: string;
  display_name: string;
  full_name?: string; // Added missing full_name property
  profile_type: ProfileType;
  user_id: string;
  parent_id?: string | null;
  avatar_url?: string | null;
  color_scheme?: string | null;
  is_active: boolean;
  dob?: string | null;
  created_at: string;
  updated_at?: string | null;
  metadata?: ProfileMetadata | null;
}

export interface ProfileWithRelations extends Profile {
  // Add any relations that might be needed
  instruments_count?: number;
  practice_sessions_count?: number;
  songs_count?: number;
}

export interface CreateProfileParams {
  username: string;
  display_name: string;
  profile_type: ProfileType;
  avatar_url?: string;
  color_scheme?: string;
  dob?: string;
  metadata?: ProfileMetadata;
}

export interface UpdateProfileParams {
  display_name?: string;
  avatar_url?: string | null;
  color_scheme?: string | null;
  is_active?: boolean;
  dob?: string | null;
  metadata?: ProfileMetadata | null;
}

// For profile selection in UI
export interface ProfileOption {
  id: string;
  username: string;
  display_name: string;
  profile_type: ProfileType;
  avatar_url?: string | null;
}

// Context for current profile
export interface ProfileContext {
  currentProfile: Profile | null;
  profiles: Profile[];
  setCurrentProfile: (profile: Profile) => void;
  isLoading: boolean;
  error: Error | null;
}

// For transferring profile ownership
export interface TransferProfileParams {
  profile_id: string;
  new_owner_id: string;
  transfer_code?: string; // For verification
}
