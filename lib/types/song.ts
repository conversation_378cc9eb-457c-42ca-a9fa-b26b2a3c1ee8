// Unified Song type definition for the entire application
export interface Song {
  // Core identifiers
  id: number; // Database primary key
  title: string;
  
  // Artist information
  primary_artist_name?: string;
  primary_artist_id?: string;
  primary_artist_image_url?: string;
  featured_artists?: Array<{ id: string; name: string }>;
  
  // Album information
  album_name?: string;
  album_id?: string;
  album_cover_url?: string;
  song_art_image_url?: string;
  header_image_url?: string;
  
  // Duration and timing
  duration_ms?: number;
  duration?: number; // Duration in seconds (for compatibility)
  
  // Release information
  release_date?: string;
  release_date_for_display?: string;
  year?: number;
  
  // Genre and categorization
  genres?: string[];
  genre?: string; // Single genre for compatibility
  tags?: string[];
  
  // External service IDs
  spotify_id?: string;
  genius_id?: string;
  genius_url?: string;
  apple_music_id?: string;
  youtube_id?: string;
  
  // Audio features (primarily from Spotify)
  valence?: number; // Musical positiveness (0.0-1.0)
  danceability?: number; // How suitable for dancing (0.0-1.0)
  energy?: number; // Intensity and activity (0.0-1.0)
  bpm?: number; // Beats per minute
  tempo?: number; // Alternative to bpm
  key?: number; // The key (0 = C, 1 = C#, ..., 11 = B)
  mode?: number; // Modality (0 = Minor, 1 = Major)
  key_signature?: string; // Textual representation like "C# Major"
  time_signature?: number;
  acousticness?: number;
  instrumentalness?: number;
  liveness?: number;
  loudness?: number;
  speechiness?: number;
  popularity?: number;
  
  // Learning and practice
  learning_status?: string;
  practice_count?: number;
  last_practiced?: string;
  confidence_level?: number;
  difficulty?: number;
  notes?: string;
  
  // Content
  lyrics?: string;
  lyrics_state?: "complete" | "partial" | "missing";
  full_title?: string;
  
  // Ownership and permissions
  user_id?: string;
  creator_id?: string;
  profile_id?: string;
  is_public?: boolean;
  is_original?: boolean;
  
  // Variations and relationships
  parent_song_id?: number;
  variation_name?: string;
  
  // Technical metadata
  source_type?: string;
  provider?: "genius" | "spotify" | "database" | string;
  provider_id?: string;
  spotify_data?: any; // JSON data from Spotify
  genius_data?: any; // JSON data from Genius
  metadata?: any; // Additional JSON metadata
  
  // Instrumentation and performance
  recording_location?: string;
  producers?: any; // JSON array
  writers?: any; // JSON array
  defaultLineup?: string;
  imageUrl?: string; // Alternative to album_cover_url
  artist?: string; // Alternative to primary_artist_name
  album?: string; // Alternative to album_name
  
  // Timestamps
  created_at?: string;
  updated_at?: string;
}

// Simplified Song type for basic usage
export interface SongSummary {
  id: string | number;
  title: string;
  primary_artist_name?: string;
  album_name?: string;
  album_cover_url?: string;
  duration_ms?: number;
  provider?: "genius" | "spotify" | "database";
  provider_id?: string;
}

// Song creation data (for forms)
export interface SongCreationData {
  title: string;
  primary_artist_name?: string;
  album_name?: string;
  genre?: string;
  difficulty?: number;
  notes?: string;
  tags?: string[];
  is_original?: boolean;
}

// Song form data (omitting auto-generated fields)
export type SongFormData = Omit<Song, "id" | "created_at" | "updated_at">;

// Export for backward compatibility
export type { Song as SongType };
