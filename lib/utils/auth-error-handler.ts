/**
 * Enhanced error handling for authentication processes
 * Distinguishes between server configuration errors and user validation errors
 */

export interface AuthError {
  type: "server" | "validation" | "network" | "unknown";
  code?: string;
  message: string;
  userMessage: string;
  actionable: boolean;
  retryable: boolean;
  fallbackOptions?: string[];
}

export interface AuthErrorAnalysis {
  error: AuthError;
  suggestions: string[];
  canRetry: boolean;
  showFallback: boolean;
}

/**
 * Analyzes Supabase auth errors and provides user-friendly messaging
 */
export function analyzeAuthError(error: any): AuthErrorAnalysis {
  const errorMessage =
    error?.message || error?.error_description || String(error);
  const errorCode = error?.error || error?.code;

  // SMTP/Email Configuration Errors (Server-side issues)
  if (
    errorMessage.includes("535 5.7.8") ||
    errorMessage.includes("Username and Password not accepted") ||
    errorMessage.includes("BadCredentials")
  ) {
    return {
      error: {
        type: "server",
        code: "SMTP_AUTH_FAILED",
        message: errorMessage,
        userMessage:
          "Our email service is temporarily unavailable. This is not an issue with your account details.",
        actionable: false,
        retryable: true,
        fallbackOptions: [
          "Try again in a few minutes",
          "Contact support if the issue persists",
        ],
      },
      suggestions: [
        "This is a temporary server configuration issue",
        "Your account details are correct",
        "Please try again in a few minutes",
        "If the problem persists, contact our support team",
      ],
      canRetry: true,
      showFallback: true,
    };
  }

  // Email sending errors (Server-side)
  if (
    errorMessage.includes("Error sending confirmation email") ||
    errorMessage.includes("Failed to send email") ||
    errorMessage.includes("SMTP")
  ) {
    return {
      error: {
        type: "server",
        code: "EMAIL_SEND_FAILED",
        message: errorMessage,
        userMessage:
          "Unable to send confirmation email due to a server issue. Your account may have been created successfully.",
        actionable: true,
        retryable: true,
        fallbackOptions: [
          "Try signing in with your credentials",
          "Request a new confirmation email",
        ],
      },
      suggestions: [
        "Your account might have been created successfully",
        "Try signing in with your email and password",
        "Check your spam/junk folder for the confirmation email",
        "Contact support if you need help",
      ],
      canRetry: true,
      showFallback: true,
    };
  }

  // Password validation errors (User input issues)
  if (
    errorMessage.includes("Password should be at least") ||
    (errorMessage.includes("password") && errorMessage.includes("weak"))
  ) {
    return {
      error: {
        type: "validation",
        code: "WEAK_PASSWORD",
        message: errorMessage,
        userMessage:
          "Please create a stronger password that meets our security requirements.",
        actionable: true,
        retryable: true,
      },
      suggestions: [
        "Use at least 8 characters",
        "Include uppercase and lowercase letters",
        "Add numbers and special characters",
        "Avoid common words or personal information",
      ],
      canRetry: true,
      showFallback: false,
    };
  }

  // Email already exists (User input issue)
  if (
    errorMessage.includes("already registered") ||
    errorMessage.includes("already exists") ||
    errorMessage.includes("User already registered")
  ) {
    return {
      error: {
        type: "validation",
        code: "EMAIL_EXISTS",
        message: errorMessage,
        userMessage: "An account with this email already exists.",
        actionable: true,
        retryable: false,
        fallbackOptions: [
          "Sign in instead",
          "Reset your password if you forgot it",
        ],
      },
      suggestions: [
        "Try signing in with this email",
        'Use the "Forgot Password" option if needed',
        "Check if you have an existing account",
        "Use a different email address",
      ],
      canRetry: false,
      showFallback: true,
    };
  }

  // Invalid email format (User input issue)
  if (
    errorMessage.includes("Invalid email") ||
    (errorMessage.includes("email") && errorMessage.includes("invalid"))
  ) {
    return {
      error: {
        type: "validation",
        code: "INVALID_EMAIL",
        message: errorMessage,
        userMessage: "Please enter a valid email address.",
        actionable: true,
        retryable: true,
      },
      suggestions: [
        "Check your email format (<EMAIL>)",
        "Remove any extra spaces",
        "Make sure you included the @ symbol and domain",
      ],
      canRetry: true,
      showFallback: false,
    };
  }

  // Network/connectivity errors
  if (
    errorMessage.includes("fetch") ||
    errorMessage.includes("network") ||
    errorMessage.includes("connection") ||
    errorMessage.includes("timeout")
  ) {
    return {
      error: {
        type: "network",
        code: "NETWORK_ERROR",
        message: errorMessage,
        userMessage:
          "Connection issue. Please check your internet connection and try again.",
        actionable: true,
        retryable: true,
      },
      suggestions: [
        "Check your internet connection",
        "Try refreshing the page",
        "Wait a moment and try again",
      ],
      canRetry: true,
      showFallback: false,
    };
  }

  // Rate limiting
  if (
    errorMessage.includes("rate limit") ||
    errorMessage.includes("too many requests")
  ) {
    return {
      error: {
        type: "server",
        code: "RATE_LIMITED",
        message: errorMessage,
        userMessage:
          "Too many attempts. Please wait a few minutes before trying again.",
        actionable: true,
        retryable: true,
      },
      suggestions: [
        "Wait 5-10 minutes before trying again",
        "Avoid multiple rapid attempts",
      ],
      canRetry: true,
      showFallback: false,
    };
  }

  // Email confirmation required (this is actually a success state)
  if (
    errorMessage.includes("Email confirmation required") ||
    errorMessage.includes("confirmation required") ||
    (error as any)?.isConfirmationRequired
  ) {
    return {
      error: {
        type: "info",
        code: "EMAIL_CONFIRMATION_REQUIRED",
        message: errorMessage,
        userMessage: "Account created successfully! Please check your email to confirm your account.",
        actionable: true,
        retryable: false,
      },
      suggestions: [
        "Check your email inbox (including spam folder)",
        "Click the confirmation link in the email",
        "You can then sign in normally",
        "Contact support if you don't receive the email",
      ],
      canRetry: false,
      showFallback: true,
    };
  }

  // Generic/unknown errors
  return {
    error: {
      type: "unknown",
      code: errorCode || "UNKNOWN_ERROR",
      message: errorMessage,
      userMessage: "An unexpected error occurred. Please try again.",
      actionable: true,
      retryable: true,
    },
    suggestions: [
      "Try again in a few moments",
      "Refresh the page and retry",
      "Contact support if the issue persists",
    ],
    canRetry: true,
    showFallback: false,
  };
}

/**
 * Formats error messages for display to users
 */
export function formatErrorForUser(analysis: AuthErrorAnalysis): {
  title: string;
  message: string;
  type: "error" | "warning" | "info";
  actions: string[];
} {
  const { error, suggestions, showFallback } = analysis;

  let title = "Error";
  let type: "error" | "warning" | "info" = "error";

  switch (error.type) {
    case "server":
      title = "Service Temporarily Unavailable";
      type = "warning";
      break;
    case "validation":
      title = "Please Check Your Input";
      type = "info";
      break;
    case "network":
      title = "Connection Issue";
      type = "warning";
      break;
    case "info":
      if (error.code === "EMAIL_CONFIRMATION_REQUIRED") {
        title = "Account Created Successfully!";
        type = "info";
      } else {
        title = "Information";
        type = "info";
      }
      break;
    default:
      title = "Something Went Wrong";
      type = "error";
  }

  const actions = [...suggestions];
  if (showFallback && error.fallbackOptions) {
    actions.push(...error.fallbackOptions);
  }

  return {
    title,
    message: error.userMessage,
    type,
    actions,
  };
}
