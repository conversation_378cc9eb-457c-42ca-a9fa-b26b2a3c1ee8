import { createLogger } from "@/lib/logs/logger";

const logger = createLogger("network-diagnostics");

export interface NetworkDiagnostics {
  timestamp: string;
  environment: string;
  dnsResolution: {
    spotify: boolean;
    google: boolean;
    cloudflare: boolean;
  };
  connectivity: {
    spotify: boolean;
    spotifyStatus: number | null;
    spotifyError: string | null;
    google: boolean;
    cloudflare: boolean;
  };
  systemInfo: {
    userAgent: string;
    platform: string;
    nodeVersion: string;
  };
}

/**
 * Comprehensive network diagnostics to help identify connectivity issues
 */
export async function runNetworkDiagnostics(): Promise<NetworkDiagnostics> {
  const diagnostics: NetworkDiagnostics = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "unknown",
    dnsResolution: {
      spotify: false,
      google: false,
      cloudflare: false,
    },
    connectivity: {
      spotify: false,
      spotifyStatus: null,
      spotifyError: null,
      google: false,
      cloudflare: false,
    },
    systemInfo: {
      userAgent: process.env.HTTP_USER_AGENT || "Node.js",
      platform: process.platform,
      nodeVersion: process.version,
    },
  };

  logger.info("Starting network diagnostics...");

  // Test DNS resolution
  await testDnsResolution(diagnostics);

  // Test connectivity
  await testConnectivity(diagnostics);

  logger.info("Network diagnostics completed", diagnostics);
  return diagnostics;
}

async function testDnsResolution(diagnostics: NetworkDiagnostics) {
  // Use dynamic import for DNS module to avoid CommonJS issues
  let dns: any = null;
  try {
    const dnsModule = await import("dns");
    dns = dnsModule.promises;
  } catch (error) {
    logger.warn("DNS module not available", { error });
    return;
  }

  // Test Spotify DNS resolution
  try {
    await dns.lookup("accounts.spotify.com");
    diagnostics.dnsResolution.spotify = true;
    logger.debug("Spotify DNS resolution: SUCCESS");
  } catch (error: any) {
    logger.warn("Spotify DNS resolution: FAILED", { error: error.message });
  }

  // Test Google DNS resolution
  try {
    await dns.lookup("google.com");
    diagnostics.dnsResolution.google = true;
    logger.debug("Google DNS resolution: SUCCESS");
  } catch (error: any) {
    logger.warn("Google DNS resolution: FAILED", { error: error.message });
  }

  // Test Cloudflare DNS resolution
  try {
    await dns.lookup("cloudflare.com");
    diagnostics.dnsResolution.cloudflare = true;
    logger.debug("Cloudflare DNS resolution: SUCCESS");
  } catch (error: any) {
    logger.warn("Cloudflare DNS resolution: FAILED", { error: error.message });
  }
}

async function testConnectivity(diagnostics: NetworkDiagnostics) {
  // Test Spotify connectivity
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch("https://accounts.spotify.com/api/token", {
      method: "HEAD", // Just test connectivity, don't send data
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    diagnostics.connectivity.spotify = true;
    diagnostics.connectivity.spotifyStatus = response.status;
    logger.debug("Spotify connectivity: SUCCESS", { status: response.status });
  } catch (error: any) {
    diagnostics.connectivity.spotifyError = error.message;
    logger.warn("Spotify connectivity: FAILED", {
      error: error.message,
      code: error.code,
    });
  }

  // Test Google connectivity
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    await fetch("https://google.com", {
      method: "HEAD",
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    diagnostics.connectivity.google = true;
    logger.debug("Google connectivity: SUCCESS");
  } catch (error: any) {
    logger.warn("Google connectivity: FAILED", { error: error.message });
  }

  // Test Cloudflare connectivity
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    await fetch("https://cloudflare.com", {
      method: "HEAD",
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    diagnostics.connectivity.cloudflare = true;
    logger.debug("Cloudflare connectivity: SUCCESS");
  } catch (error: any) {
    logger.warn("Cloudflare connectivity: FAILED", { error: error.message });
  }
}

/**
 * Quick connectivity test specifically for Spotify
 */
export async function testSpotifyConnectivity(): Promise<{
  success: boolean;
  error?: string;
  status?: number;
}> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000);

    const response = await fetch("https://accounts.spotify.com/api/token", {
      method: "HEAD",
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    return {
      success: true,
      status: response.status,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message,
    };
  }
}
