➜  april-09 git:(backup/fix-setlists-add-nav) ✗ npm run build

> crescender@0.1.0 build
> next build

   ▲ Next.js 15.3.2
   - Environments: .env.local, .env

   Creating an optimized production build ...
 ✓ Compiled successfully in 94s
   Skipping validation of types

Failed to compile.

./app/api/children/[username]/curriculum/route.ts
22:2  Error: Parsing error: Identifier expected. 'const' is a reserved word that cannot be used here.

./app/api/children/[username]/practice/assignments/[id]/route.ts
19:2  Error: Parsing error: Identifier expected. 'const' is a reserved word that cannot be used here.

./app/api/children/[username]/practice/assignments/route.ts
19:2  Error: Parsing error: Identifier expected. 'const' is a reserved word that cannot be used here.

./app/api/children/[username]/practice/most-practiced/route.ts
12:2  Error: Parsing error: Identifier expected. 'const' is a reserved word that cannot be used here.

./app/api/children/[username]/practice/route.ts
147:2  Error: Parsing error: Identifier expected. 'const' is a reserved word that cannot be used here.

./app/api/children/[username]/songs/route.ts
27:2  Error: Parsing error: Identifier expected. 'const' is a reserved word that cannot be used here.

./app/api/practice/[id]/route.ts
16:2  Error: Parsing error: ',' expected.

./app/api/setlists/[slug]/entries/route.ts
11:2  Error: Parsing error: Identifier expected. 'const' is a reserved word that cannot be used here.

./app/api/setlists/[slug]/route.ts
45:2  Error: Parsing error: Identifier expected. 'const' is a reserved word that cannot be used here.

./components/finances/money-dashboard.tsx
128:6  Warning: React Hook useCallback has a missing dependency: 'currentProfile'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
144:6  Warning: React Hook useCallback has a missing dependency: 'currentProfile'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
155:6  Warning: React Hook useCallback has a missing dependency: 'currentProfile'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./components/ui/google-maps-search.tsx
237:6  Warning: React Hook useEffect has an unnecessary dependency: 'currentUser'. Either exclude it or remove the dependency array. Outer scope values like 'currentUser' aren't valid dependencies because mutating them doesn't re-render the component.  react-hooks/exhaustive-deps

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules
➜  april-09 git:(backup/fix-setlists-add-nav) ✗ 