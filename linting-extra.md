➜  april-09 git:(backup/fix-setlists-add-nav) ✗ npm run build

> crescender@0.1.0 build
> next build

   ▲ Next.js 15.3.2
   - Environments: .env.local, .env

   Creating an optimized production build ...
 ✓ Compiled successfully in 59s
   Skipping validation of types

./components/finances/money-dashboard.tsx
128:6  Warning: React Hook useCallback has a missing dependency: 'currentProfile'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
144:6  Warning: React Hook useCallback has a missing dependency: 'currentProfile'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
155:6  Warning: React Hook useCallback has a missing dependency: 'currentProfile'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./components/ui/google-maps-search.tsx
237:6  Warning: React Hook useEffect has an unnecessary dependency: 'currentUser'. Either exclude it or remove the dependency array. Outer scope values like 'currentUser' aren't valid dependencies because mutating them doesn't re-render the component.  react-hooks/exhaustive-deps

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules
 ✓ Linting 
   Collecting page data  ..Error: `cookies` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context
    at f (.next/server/chunks/4999.js:1:14746)
    at <unknown> (.next/server/app/api/deviceapps/children/verify-shared-code/route.js:1:2126)
    at t.a (.next/server/webpack-runtime.js:1:903)
    at 3451 (.next/server/app/api/deviceapps/children/verify-shared-code/route.js:1:189)
    at t (.next/server/webpack-runtime.js:1:142)
    at <unknown> (.next/server/app/api/deviceapps/children/verify-shared-code/route.js:1:5761)
    at t.a (.next/server/webpack-runtime.js:1:903)
    at 93215 (.next/server/app/api/deviceapps/children/verify-shared-code/route.js:1:5582)

> Build error occurred
[Error: Failed to collect page data for /api/deviceapps/children/verify-shared-code] {
  type: 'Error'
}
➜  april-09 git:(backup/fix-setlists-add-nav) ✗ 