  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthLayout] Rendering with AuthInitializer.
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[AuthInitializer] Server component initializing auth state
[AuthLayout] Rendering with AuthInitializer.
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthLayout] Rendering with AuthInitializer.
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /setlists couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /setlists couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /setlists/add couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /setlists/add couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /receipts couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /receipts couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /settings/connections/spotify-callback couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /settings/connections/spotify-callback couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /settings/connections couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /settings/connections couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /setlists/wizard couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /setlists/wizard couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /test-colors couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /test-colors couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /receipts/capture couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /receipts/capture couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /test-public-profile couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /test-public-profile couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /songs couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /songs couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /subscription couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /subscription couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /transactions couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /transactions couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /settings/collab-hub couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /settings/collab-hub couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /settings couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /settings couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /admin/dashboard/billing couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /admin/dashboard/billing couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /auth/signup couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /auth/signup couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /auth/signup couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580)
    at stringify (<anonymous>) {
  description: "Route /auth/signup couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /auth/signout couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /auth/signout couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /auth/signout couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580)
    at stringify (<anonymous>) {
  description: "Route /auth/signout couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /signup-clean couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /signup-clean couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /deviceapps/shared couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /deviceapps/shared couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /deviceapps couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /deviceapps couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /auth/forgot-password couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /auth/forgot-password couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /auth/forgot-password couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580)
    at stringify (<anonymous>) {
  description: "Route /auth/forgot-password couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /deviceapps/kids/login couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /deviceapps/kids/login couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /deviceapps/kids/tablet/ios couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /deviceapps/kids/tablet/ios couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /deviceapps/kids couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /deviceapps/kids couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /children/add couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /children/add couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /calendar couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /calendar couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /debug couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /debug couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /forgot-password couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /forgot-password couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /gear/dbfull/new couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /gear/dbfull/new couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /gear/new couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /gear/new couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /learning/curricula couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /learning/curricula couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /learning couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /learning couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /gear couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /gear couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /learning/browse couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /learning/browse couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /learning/tutorials couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /learning/tutorials couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /maps-test couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /maps-test couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /children/new couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /children/new couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /new-profile couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /new-profile couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AppLayout] Root layout rendering
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /activate couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /activate couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /api-key-test couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /api-key-test couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /_not-found couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /_not-found couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /calendar/availability couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /calendar/availability couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /admin-direct couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /admin-direct couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /calendar/events couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /calendar/events couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /calendar/events/new couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /calendar/events/new couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /children couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /children couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /curriculum couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /curriculum couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /deviceapps-login couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /deviceapps-login couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /child-login couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /child-login couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /admin/homescreen-setup couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /admin/homescreen-setup couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /practice/new couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /practice/new couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /admin/dashboard/activity couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /admin/dashboard/activity couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /admin/brands couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /admin/brands couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /admin couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /admin couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /admin/dashboard couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /admin/dashboard couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /auth/create-profile couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /auth/create-profile couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /auth/create-profile couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580)
    at stringify (<anonymous>) {
  description: "Route /auth/create-profile couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /admin/spotify-test couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /admin/spotify-test couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /auth/signin couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580) {
  description: "Route /auth/signin couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Unexpected server error: Error: Dynamic server usage: Route /auth/signin couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
    at y (.next/server/chunks/4447.js:19:10449)
    at f (.next/server/chunks/4999.js:1:14660)
    at l (.next/server/chunks/5145.js:1:48580)
    at stringify (<anonymous>) {
  description: "Route /auth/signin couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'
}
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AdminPage] Auth state: {
  isAuthenticated: false,
  isLoading: true,
  userId: undefined,
  authUser: 'null',
  roles: null
}
[AdminPage] Admin check: {
  isAdmin: false,
  rolesAdmin: undefined,
  userId: undefined,
  hasRoles: false
}
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthLayout] Rendering with AuthInitializer.
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthInitializer] Server component initializing auth state
[AuthLayout] Rendering with AuthInitializer.
[AuthInitializer] Server component initializing auth state
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[RobustAuthProvider] Next.js 15 Best Practice Provider
[AuthStateHydrator] Component rendering with initial state: {
  hasAuthUser: false,
  hasProfile: false,
  hasRoles: false,
  hasChildren: false,
  childrenCount: undefined
}
[SignInPage] Rendering (Step 4 - Full handleSignIn Active).
 ⨯ Next.js build worker exited with code: 1 and signal: null
➜  april-09 git:(backup/fix-setlists-add-nav) ✗ 