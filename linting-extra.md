The following routes couldn't be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error",
  digest: 'DYNAMIC_SERVER_USAGE'

/gear
/_not-found
/maps-test
/learning/tutorials
/learning/curricula
/new-profile
/parent/events
/learning/browse
/parent
/
/money
/practice/new
/learning
/profiles
/calendar/events/new 
/setlists
/reset-password
/settings/collab-hub 
/receipts/capture
/profile-management
/receipts
/admin
/deviceapps
/auth/forgot-password 
/auth/forgot-password 
/deviceapps/shared
/signup-clean
/practice
/auth/signup
/auth/signup
/deviceapps/kids/mobile/ios 
/deviceapps/kids/tv/ios 
/deviceapps/kids/tv/android 
/deviceapps/kids/mobile/
/setlists/add
/subscription
/settings/connections 
/test-public-profile 
/test-colors
/songs
/settings
/gear/new
/admin-direct
/activate
/settings/connections/
/transactions/new
/calendar
/child-login
/api-key-test
/children/add
/calendar/events
/children/new
/curriculum
/calendar/availability 
/children
/deviceapps-login
/forgot-password
/debug