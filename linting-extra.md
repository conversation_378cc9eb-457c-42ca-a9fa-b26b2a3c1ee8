➜  april-09 git:(backup/fix-setlists-add-nav) ✗ npm run build

> crescender@0.1.0 build
> next build

   ▲ Next.js 15.3.2
   - Environments: .env.local, .env

   Creating an optimized production build ...
Failed to compile.

./app/api/genres/route.ts
Module parse failed: Identifier 'cookieStore' has already been declared (9:10)
File was processed with these loaders:
 * ./node_modules/next/dist/build/webpack/loaders/next-flight-loader/index.js
 * ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js
You may need an additional loader to handle the result of these loaders.
|     const cookieStore = await cookies();
|     // Next.js requires cookies() to be read before use
>     const cookieStore = cookies();
|     const supabase = createRouteHandlerClient({
|         cookies: ()=>cookieStore

Import trace for requested module:
./app/api/genres/route.ts

./app/api/practice/[id]/route.ts
Error:   × Expected ',', got 'const'
    ╭─[/Users/<USER>/Utilities/april-09/app/api/practice/[id]/route.ts:16:1]
 13 │ export async function GET(
 14 │   request: NextRequest,
 15 │   { params }: { params: Promise<{ id: string }> }
 16 │   const cookieStore = await cookies();
    ·   ─────
 17 │ ) {
 18 │   const { id } = await params;
 19 │   const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    ╰────

Caused by:
    Syntax Error

Import trace for requested module:
./app/api/practice/[id]/route.ts

./app/api/setlists/[slug]/entries/route.ts
Module parse failed: Identifier 'cookieStore' has already been declared (9:10)
File was processed with these loaders:
 * ./node_modules/next/dist/build/webpack/loaders/next-flight-loader/index.js
 * ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js
You may need an additional loader to handle the result of these loaders.
|     const cookieStore = await cookies();
|     const { slug } = await params;
>     const cookieStore = cookies();
|     const supabase = createRouteHandlerClient({
|         cookies: ()=>cookieStore

Import trace for requested module:
./app/api/setlists/[slug]/entries/route.ts

./app/api/setlists/[slug]/route.ts
Error:   × Expected ident
     ╭─[/Users/<USER>/Utilities/april-09/app/api/setlists/[slug]/route.ts:107:1]
 104 │ export async function PATCH(
 105 │   request: NextRequest,
 106 │   { params }: { params: Promise<{ slug: string }> }, // Ensure params is destructured correctly here too
 107 │   const cookieStore = await cookies();
     ·   ─────
 108 │ ) {
 109 │   const startTime = Date.now();
 110 │   const { slug } = await params;
     ╰────

Caused by:
    Syntax Error

Import trace for requested module:
./app/api/setlists/[slug]/route.ts


> Build failed because of webpack errors
➜  april-09 git:(backup/fix-setlists-add-nav) ✗ 