-- Fix Events table RLS policies to ensure event creation works properly
-- This migration ensures the events table has the correct RLS policies for profile-based access

-- Enable RLS on events table
ALTER TABLE events ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view their own events" ON events;
DROP POLICY IF EXISTS "Users can insert their own events" ON events;
DROP POLICY IF EXISTS "Users can update their own events" ON events;
DROP POLICY IF EXISTS "Users can delete their own events" ON events;
DROP POLICY IF EXISTS "Affiliated users can view events" ON events;
DROP POLICY IF EXISTS "events_select_policy" ON events;
DROP POLICY IF EXISTS "events_insert_policy" ON events;
DROP POLICY IF EXISTS "events_update_policy" ON events;
DROP POLICY IF EXISTS "events_delete_policy" ON events;

-- Create new policies for events using profile-based access
-- These policies allow users to manage events where they are the creator

CREATE POLICY "events_select_policy" ON events
  FOR SELECT
  USING (
    creator_profile_id IN (
      SELECT id FROM profiles WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "events_insert_policy" ON events
  FOR INSERT
  WITH CHECK (
    creator_profile_id IN (
      SELECT id FROM profiles WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "events_update_policy" ON events
  FOR UPDATE
  USING (
    creator_profile_id IN (
      SELECT id FROM profiles WHERE user_id = auth.uid()
    )
  )
  WITH CHECK (
    creator_profile_id IN (
      SELECT id FROM profiles WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "events_delete_policy" ON events
  FOR DELETE
  USING (
    creator_profile_id IN (
      SELECT id FROM profiles WHERE user_id = auth.uid()
    )
  );

-- Grant necessary permissions to authenticated users
GRANT ALL ON events TO authenticated;

-- Create index for better performance if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_events_creator_profile_id ON events(creator_profile_id);

-- Verify the policies are working by checking if the current user can access their profiles
-- This is a diagnostic query that can be run to test the setup
-- SELECT id FROM profiles WHERE user_id = auth.uid();
