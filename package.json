{"name": "crescender", "displayName": "Crescender", "version": "0.1.0", "private": true, "engines": {"node": ">=18.17.0"}, "scripts": {"scrape-tags": "ts-node scripts/fetch-genius-tags.ts", "dev": "NEXT_IGNORE_NODE_VERSION=true next dev", "dev:deviceapps": "NEXT_IGNORE_NODE_VERSION=true next dev -p 3030", "clean-dev": "node clean-dev.js", "build": "next build", "build:vercel": "NODE_ENV=production next build", "clean-build": "rm -rf .next && next build", "start": "next start", "lint": "next lint", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "reindex": "node scripts/reindex.js", "test:ui": "vitest --ui", "scan-testable": "node scripts/scan-testable-elements.js", "generate-tests": "node scripts/generate-tests-from-registry.js 10", "run-tests": "node scripts/run-and-analyze-tests.js", "test-plan": "node scripts/master-test-plan.js", "test:parent-child": "node scripts/test-parent-child.js", "test:curriculum": "ts-node tests/curriculum-lesson-plan.test.ts", "test:check-functionality": "ts-node tests/check-parent-child-functionality.ts", "test:fix-cycle": "ts-node tests/run-test-fix-cycle.ts", "test:fix-issues": "ts-node tests/fix-issues.ts", "test:db-schema": "node scripts/run-migrations-client.js", "test:add-child-auth": "node tests/add-child-auth-test.js", "test:browser-add-child": "node tests/browser-add-child-test.js"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.6.0", "@google-cloud/vision": "^5.1.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@stripe/stripe-js": "^7.2.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-table": "latest", "@types/uuid": "^10.0.0", "@veryfi/veryfi-sdk": "^1.4.4", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "buffer": "^6.0.3", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "critters": "^0.0.23", "date-fns": "latest", "dotenv": "^16.5.0", "embla-carousel-react": "8.5.1", "eslint": "8.57.0", "firebase": "^11.6.1", "firebase-admin": "^13.4.0", "glob": "^11.0.2", "html5-qrcode": "^2.3.8", "immer": "latest", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "multiselect-react-dropdown": "^2.0.25", "next": "15.3.2", "next-themes": "^0.4.6", "node-fetch": "^2.6.9", "nodemailer": "^7.0.3", "nvm": "^0.0.4", "pragmatic-drag-and-drop": "^0.0.1-security", "process": "^0.11.10", "qrcode.react": "^3.1.0", "react": "^18", "react-calendar": "^6.0.0", "react-day-picker": "8.10.1", "react-dom": "^18", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.1", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "stripe": "^18.1.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.3", "upgrade": "^1.1.0", "use-sync-external-store": "latest", "uuid": "^11.1.0", "vaul": "^0.9.6", "zod": "^3.24.1", "zustand": "latest"}, "devDependencies": {"@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/node": "^22", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^3.1.1", "eslint": "8.57.0", "eslint-config-next": "15.3.2", "jsdom": "^24.0.0", "msw": "^2.1.5", "npm-force-resolutions": "^0.0.10", "postcss": "^8", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5", "vite-tsconfig-paths": "^4.3.1", "vitest": "^3.1.1", "whatwg-fetch": "^3.6.20"}, "overrides": {"eslint": "8.57.0"}}