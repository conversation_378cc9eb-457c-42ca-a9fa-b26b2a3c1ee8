#!/usr/bin/env node

/**
 * Final Cookie Fix Script
 * 
 * This script comprehensively fixes all remaining cookie-related issues:
 * 1. Adds export const dynamic = "force-dynamic" to all routes using cookies
 * 2. Fixes createRouteHandlerClient({ cookies }) patterns
 * 3. Fixes createRouteHandlerClient({ cookies: () => cookies() }) patterns
 */

const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// Find all API route files
async function findApiRoutes() {
  const apiRoutes = await glob('app/api/**/route.ts', { cwd: process.cwd() });
  return apiRoutes;
}

// Check if a file uses cookies
function usesCookies(content) {
  return content.includes('from "next/headers"') && 
         (content.includes('cookies()') || content.includes('cookies ') || content.includes('{ cookies }'));
}

// Check if file already has dynamic export
function hasDynamicExport(content) {
  return content.includes('export const dynamic') || 
         content.includes('export const runtime');
}

// Add dynamic export to file
function addDynamicExport(content) {
  const lines = content.split('\n');
  let insertIndex = 0;
  
  // Find the first import statement
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Skip comments and empty lines at the top
    if (line.startsWith('//') || line.startsWith('/*') || line === '') {
      continue;
    }
    
    // If we find an import, insert before it
    if (line.startsWith('import ')) {
      insertIndex = i;
      break;
    }
    
    // If we find an export, insert before it
    if (line.startsWith('export ')) {
      insertIndex = i;
      break;
    }
    
    // If we find a function or other code, insert before it
    if (line.length > 0) {
      insertIndex = i;
      break;
    }
  }
  
  // Insert the dynamic export
  lines.splice(insertIndex, 0, 'export const dynamic = "force-dynamic";', '');
  
  return lines.join('\n');
}

// Fix all cookie patterns in a file
function fixCookiePatterns(content) {
  // Pattern 1: createRouteHandlerClient({ cookies })
  content = content.replace(
    /createRouteHandlerClient\(\{\s*cookies\s*\}\)/g,
    'createRouteHandlerClient({ cookies: () => cookieStore })'
  );
  
  // Pattern 2: createRouteHandlerClient({ cookies: () => cookies() })
  content = content.replace(
    /createRouteHandlerClient\(\{\s*cookies:\s*\(\)\s*=>\s*cookies\(\)\s*\}\)/g,
    'createRouteHandlerClient({ cookies: () => cookieStore })'
  );
  
  return content;
}

// Add cookieStore declarations to functions that need them
function addCookieStoreDeclarations(content) {
  const lines = content.split('\n');
  let modified = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // If we find a function that uses createRouteHandlerClient with cookieStore
    if (line.includes('createRouteHandlerClient({ cookies: () => cookieStore })')) {
      
      // Check if cookieStore is already declared in this function
      let functionStart = i;
      while (functionStart > 0 && !lines[functionStart].includes('export async function')) {
        functionStart--;
      }
      
      // Check if cookieStore is already declared in this function
      let hasCookieStore = false;
      for (let j = functionStart; j <= i; j++) {
        if (lines[j].includes('const cookieStore = await cookies()')) {
          hasCookieStore = true;
          break;
        }
      }
      
      if (!hasCookieStore) {
        // Find the opening brace of the function
        let braceIndex = functionStart;
        while (braceIndex < lines.length && !lines[braceIndex].includes('{')) {
          braceIndex++;
        }
        
        // Insert cookieStore declaration after the opening brace
        if (braceIndex < lines.length) {
          const indent = '  '; // Standard 2-space indent
          lines.splice(braceIndex + 1, 0, `${indent}const cookieStore = await cookies();`);
          modified = true;
          i++; // Adjust index since we inserted a line
        }
      }
    }
  }
  
  return lines.join('\n');
}

// Process a single file
function processFile(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ File not found: ${filePath}`);
      return false;
    }
    
    const originalContent = fs.readFileSync(fullPath, 'utf8');
    let content = originalContent;
    
    // Check if file uses cookies
    if (!usesCookies(content)) {
      console.log(`⏭️  Skipping ${filePath} (doesn't use cookies)`);
      return false;
    }
    
    // Add dynamic export if missing
    if (!hasDynamicExport(content)) {
      content = addDynamicExport(content);
    }
    
    // Fix cookie patterns
    content = fixCookiePatterns(content);
    
    // Add cookieStore declarations where needed
    content = addCookieStoreDeclarations(content);
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`🔧 Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`✅ Already correct: ${filePath}`);
      return false;
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
async function main() {
  console.log('🔧 Running final comprehensive cookie fix...\n');
  
  const apiRoutes = await findApiRoutes();
  console.log(`Found ${apiRoutes.length} API route files\n`);
  
  let fixedCount = 0;
  
  for (const route of apiRoutes) {
    if (processFile(route)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ Fixed ${fixedCount} routes`);
  console.log('\n🚀 Now run "npm run build" to verify all fixes!');
}

// Run the script
main().catch(console.error);
