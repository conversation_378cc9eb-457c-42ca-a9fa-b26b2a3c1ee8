#!/usr/bin/env node

/**
 * Fix Calendar Component Issues Script
 * 
 * This script fixes calendar component prop mismatches for react-day-picker v8+
 * The main issue is that 'mode' prop has been replaced with different props.
 */

const fs = require('fs');
const path = require('path');

// Files that need calendar fixes
const filesToFix = [
  'app/parent/add-child-form.tsx',
  'app/parent/new-add-child-form.tsx', 
  'components/children/child-form.tsx',
  'components/profile/ProfileForm.tsx'
];

// Fix calendar component usage
function fixCalendarUsage(content) {
  // Pattern: mode="single" with onSelect prop
  // Replace with proper react-day-picker v8+ syntax
  content = content.replace(
    /mode:\s*["']single["']\s*,\s*selected:\s*([^,]+),\s*onSelect:\s*([^,]+),/g,
    'mode: "single",\n        selected: $1,\n        onSelect: $2,'
  );
  
  // Remove mode prop entirely and fix the Calendar component usage
  content = content.replace(
    /\{\s*mode:\s*["']single["']\s*,\s*selected:\s*([^,]+),\s*onSelect:\s*([^,]+),([^}]+)\}/g,
    '{\n        selected: $1,\n        onSelect: $2,$3\n      }'
  );
  
  // Fix specific pattern with mode: string
  content = content.replace(
    /mode:\s*["']single["']\s*,/g,
    ''
  );
  
  return content;
}

// Process a single file
function processFile(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ File not found: ${filePath}`);
      return false;
    }
    
    const originalContent = fs.readFileSync(fullPath, 'utf8');
    let content = originalContent;
    
    // Check if file uses Calendar component
    if (!content.includes('Calendar') && !content.includes('mode:')) {
      console.log(`⏭️  Skipping ${filePath} (doesn't use Calendar component)`);
      return false;
    }
    
    // Apply fixes
    content = fixCalendarUsage(content);
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`🔧 Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`✅ Already correct: ${filePath}`);
      return false;
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
async function main() {
  console.log('🔧 Fixing calendar component issues...\n');
  
  let fixedCount = 0;
  
  for (const file of filesToFix) {
    if (processFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ Fixed ${fixedCount} calendar components`);
  console.log('\n🚀 Calendar components should now be compatible with react-day-picker v8+!');
}

// Run the script
main().catch(console.error);
