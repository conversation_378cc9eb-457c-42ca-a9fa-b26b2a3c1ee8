#!/usr/bin/env node

/**
 * Fix Cookie Type Issues Script
 * 
 * This script fixes the TypeScript errors where createRouteHandlerClient
 * expects cookies function but we're passing cookieStore.
 */

const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// Find all API route files
async function findApiRoutes() {
  const apiRoutes = await glob('app/api/**/route.ts', { cwd: process.cwd() });
  return apiRoutes;
}

// Fix cookie type issues in a file
function fixCookieTypes(content) {
  // Pattern 1: createRouteHandlerClient({ cookies: () => cookieStore })
  // Should be: createRouteHandlerClient({ cookies })
  content = content.replace(
    /createRouteHandlerClient\(\{\s*cookies:\s*\(\)\s*=>\s*cookieStore\s*\}\)/g,
    'createRouteHandlerClient({ cookies })'
  );
  
  // Remove cookieStore declarations that are no longer needed
  content = content.replace(
    /\s*const cookieStore = await cookies\(\);\s*\n/g,
    '\n'
  );
  
  return content;
}

// Process a single file
function processFile(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ File not found: ${filePath}`);
      return false;
    }
    
    const originalContent = fs.readFileSync(fullPath, 'utf8');
    let content = originalContent;
    
    // Check if file uses createRouteHandlerClient
    if (!content.includes('createRouteHandlerClient')) {
      console.log(`⏭️  Skipping ${filePath} (doesn't use createRouteHandlerClient)`);
      return false;
    }
    
    // Apply fixes
    content = fixCookieTypes(content);
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`🔧 Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`✅ Already correct: ${filePath}`);
      return false;
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
async function main() {
  console.log('🔧 Fixing cookie type issues in API routes...\n');
  
  const apiRoutes = await findApiRoutes();
  console.log(`Found ${apiRoutes.length} API route files\n`);
  
  let fixedCount = 0;
  
  for (const route of apiRoutes) {
    if (processFile(route)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ Fixed ${fixedCount} API routes`);
  console.log('\n🚀 Now run TypeScript check to verify fixes!');
}

// Run the script
main().catch(console.error);
