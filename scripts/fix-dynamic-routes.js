#!/usr/bin/env node

/**
 * Fix Dynamic Routes Script
 * 
 * This script adds 'export const dynamic = "force-dynamic"' to all API routes
 * that use cookies() to prevent Next.js 15 static rendering issues.
 */

const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// Find all API route files
async function findApiRoutes() {
  const apiRoutes = await glob('app/api/**/route.ts', { cwd: process.cwd() });
  return apiRoutes;
}

// Check if a file uses cookies
function usesCookies(content) {
  return content.includes('from "next/headers"') && 
         (content.includes('cookies()') || content.includes('cookies '));
}

// Check if file already has dynamic export
function hasDynamicExport(content) {
  return content.includes('export const dynamic') || 
         content.includes('export const runtime');
}

// Add dynamic export to file
function addDynamicExport(content) {
  // Find the first import statement
  const lines = content.split('\n');
  let insertIndex = 0;
  
  // Look for the first import or find a good place to insert
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Skip comments and empty lines at the top
    if (line.startsWith('//') || line.startsWith('/*') || line === '') {
      continue;
    }
    
    // If we find an import, insert before it
    if (line.startsWith('import ')) {
      insertIndex = i;
      break;
    }
    
    // If we find an export, insert before it
    if (line.startsWith('export ')) {
      insertIndex = i;
      break;
    }
    
    // If we find a function or other code, insert before it
    if (line.length > 0) {
      insertIndex = i;
      break;
    }
  }
  
  // Insert the dynamic export
  lines.splice(insertIndex, 0, 'export const dynamic = "force-dynamic";', '');
  
  return lines.join('\n');
}

// Process a single file
function processFile(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ File not found: ${filePath}`);
      return false;
    }
    
    const content = fs.readFileSync(fullPath, 'utf8');
    
    // Check if file uses cookies
    if (!usesCookies(content)) {
      console.log(`⏭️  Skipping ${filePath} (doesn't use cookies)`);
      return false;
    }
    
    // Check if already has dynamic export
    if (hasDynamicExport(content)) {
      console.log(`✅ Already fixed: ${filePath}`);
      return false;
    }
    
    // Add dynamic export
    const updatedContent = addDynamicExport(content);
    
    // Write back to file
    fs.writeFileSync(fullPath, updatedContent, 'utf8');
    console.log(`🔧 Fixed: ${filePath}`);
    return true;
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
async function main() {
  console.log('🔍 Finding API routes that use cookies...\n');
  
  const apiRoutes = await findApiRoutes();
  console.log(`Found ${apiRoutes.length} API route files\n`);
  
  let fixedCount = 0;
  
  for (const route of apiRoutes) {
    if (processFile(route)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ Fixed ${fixedCount} API routes`);
  console.log('\n🚀 Now run "npm run build" to verify the fixes!');
}

// Run the script
main().catch(console.error);
