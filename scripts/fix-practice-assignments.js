#!/usr/bin/env node

/**
 * Fix Practice Assignments Table References
 * 
 * This script fixes all references from "practice_assignments" to "lesson_assignments"
 * and updates related column names to match the actual database schema.
 */

const fs = require('fs');
const path = require('path');

// File to fix
const filePath = 'lib/queries/practiceAssignmentQueries.ts';

function fixPracticeAssignments() {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ File not found: ${filePath}`);
      return false;
    }
    
    let content = fs.readFileSync(fullPath, 'utf8');
    const originalContent = content;
    
    // Replace all practice_assignments with lesson_assignments
    content = content.replace(/practice_assignments/g, 'lesson_assignments');
    
    // Replace child_id with student_profile_id (the correct column name in lesson_assignments)
    content = content.replace(/child_id/g, 'student_profile_id');
    
    // Fix specific field mappings
    content = content.replace(/assigned_by_type/g, 'status'); // lesson_assignments uses status instead
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`🔧 Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`✅ Already correct: ${filePath}`);
      return false;
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
async function main() {
  console.log('🔧 Fixing practice assignments table references...\n');
  
  if (fixPracticeAssignments()) {
    console.log('\n✨ Fixed practice assignments table references!');
  } else {
    console.log('\n⏭️  No changes needed');
  }
  
  console.log('\n🚀 Practice assignment queries should now use the correct database schema!');
}

// Run the script
main().catch(console.error);
