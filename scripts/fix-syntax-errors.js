#!/usr/bin/env node

/**
 * Fix Syntax Errors <PERSON>ript
 * 
 * This script fixes the specific syntax error where cookieStore declaration
 * was incorrectly placed in function parameters instead of function body.
 */

const fs = require('fs');
const path = require('path');

// List of files with syntax errors from the build output
const problematicFiles = [
  'app/api/children/[username]/practice/assignments/[id]/route.ts',
  'app/api/children/[username]/practice/assignments/route.ts',
  'app/api/children/[username]/practice/most-practiced/route.ts',
  'app/api/children/[username]/practice/route.ts',
  'app/api/children/[username]/songs/route.ts',
  'app/api/practice/[id]/route.ts',
  'app/api/setlists/[slug]/entries/route.ts',
  'app/api/setlists/[slug]/route.ts'
];

// Fix the specific pattern where cookieStore is in function parameters
function fixSyntaxError(content) {
  // Pattern: function(..., const cookieStore = await cookies();)
  // Should be: function(...) { const cookieStore = await cookies();
  
  // Fix the pattern where cookieStore declaration is in parameters
  content = content.replace(
    /(\w+)\s*:\s*\{\s*params:\s*Promise<[^>]+>\s*\}\s*,\s*const cookieStore = await cookies\(\);\s*\)\s*\{/g,
    '$1: { params: Promise<{ username: string }> },\n) {\n  const cookieStore = await cookies();'
  );
  
  // More specific pattern for different parameter types
  content = content.replace(
    /(\w+)\s*:\s*\{\s*params:\s*Promise<[^>]+>\s*\}\s*,\s*const cookieStore = await cookies\(\);\s*\)\s*\{/g,
    '$1: { params: Promise<any> },\n) {\n  const cookieStore = await cookies();'
  );
  
  // Handle the case where it's at the end of parameters
  content = content.replace(
    /,\s*const cookieStore = await cookies\(\);\s*\)\s*\{/g,
    ',\n) {\n  const cookieStore = await cookies();'
  );
  
  return content;
}

// More comprehensive fix using regex to handle various parameter patterns
function fixParameterSyntax(content) {
  const lines = content.split('\n');
  let modified = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Look for the problematic pattern
    if (line.includes('const cookieStore = await cookies();') && 
        (line.includes('{ params }') || line.includes('params:'))) {
      
      // Find the function declaration start
      let functionStart = i;
      while (functionStart > 0 && !lines[functionStart].includes('export async function')) {
        functionStart--;
      }
      
      // Find the opening brace
      let braceIndex = i;
      while (braceIndex < lines.length && !lines[braceIndex].includes(') {')) {
        braceIndex++;
      }
      
      // Remove the cookieStore from parameters and add it after the opening brace
      lines[i] = line.replace(/,\s*const cookieStore = await cookies\(\);/, '');
      
      // If the line now ends with just the closing paren and brace, fix it
      if (lines[i].trim().endsWith(',')) {
        lines[i] = lines[i].replace(/,$/, '');
      }
      
      // Add the cookieStore declaration after the opening brace
      if (braceIndex < lines.length) {
        const nextLineIndex = braceIndex + 1;
        if (nextLineIndex < lines.length && !lines[nextLineIndex].includes('const cookieStore')) {
          lines.splice(nextLineIndex, 0, '    const cookieStore = await cookies();');
          modified = true;
        }
      }
    }
  }
  
  return lines.join('\n');
}

// Process a single file
function processFile(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ File not found: ${filePath}`);
      return false;
    }
    
    const originalContent = fs.readFileSync(fullPath, 'utf8');
    let content = originalContent;
    
    // Apply fixes
    content = fixSyntaxError(content);
    content = fixParameterSyntax(content);
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`🔧 Fixed syntax error in: ${filePath}`);
      return true;
    } else {
      console.log(`✅ No syntax errors found in: ${filePath}`);
      return false;
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function
async function main() {
  console.log('🔧 Fixing syntax errors in API routes...\n');
  
  let fixedCount = 0;
  
  for (const file of problematicFiles) {
    if (processFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ Fixed syntax errors in ${fixedCount} files`);
  console.log('\n🚀 Now run "npm run build" to verify the fixes!');
}

// Run the script
main().catch(console.error);
