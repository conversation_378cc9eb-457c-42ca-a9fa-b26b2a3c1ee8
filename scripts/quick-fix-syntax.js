#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const files = [
  'app/api/children/[username]/practice/most-practiced/route.ts',
  'app/api/children/[username]/practice/route.ts', 
  'app/api/children/[username]/songs/route.ts',
  'app/api/practice/[id]/route.ts',
  'app/api/setlists/[slug]/entries/route.ts',
  'app/api/setlists/[slug]/route.ts'
];

files.forEach(filePath => {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ File not found: ${filePath}`);
      return;
    }
    
    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Fix the syntax error pattern
    content = content.replace(
      /(\{ params \}: \{ params: Promise<[^>]+> \}),\s*const cookieStore = await cookies\(\);\s*\) \{/g,
      '$1,\n) {\n  const cookieStore = await cookies();'
    );
    
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`🔧 Fixed: ${filePath}`);
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
  }
});

console.log('✨ Done fixing syntax errors!');
