{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "target": "ES6", "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "forceConsistentCasingInFileNames": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}}, "include": ["app/**/*.ts", "app/**/*.tsx", "components/**/*.ts", "components/**/*.tsx", "lib/**/*.ts", "lib/**/*.tsx", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "tests", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "type-errors.txt", "scripts/**/*", "migrations/**/*", "deviceapps-standalone/**/*", "refactor-setlist.js", "clean-dev.js"]}