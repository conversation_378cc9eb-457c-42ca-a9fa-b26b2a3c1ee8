app/api/children/[username]/curriculum/route.ts(28,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/[username]/curriculum/route.ts(131,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/[username]/events/route.ts(33,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/[username]/events/route.ts(156,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/[username]/events/route.ts(284,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/[username]/practice/assignments/[id]/route.ts(24,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/[username]/practice/assignments/[id]/route.ts(118,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/[username]/practice/assignments/[id]/route.ts(223,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/[username]/practice/assignments/route.ts(25,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/[username]/practice/assignments/route.ts(146,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/[username]/practice/most-practiced/route.ts(16,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/[username]/practice/route.ts(31,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/[username]/practice/stats/route.ts(22,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/login-requests/deny/route.ts(26,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/login-requests/route.ts(22,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/route.ts(23,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/children/username/[username]/route.ts(22,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/deviceapps/children/record-login/route.ts(62,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/deviceapps/children/verify-shared-code/route.ts(4,23): error TS1378: Top-level 'await' expressions are only allowed when the 'module' option is set to 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext', or 'preserve', and the 'target' option is set to 'es2017' or higher.
app/api/deviceapps/test/create-check-table-exists-function/route.ts(20,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/deviceapps/test/create-test-child/route.ts(24,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/gear/[id]/favourite/route.ts(56,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/gear/[id]/route.ts(27,22): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/gear/[id]/route.ts(116,22): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/gear/[id]/route.ts(278,22): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/gear/[id]/subitems/route.ts(28,22): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/gear/brands/route.ts(159,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/gear/route.ts(25,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/gear/route.ts(111,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/gear/test/route.ts(34,66): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/genres/route.ts(10,62): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/practice/[id]/route.ts(18,62): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/practice/[id]/route.ts(46,62): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/practice/[id]/route.ts(76,62): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/practice/route.ts(28,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/practice/route.ts(119,62): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/practice/stats/route.ts(14,62): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/profiles/[id]/route.ts(23,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/profiles/[id]/route.ts(94,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/setlists/[slug]/entries/[entryId]/route.ts(14,62): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/setlists/[slug]/entries/[entryId]/route.ts(48,62): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/setlists/[slug]/entries/route.ts(14,62): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/setlists/[slug]/entries/route.ts(49,62): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/setlists/[slug]/route.ts(123,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/songs/[id]/route.ts(32,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/songs/route.ts(53,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/spotify/env-check/route.ts(11,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/spotify/test/route.ts(12,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/spotify/test/route.ts(54,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/spotify/track/[id]/route.ts(25,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/transactions/route.ts(11,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/api/transactions/route.ts(82,64): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
app/parent/add-child-form.tsx(191,17): error TS2322: Type 'Dispatch<SetStateAction<Date | undefined>>' is not assignable to type '(date: Date | null) => void'.
  Types of parameters 'value' and 'date' are incompatible.
    Type 'Date | null' is not assignable to type 'SetStateAction<Date | undefined>'.
      Type 'null' is not assignable to type 'SetStateAction<Date | undefined>'.
app/parent/new-add-child-form.tsx(157,17): error TS2322: Type '{ mode: string; selected: Date | undefined; onSelect: Dispatch<SetStateAction<Date | undefined>>; disabled: (date: Date) => boolean; initialFocus: true; captionLayout: string; fromYear: number; toYear: number; }' is not assignable to type 'IntrinsicAttributes & CalendarProps'.
  Property 'mode' does not exist on type 'IntrinsicAttributes & CalendarProps'.
app/practice/[id]/page.tsx(427,23): error TS2322: Type 'undefined' is not assignable to type 'string | number | null'.
app/practice/[id]/page.tsx(429,23): error TS2322: Type 'undefined' is not assignable to type 'string[]'.
components/auth/MusicLoadingMessage.tsx(218,7): error TS2322: Type '({ type: string; text: string; backgroundImage: string; logo: string; } | { lyric: string; song_title: string; composer: string; artist: string; year: number; album: string; type: string; })[]' is not assignable to type '(LyricItem | TransitionItem)[]'.
  Type '{ type: string; text: string; backgroundImage: string; logo: string; } | { lyric: string; song_title: string; composer: string; artist: string; year: number; album: string; type: string; }' is not assignable to type 'LyricItem | TransitionItem'.
    Type '{ type: string; text: string; backgroundImage: string; logo: string; }' is not assignable to type 'LyricItem | TransitionItem'.
      Type '{ type: string; text: string; backgroundImage: string; logo: string; }' is not assignable to type 'TransitionItem'.
        Types of property 'type' are incompatible.
          Type 'string' is not assignable to type '"transition"'.
components/calendar/event-form.tsx(271,23): error TS2769: No overload matches this call.
  Overload 1 of 2, '(values: { amount: number; created_at?: string | null | undefined; description?: string | null | undefined; equipment_associated?: Json | undefined; event_associated?: string | null | undefined; ... 7 more ...; uuid?: string | undefined; }, options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Argument of type '{ profile_id: string; transaction_type: any; amount: any; description: any; transaction_date: any; event_associated: number | null; equipment_associated: any; metadata: { category: any; }; }' is not assignable to parameter of type '{ amount: number; created_at?: string | null | undefined; description?: string | null | undefined; equipment_associated?: Json | undefined; event_associated?: string | null | undefined; ... 7 more ...; uuid?: string | undefined; }'.
      Types of property 'event_associated' are incompatible.
        Type 'number | null' is not assignable to type 'string | null | undefined'.
          Type 'number' is not assignable to type 'string'.
  Overload 2 of 2, '(values: { amount: number; created_at?: string | null | undefined; description?: string | null | undefined; equipment_associated?: Json | undefined; event_associated?: string | null | undefined; ... 7 more ...; uuid?: string | undefined; }[], options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Argument of type '{ profile_id: string; transaction_type: any; amount: any; description: any; transaction_date: any; event_associated: number | null; equipment_associated: any; metadata: { category: any; }; }' is not assignable to parameter of type '{ amount: number; created_at?: string | null | undefined; description?: string | null | undefined; equipment_associated?: Json | undefined; event_associated?: string | null | undefined; ... 7 more ...; uuid?: string | undefined; }[]'.
      Type '{ profile_id: string; transaction_type: any; amount: any; description: any; transaction_date: any; event_associated: number | null; equipment_associated: any; metadata: { category: any; }; }' is missing the following properties from type '{ amount: number; created_at?: string | null | undefined; description?: string | null | undefined; equipment_associated?: Json | undefined; event_associated?: string | null | undefined; ... 7 more ...; uuid?: string | undefined; }[]': length, pop, push, concat, and 35 more.
components/children/child-form.tsx(471,19): error TS2322: Type '{ mode: string; selected: Date | undefined; onSelect: Dispatch<SetStateAction<Date | undefined>>; initialFocus: true; disabled: (date: Date) => boolean; }' is not assignable to type 'IntrinsicAttributes & CalendarProps'.
  Property 'mode' does not exist on type 'IntrinsicAttributes & CalendarProps'.
components/children/children-list.tsx(17,9): error TS2322: Type '{ username: string; bio: string; avatar_url: string | null; color_scheme: string | null; created_at: string | null; dob: string; id: string; instruments: string[] | null; is_active: boolean | null; ... 8 more ...; user_id: string; }[]' is not assignable to type 'Child[]'.
  Type '{ username: string; bio: string; avatar_url: string | null; color_scheme: string | null; created_at: string | null; dob: string; id: string; instruments: string[] | null; is_active: boolean | null; ... 8 more ...; user_id: string; }' is not assignable to type 'Child'.
    Types of property 'avatar_url' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
components/children/device-details.tsx(66,36): error TS2339: Property 'name' does not exist on type 'SelectQueryError<"column 'last_login_at' does not exist on 'children'.">'.
components/children/device-details.tsx(67,36): error TS2339: Property 'last_login_at' does not exist on type 'SelectQueryError<"column 'last_login_at' does not exist on 'children'.">'.
components/children/device-details.tsx(68,37): error TS2339: Property 'last_login_device' does not exist on type 'SelectQueryError<"column 'last_login_at' does not exist on 'children'.">'.
components/curriculum/lesson-plan-editor.tsx(92,25): error TS2367: This comparison appears to be unintentional because the types 'string' and 'number' have no overlap.
components/curriculum/lesson-plan-editor.tsx(107,39): error TS2339: Property 'description' does not exist on type '{ content: Json; created_at: string | null; curriculum_id: number; id: number; metadata: Json; order_index: number | null; title: string; }'.
components/curriculum/lesson-plan-editor.tsx(108,44): error TS2339: Property 'duration_minutes' does not exist on type '{ content: Json; created_at: string | null; curriculum_id: number; id: number; metadata: Json; order_index: number | null; title: string; }'.
components/curriculum/lesson-plan-editor.tsx(141,9): error TS2353: Object literal may only specify known properties, and 'duration_minutes' does not exist in type '{ content?: Json | undefined; created_at?: string | null | undefined; curriculum_id?: number | undefined; id?: number | undefined; metadata?: Json | undefined; order_index?: number | ... 1 more ... | undefined; title?: string | undefined; }'.
components/curriculum/lesson-plan-editor.tsx(176,9): error TS2322: Type 'string' is not assignable to type 'number'.
components/curriculum/lesson-plan-editor.tsx(208,34): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
components/curriculum/lesson-plan-editor.tsx(210,31): error TS2367: This comparison appears to be unintentional because the types 'number' and 'string' have no overlap.
components/curriculum/lesson-plan-editor.tsx(244,43): error TS2353: Object literal may only specify known properties, and 'display_order' does not exist in type '{ created_at?: string | null | undefined; id?: number | undefined; lesson_plan_id?: number | undefined; notes?: string | null | undefined; song_id?: number | undefined; }'.
components/curriculum/lesson-plan-editor.tsx(342,25): error TS2339: Property 'duration_minutes' does not exist on type '{ content: Json; created_at: string | null; curriculum_id: number; id: number; metadata: Json; order_index: number | null; title: string; }'.
components/curriculum/lesson-plan-editor.tsx(345,35): error TS2339: Property 'duration_minutes' does not exist on type '{ content: Json; created_at: string | null; curriculum_id: number; id: number; metadata: Json; order_index: number | null; title: string; }'.
components/curriculum/lesson-plan-editor.tsx(349,25): error TS2339: Property 'description' does not exist on type '{ content: Json; created_at: string | null; curriculum_id: number; id: number; metadata: Json; order_index: number | null; title: string; }'.
components/curriculum/lesson-plan-editor.tsx(352,31): error TS2339: Property 'description' does not exist on type '{ content: Json; created_at: string | null; curriculum_id: number; id: number; metadata: Json; order_index: number | null; title: string; }'.
components/curriculum/lesson-plan-editor.tsx(451,57): error TS2345: Argument of type 'number' is not assignable to parameter of type 'string'.
components/debug/EnhancedTableTester.tsx(105,33): error TS7015: Element implicitly has an 'any' type because index expression is not of type 'number'.
components/deviceapps/child-selection.tsx(95,29): error TS2352: Conversion of type 'SelectQueryError<"Could not embed because more than one relationship was found for 'profiles' and 'parent_child_links' you need to hint the column with profiles!<columnName> ?">' to type 'Child' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ error: true; } & String' is missing the following properties from type 'Child': id, username, name
components/deviceapps/profile-switcher.tsx(93,29): error TS2352: Conversion of type 'SelectQueryError<"Could not embed because more than one relationship was found for 'profiles' and 'parent_child_links' you need to hint the column with profiles!<columnName> ?">' to type 'Child' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ error: true; } & String' is missing the following properties from type 'Child': id, username, name
components/finances/money-dashboard.tsx(199,29): error TS2339: Property 'notes' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'notes' does not exist on type 'string'.
components/finances/money-dashboard.tsx(200,32): error TS2339: Property 'priority' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'priority' does not exist on type 'string'.
components/finances/money-dashboard.tsx(201,35): error TS2339: Property 'target_date' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'target_date' does not exist on type 'string'.
components/finances/money-dashboard.tsx(202,29): error TS2339: Property 'color' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'color' does not exist on type 'string'.
components/finances/money-dashboard.tsx(237,49): error TS2345: Argument of type '{ profile_id: string; name: string; target_amount: number; current_amount: number; gear_id: number | null; metadata: { notes: string; priority: "high" | "low" | "medium"; target_date: string; color: string; }; }' is not assignable to parameter of type '{ created_at?: string | undefined; current_amount?: number | undefined; gear_id?: string | null | undefined; id?: string | undefined; metadata?: Json | undefined; name?: string | undefined; profile_id?: string | undefined; target_amount?: number | undefined; updated_at?: string | undefined; }'.
  Types of property 'gear_id' are incompatible.
    Type 'number | null' is not assignable to type 'string | null | undefined'.
      Type 'number' is not assignable to type 'string'.
components/finances/money-dashboard.tsx(245,33): error TS2345: Argument of type '{ profile_id: string; name: string; target_amount: number; current_amount: number; gear_id: number | null; metadata: { notes: string; priority: "high" | "low" | "medium"; target_date: string; color: string; }; }' is not assignable to parameter of type '{ created_at?: string | undefined; current_amount?: number | undefined; gear_id?: string | null | undefined; id?: string | undefined; metadata?: Json | undefined; name: string; profile_id: string; target_amount: number; updated_at?: string | undefined; }'.
  Types of property 'gear_id' are incompatible.
    Type 'number | null' is not assignable to type 'string | null | undefined'.
      Type 'number' is not assignable to type 'string'.
components/finances/money-dashboard.tsx(561,61): error TS2339: Property 'color' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'color' does not exist on type 'string'.
components/finances/money-dashboard.tsx(565,41): error TS2339: Property 'priority' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'priority' does not exist on type 'string'.
components/finances/money-dashboard.tsx(601,44): error TS2339: Property 'color' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'color' does not exist on type 'string'.
components/finances/money-dashboard.tsx(614,37): error TS2339: Property 'target_date' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'target_date' does not exist on type 'string'.
components/finances/money-dashboard.tsx(618,41): error TS2339: Property 'target_date' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'target_date' does not exist on type 'string'.
components/finances/simplified-transaction-detail.tsx(141,23): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
components/finances/simplified-transaction-form.tsx(287,21): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
components/finances/transaction-detail.tsx(134,23): error TS2345: Argument of type 'number' is not assignable to parameter of type 'string'.
components/gear/gear-card.tsx(153,28): error TS2345: Argument of type 'number' is not assignable to parameter of type 'string'.
components/gear/gear-detail-new.tsx(228,17): error TS2345: Argument of type 'number' is not assignable to parameter of type 'string'.
components/gear/gear-detail-new.tsx(431,47): error TS2345: Argument of type 'number' is not assignable to parameter of type 'string'.
components/gear/gear-form-new.tsx(375,32): error TS2339: Property 'profile_id' does not exist on type '{ size?: string | undefined; name: string; notes?: string | undefined; is_active: boolean; description?: string | undefined; metadata?: Record<string, any> | undefined; brand_id?: number | undefined; ... 20 more ...; subcategoryName?: string | undefined; }'.
components/gear/gear-form.tsx(164,38): error TS2339: Property 'subcategories' does not exist on type 'GearCategory'.
components/gear/gear-form.tsx(165,12): error TS7006: Parameter 's' implicitly has an 'any' type.
components/gear/gear-form.tsx(288,30): error TS2345: Argument of type 'number' is not assignable to parameter of type 'string'.
components/gear/gear-form.tsx(409,21): error TS2322: Type 'Date | undefined' is not assignable to type 'Date | null'.
  Type 'undefined' is not assignable to type 'Date | null'.
components/gear/gear-image-uploader.tsx(86,11): error TS2345: Argument of type 'number | undefined' is not assignable to parameter of type 'string | undefined'.
  Type 'number' is not assignable to type 'string'.
components/practice/assignment/practice-assignment-form.tsx(312,15): error TS2322: Type 'Dispatch<SetStateAction<number | undefined>>' is not assignable to type '(id: string | number | null) => void'.
  Types of parameters 'value' and 'id' are incompatible.
    Type 'string | number | null' is not assignable to type 'SetStateAction<number | undefined>'.
      Type 'null' is not assignable to type 'SetStateAction<number | undefined>'.
components/practice/kids-practice-recorder.tsx(420,88): error TS2304: Cannot find name 'ColorScheme'.
components/practice/kids-practice-recorder.tsx(480,87): error TS2304: Cannot find name 'ColorScheme'.
components/practice/kids-practice-recorder.tsx(687,60): error TS2345: Argument of type 'Song[]' is not assignable to parameter of type 'SetStateAction<Song[]>'.
  Type 'import("/Users/<USER>/Utilities/april-09/components/practice/types").Song[]' is not assignable to type 'import("/Users/<USER>/Utilities/april-09/lib/queries/songQueries").Song[]'.
    Type 'Song' is missing the following properties from type 'Song': source_type, notes, tags, is_public
components/practice/kids-practice-recorder.tsx(744,87): error TS2304: Cannot find name 'ColorScheme'.
components/profile/ProfileForm.tsx(328,23): error TS2322: Type '{ mode: string; selected: Date | undefined; onSelect: (...event: any[]) => void; disabled: (date: Date) => boolean; initialFocus: true; }' is not assignable to type 'IntrinsicAttributes & CalendarProps'.
  Property 'mode' does not exist on type 'IntrinsicAttributes & CalendarProps'.
components/setlists/components/SetlistBuilder.tsx(54,38): error TS2339: Property 'duration' does not exist on type 'SetlistItemSong'.
components/setlists/components/SetlistBuilder.tsx(54,60): error TS2339: Property 'duration' does not exist on type 'SetlistItemSong'.
components/setlists/components/SetlistBuilder.tsx(57,16): error TS2367: This comparison appears to be unintentional because the types '"break" | "custom" | "speech"' and '"song"' have no overlap.
components/setlists/wizard/setlist-builder.tsx(81,36): error TS2551: Property 'duration_ms' does not exist on type 'Song'. Did you mean 'duration'?
components/setlists/wizard/setlist-builder.tsx(190,69): error TS2551: Property 'duration_ms' does not exist on type 'Song'. Did you mean 'duration'?
components/setlists/wizard/setlist-builder.tsx(191,73): error TS2551: Property 'duration_ms' does not exist on type 'Song'. Did you mean 'duration'?
components/setlists/wizard/setlist-builder.tsx(230,90): error TS2551: Property 'duration_ms' does not exist on type 'Song'. Did you mean 'duration'?
components/setlists/wizard/setlist-builder.tsx(238,49): error TS2322: Type '{ duration_ms: number; id: string; title: string; artist?: string | undefined; album?: string | undefined; duration: number; genre?: string | undefined; year?: number | undefined; imageUrl?: string | undefined; ... 10 more ...; defaultLineup?: string | undefined; } | undefined' is not assignable to type 'Song | undefined'.
  Object literal may only specify known properties, but 'duration_ms' does not exist in type 'Song'. Did you mean to write 'duration'?
components/setlists/wizard/setlist-generator.tsx(302,47): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
components/setlists/wizard/setlist-wizard.tsx(161,27): error TS2551: Property 'duration_ms' does not exist on type 'Song'. Did you mean 'duration'?
components/setlists/wizard/setlist-wizard.tsx(174,22): error TS2551: Property 'duration_ms' does not exist on type 'Song'. Did you mean 'duration'?
components/setlists/wizard/setlist-wizard.tsx(319,28): error TS2339: Property 'id' does not exist on type 'Setlist | { error: string; }'.
  Property 'id' does not exist on type '{ error: string; }'.
components/setlists/wizard/setlist-wizard.tsx(324,41): error TS2339: Property 'slug' does not exist on type 'Setlist | { error: string; }'.
  Property 'slug' does not exist on type '{ error: string; }'.
components/setlists/wizard/setlist-wizard.tsx(324,56): error TS2339: Property 'id' does not exist on type 'Setlist | { error: string; }'.
  Property 'id' does not exist on type '{ error: string; }'.
components/setlists/wizard/setlist-wizard.tsx(325,35): error TS2339: Property 'error' does not exist on type 'Setlist | { error: string; }'.
  Property 'error' does not exist on type 'Setlist'.
components/setlists/wizard/setlist-wizard.tsx(326,57): error TS2339: Property 'error' does not exist on type 'Setlist | { error: string; }'.
  Property 'error' does not exist on type 'Setlist'.
components/setlists/wizard/setlist-wizard.tsx(327,55): error TS2339: Property 'error' does not exist on type 'Setlist | { error: string; }'.
  Property 'error' does not exist on type 'Setlist'.
components/settings/child-practice-goals.tsx(442,69): error TS2304: Cannot find name 'saving'.
components/settings/child-practice-goals.tsx(447,50): error TS2304: Cannot find name 'saving'.
components/settings/child-practice-goals.tsx(449,14): error TS2304: Cannot find name 'saving'.
components/settings/child-role-settings.tsx(135,13): error TS2322: Type 'string[]' is not assignable to type 'UserRoleType[]'.
  Type 'string' is not assignable to type 'UserRoleType'.
components/settings/child-role-settings.tsx(234,9): error TS2322: Type 'string[]' is not assignable to type 'UserRoleType[]'.
  Type 'string' is not assignable to type 'UserRoleType'.
components/settings/instrument-competencies-improved.tsx(67,47): error TS2339: Property 'roles' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'roles' does not exist on type 'string'.
components/settings/instrument-competencies-improved.tsx(68,49): error TS2339: Property 'roles' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'roles' does not exist on type 'string'.
components/settings/instrument-competencies-improved.tsx(69,47): error TS2339: Property 'roles' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'roles' does not exist on type 'string'.
components/settings/instrument-competencies-local.tsx(81,34): error TS2551: Property 'roles' does not exist on type '{ age: number | null; auth_method: string | null; avatar: string | null; avatar_url: string | null; bio: string | null; color_scheme: string | null; created_at: string | null; display_name: string | null; ... 14 more ...; username: string | null; }'. Did you mean 'role'?
components/settings/instrument-competencies-local.tsx(82,36): error TS2551: Property 'roles' does not exist on type '{ age: number | null; auth_method: string | null; avatar: string | null; avatar_url: string | null; bio: string | null; color_scheme: string | null; created_at: string | null; display_name: string | null; ... 14 more ...; username: string | null; }'. Did you mean 'role'?
components/settings/instrument-competencies-local.tsx(83,34): error TS2551: Property 'roles' does not exist on type '{ age: number | null; auth_method: string | null; avatar: string | null; avatar_url: string | null; bio: string | null; color_scheme: string | null; created_at: string | null; display_name: string | null; ... 14 more ...; username: string | null; }'. Did you mean 'role'?
components/settings/instrument-competencies-table.tsx(149,18): error TS2345: Argument of type 'UserInstrument & { profileName: string; childId?: string | undefined; }' is not assignable to parameter of type 'SetStateAction<{ id: string; childId?: string | undefined; metadata: any; profileName: string; } | null>'.
  Type 'UserInstrument & { profileName: string; childId?: string | undefined; }' is not assignable to type '{ id: string; childId?: string | undefined; metadata: any; profileName: string; }'.
    Property 'metadata' is optional in type 'UserInstrument & { profileName: string; childId?: string | undefined; }' but required in type '{ id: string; childId?: string | undefined; metadata: any; profileName: string; }'.
components/settings/instrument-competencies-table.tsx(151,70): error TS2345: Argument of type 'UserInstrument & { profileName: string; childId?: string | undefined; }' is not assignable to parameter of type 'SetStateAction<{ id: string; childId?: string | undefined; metadata: any; profileName: string; } | null>'.
  Type 'UserInstrument & { profileName: string; childId?: string | undefined; }' is not assignable to type '{ id: string; childId?: string | undefined; metadata: any; profileName: string; }'.
    Property 'metadata' is optional in type 'UserInstrument & { profileName: string; childId?: string | undefined; }' but required in type '{ id: string; childId?: string | undefined; metadata: any; profileName: string; }'.
components/settings/instrument-competencies-table.tsx(153,20): error TS2345: Argument of type 'UserInstrument & { profileName: string; childId?: string | undefined; }' is not assignable to parameter of type 'SetStateAction<{ id: string; childId?: string | undefined; metadata: any; profileName: string; } | null>'.
  Type 'UserInstrument & { profileName: string; childId?: string | undefined; }' is not assignable to type '{ id: string; childId?: string | undefined; metadata: any; profileName: string; }'.
    Property 'metadata' is optional in type 'UserInstrument & { profileName: string; childId?: string | undefined; }' but required in type '{ id: string; childId?: string | undefined; metadata: any; profileName: string; }'.
components/settings/instrument-competencies-table.tsx(376,37): error TS2339: Property 'user_metadata' does not exist on type '{ avatar_url: string | null; created_at: string | null; email: string; id: string; is_admin: boolean | null; name: string | null; privacy_accepted_at: string | null; privacy_version: string | null; terms_accepted_at: string | null; terms_version: string | null; updated_at: string | null; }'.
components/settings/instrument-competencies-table.tsx(407,29): error TS2339: Property 'instrument_id' does not exist on type '{ id: string; childId?: string | undefined; metadata: any; profileName: string; }'.
components/settings/instrument-competencies-table.tsx(465,29): error TS2339: Property 'instrument_id' does not exist on type '{ id: string; childId?: string | undefined; metadata: any; profileName: string; }'.
components/settings/instrument-competencies.tsx(49,34): error TS2551: Property 'roles' does not exist on type '{ age: number | null; auth_method: string | null; avatar: string | null; avatar_url: string | null; bio: string | null; color_scheme: string | null; created_at: string | null; display_name: string | null; ... 14 more ...; username: string | null; }'. Did you mean 'role'?
components/settings/instrument-competencies.tsx(50,36): error TS2551: Property 'roles' does not exist on type '{ age: number | null; auth_method: string | null; avatar: string | null; avatar_url: string | null; bio: string | null; color_scheme: string | null; created_at: string | null; display_name: string | null; ... 14 more ...; username: string | null; }'. Did you mean 'role'?
components/settings/instrument-competencies.tsx(51,34): error TS2551: Property 'roles' does not exist on type '{ age: number | null; auth_method: string | null; avatar: string | null; avatar_url: string | null; bio: string | null; color_scheme: string | null; created_at: string | null; display_name: string | null; ... 14 more ...; username: string | null; }'. Did you mean 'role'?
components/songs/create-song-form.tsx(146,32): error TS2304: Cannot find name 'SongCreationData'.
components/songs/manual-song-form.tsx(21,23): error TS2304: Cannot find name 'Song'.
components/songs/manual-song-form.tsx(127,29): error TS2554: Expected 2 arguments, but got 1.
components/songs/manual-song-form.tsx(222,28): error TS2339: Property 'difficulty' does not exist on type 'FormData'.
components/songs/manual-song-form.tsx(228,21): error TS2339: Property 'difficulty' does not exist on type 'FormData'.
components/songs/manual-song-form.tsx(237,41): error TS2339: Property 'tags' does not exist on type 'FormData'.
components/songs/manual-song-form.tsx(237,58): error TS2339: Property 'tags' does not exist on type 'FormData'.
components/songs/manual-song-form.tsx(249,27): error TS2339: Property 'notes' does not exist on type 'FormData'.
components/songs/song-detail.tsx(468,61): error TS2345: Argument of type 'string | null | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
components/songs/song-detail.tsx(470,49): error TS2339: Property 'name' does not exist on type 'Setlist'.
components/songs/song-list.tsx(57,35): error TS2304: Cannot find name 'currentUser'.
components/songs/song-list.tsx(287,65): error TS2345: Argument of type 'string | null | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
components/songs/song-list.tsx(289,53): error TS2339: Property 'name' does not exist on type 'Setlist'.
components/ui/calendar.tsx(91,9): error TS2322: Type '(newValue: Date | Date[] | null) => void' is not assignable to type '(value: Value, event: MouseEvent<HTMLButtonElement, MouseEvent>) => void'.
  Types of parameters 'newValue' and 'value' are incompatible.
    Type 'Value' is not assignable to type 'Date | Date[] | null'.
      Type 'Range<ValuePiece>' is not assignable to type 'Date | Date[] | null'.
        Type '[ValuePiece, ValuePiece]' is not assignable to type 'Date[]'.
          Type 'ValuePiece' is not assignable to type 'Date'.
            Type 'null' is not assignable to type 'Date'.
components/ui/formatted-otp-input.tsx(25,5): error TS2322: Type '{ is?: string | undefined; type?: HTMLInputTypeAttribute | undefined; id?: string | undefined; title?: string | undefined; children?: undefined; name?: string | undefined; ... 310 more ...; transform: (input: string) => string; } | { ...; }' is not assignable to type 'IntrinsicAttributes & (OTPInputProps & RefAttributes<HTMLInputElement>)'.
  Type '{ is?: string | undefined; type?: HTMLInputTypeAttribute | undefined; id?: string | undefined; title?: string | undefined; children?: undefined; name?: string | undefined; ... 310 more ...; transform: (input: string) => string; }' is not assignable to type 'IntrinsicAttributes & (OTPInputProps & RefAttributes<HTMLInputElement>)'.
    Property 'transform' does not exist on type 'IntrinsicAttributes & Omit<InputHTMLAttributes<HTMLInputElement>, "value" | "onChange" | "maxLength" | "textAlign" | ... 4 more ... | "noScriptCSSFallback"> & { ...; } & { ...; } & RefAttributes<...>'.
components/ui/google-maps-search.tsx(72,12): error TS2503: Cannot find namespace 'google'.
components/ui/google-maps-search.tsx(73,32): error TS2503: Cannot find namespace 'google'.
components/ui/google-maps-search.tsx(176,17): error TS2304: Cannot find name 'google'.
components/ui/google-maps-search.tsx(186,39): error TS2304: Cannot find name 'google'.
components/ui/google-maps-search.tsx(196,14): error TS7006: Parameter 'predictions' implicitly has an 'any' type.
components/ui/google-maps-search.tsx(196,27): error TS7006: Parameter 'status' implicitly has an 'any' type.
components/ui/google-maps-search.tsx(216,12): error TS2304: Cannot find name 'currentUser'.
components/ui/google-maps-search.tsx(223,26): error TS2304: Cannot find name 'currentUser'.
components/ui/google-maps-search.tsx(237,7): error TS2304: Cannot find name 'currentUser'.
components/ui/google-maps-search.tsx(278,14): error TS7006: Parameter 'predictions' implicitly has an 'any' type.
components/ui/google-maps-search.tsx(278,27): error TS7006: Parameter 'status' implicitly has an 'any' type.
components/ui/google-maps-search.tsx(283,28): error TS2304: Cannot find name 'google'.
components/ui/google-maps-search.tsx(323,37): error TS7006: Parameter 'status' implicitly has an 'any' type.
components/ui/google-maps-search.tsx(324,26): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(41,34): error TS2503: Cannot find namespace 'google'.
components/ui/google-maps-with-autocomplete.tsx(42,40): error TS2503: Cannot find namespace 'google'.
components/ui/google-maps-with-autocomplete.tsx(48,49): error TS2503: Cannot find namespace 'google'.
components/ui/google-maps-with-autocomplete.tsx(63,29): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(65,36): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(74,10): error TS7006: Parameter 'component' implicitly has an 'any' type.
components/ui/google-maps-with-autocomplete.tsx(79,31): error TS7006: Parameter 'component' implicitly has an 'any' type.
components/ui/google-maps-with-autocomplete.tsx(83,31): error TS7006: Parameter 'component' implicitly has an 'any' type.
components/ui/google-maps-with-autocomplete.tsx(87,31): error TS7006: Parameter 'component' implicitly has an 'any' type.
components/ui/google-maps-with-autocomplete.tsx(124,23): error TS2503: Cannot find namespace 'google'.
components/ui/google-maps-with-autocomplete.tsx(132,24): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(148,38): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(163,9): error TS2503: Cannot find namespace 'google'.
components/ui/google-maps-with-autocomplete.tsx(164,9): error TS2503: Cannot find namespace 'google'.
components/ui/google-maps-with-autocomplete.tsx(166,9): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(166,31): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(168,23): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(176,23): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(180,20): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(196,36): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(199,34): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(218,32): error TS2304: Cannot find name 'google'.
components/ui/google-maps-with-autocomplete.tsx(219,53): error TS7006: Parameter 'results' implicitly has an 'any' type.
components/ui/google-maps-with-autocomplete.tsx(219,62): error TS7006: Parameter 'status' implicitly has an 'any' type.
components/ui/google-maps-with-autocomplete.tsx(221,26): error TS2304: Cannot find name 'google'.
lib/actions/setlist-wizard-actions.ts(72,49): error TS2339: Property 'entries' does not exist on type 'ReadonlyRequestCookies'.
lib/actions/setlist-wizard-actions.ts(73,12): error TS2345: Argument of type '([name, value]: [any, any]) => string' is not assignable to parameter of type '(value: unknown, index: number, array: unknown[]) => string'.
  Types of parameters '__0' and 'value' are incompatible.
    Type 'unknown' is not assignable to type '[any, any]'.
lib/actions/setlist-wizard-actions.ts(104,11): error TS2322: Type '{ id: string; title: string; artist: string | null | undefined; album: string | null | undefined; duration: number; genre: string | undefined; year: number | undefined; imageUrl: string | null | undefined; ... 10 more ...; defaultLineup: string; }[]' is not assignable to type 'Song[]'.
  Type '{ id: string; title: string; artist: string | null | undefined; album: string | null | undefined; duration: number; genre: string | undefined; year: number | undefined; imageUrl: string | null | undefined; ... 10 more ...; defaultLineup: string; }' is not assignable to type 'Song'.
    Types of property 'artist' are incompatible.
      Type 'string | null | undefined' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
lib/actions/setlist-wizard-actions.ts(213,13): error TS2322: Type '"other" | "song" | "break" | "speech"' is not assignable to type '"song" | "break" | "custom" | "speech"'.
  Type '"other"' is not assignable to type '"song" | "break" | "custom" | "speech"'.
lib/api-helpers.ts(11,20): error TS2739: Type 'ReadonlyRequestCookies' is missing the following properties from type 'Promise<ReadonlyRequestCookies>': then, catch, finally, [Symbol.toStringTag]
lib/constants/colors.ts(22,3): error TS1117: An object literal cannot have multiple properties with the same name.
lib/constants/colors.ts(24,3): error TS1117: An object literal cannot have multiple properties with the same name.
lib/constants/colors.ts(25,3): error TS1117: An object literal cannot have multiple properties with the same name.
lib/constants/colors.ts(26,3): error TS1117: An object literal cannot have multiple properties with the same name.
lib/constants/colors.ts(27,3): error TS1117: An object literal cannot have multiple properties with the same name.
lib/constants/colors.ts(28,3): error TS1117: An object literal cannot have multiple properties with the same name.
lib/context/ProfileContext.tsx(18,38): error TS2304: Cannot find name 'ProfileContextType'.
lib/context/ProfileContext.tsx(220,27): error TS2339: Property 'id' does not exist on type '{} | { [key: string]: any; }'.
  Property 'id' does not exist on type '{}'.
lib/context/ProfileContext.tsx(224,63): error TS2339: Property 'metadata' does not exist on type '{} | { [key: string]: any; }'.
  Property 'metadata' does not exist on type '{}'.
lib/firebase/firebase.ts(47,33): error TS7006: Parameter 'sw' implicitly has an 'any' type.
lib/logs/index.ts(6,16): error TS2304: Cannot find name 'logger'.
lib/middleware/gear-middleware.ts(104,9): error TS2353: Object literal may only specify known properties, and 'user' does not exist in type 'GearAuthContext'.
lib/notifications/send-notification.ts(2,24): error TS7016: Could not find a declaration file for module 'nodemailer'. '/Users/<USER>/Utilities/april-09/node_modules/nodemailer/lib/nodemailer.js' implicitly has an 'any' type.
  Try `npm i --save-dev @types/nodemailer` if it exists or add a new declaration (.d.ts) file containing `declare module 'nodemailer';`
lib/notifications/send-notification.ts(122,7): error TS2588: Cannot assign to 'preferences' because it is a constant.
lib/notifications/send-notification.ts(190,22): error TS2339: Property 'email' does not exist on type '{ user: User; }'.
lib/notifications/send-notification.ts(261,23): error TS7034: Variable 'failedTokens' implicitly has type 'any[]' in some locations where its type cannot be determined.
lib/notifications/send-notification.ts(262,45): error TS7006: Parameter 'resp' implicitly has an 'any' type.
lib/notifications/send-notification.ts(262,51): error TS7006: Parameter 'idx' implicitly has an 'any' type.
lib/notifications/send-notification.ts(270,19): error TS7005: Variable 'failedTokens' implicitly has an 'any[]' type.
lib/notifications/send-notification.ts(274,37): error TS7005: Variable 'failedTokens' implicitly has an 'any[]' type.
lib/providers/genius-provider.ts(77,7): error TS2353: Object literal may only specify known properties, and 'song_art_image_url' does not exist in type 'SongSummary'.
lib/providers/genius-provider.ts(220,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/genius-provider.ts(221,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/genius-provider.ts(222,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/genius-provider.ts(223,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/genius-provider.ts(224,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/genius-provider.ts(225,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/genius-provider.ts(226,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/genius-provider.ts(227,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/genius-provider.ts(228,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/genius-provider.ts(229,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/genius-provider.ts(230,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/genius-provider.ts(231,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/genius-provider.ts(232,7): error TS2322: Type 'null' is not assignable to type 'number | undefined'.
lib/providers/spotify-provider.ts(66,31): error TS2339: Property 'id' does not exist on type '{ name: string; images: { url: string; height: number; width: number; }[]; release_date: string; }'.
lib/queries/childPracticeQueries.ts(45,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"child_practice_sessions"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"child_practice_sessions"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/instruments/childInstrumentQueries.ts(450,7): error TS2698: Spread types may only be created from object types.
lib/queries/instruments/userInstrumentQueries.ts(400,7): error TS2698: Spread types may only be created from object types.
lib/queries/moneyQueries.ts(49,17): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
lib/queries/moneyQueries.ts(92,17): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
lib/queries/moneyQueries.ts(118,20): error TS2345: Argument of type 'string' is not assignable to parameter of type 'number'.
lib/queries/practiceAssignmentQueries.ts(34,17): error TS2589: Type instantiation is excessively deep and possibly infinite.
lib/queries/practiceAssignmentQueries.ts(35,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(114,35): error TS2589: Type instantiation is excessively deep and possibly infinite.
lib/queries/practiceAssignmentQueries.ts(115,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(151,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(165,28): error TS2339: Property 'child_id' does not exist on type 'SelectQueryError<"column 'id' does not exist on 'setlists'."> | SelectQueryError<"column 'id' does not exist on 'users'."> | SelectQueryError<...> | ... 70 more ... | SelectQueryError<...>'.
  Property 'child_id' does not exist on type 'SelectQueryError<"column 'id' does not exist on 'setlists'.">'.
lib/queries/practiceAssignmentQueries.ts(176,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(207,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(221,28): error TS2339: Property 'child_id' does not exist on type 'SelectQueryError<"column 'id' does not exist on 'setlists'."> | SelectQueryError<"column 'id' does not exist on 'users'."> | SelectQueryError<...> | ... 70 more ... | SelectQueryError<...>'.
  Property 'child_id' does not exist on type 'SelectQueryError<"column 'id' does not exist on 'setlists'.">'.
lib/queries/practiceAssignmentQueries.ts(232,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(259,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(273,28): error TS2339: Property 'child_id' does not exist on type 'SelectQueryError<"column 'id' does not exist on 'setlists'."> | SelectQueryError<"column 'id' does not exist on 'users'."> | SelectQueryError<...> | ... 70 more ... | SelectQueryError<...>'.
  Property 'child_id' does not exist on type 'SelectQueryError<"column 'id' does not exist on 'setlists'.">'.
lib/queries/practiceAssignmentQueries.ts(284,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(315,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(326,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(339,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(352,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(365,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(376,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceAssignmentQueries.ts(405,25): error TS2339: Property 'type' does not exist on type 'SelectQueryError<"column 'type' does not exist on 'setlists'."> | SelectQueryError<"column 'type' does not exist on 'users'."> | ... 71 more ... | SelectQueryError<...>'.
  Property 'type' does not exist on type 'SelectQueryError<"column 'type' does not exist on 'setlists'.">'.
lib/queries/practiceAssignmentQueries.ts(415,31): error TS2339: Property 'assigned_by_type' does not exist on type 'SelectQueryError<"column 'assigned_by_type' does not exist on 'setlists'."> | SelectQueryError<"column 'assigned_by_type' does not exist on 'users'."> | ... 71 more ... | SelectQueryError<...>'.
  Property 'assigned_by_type' does not exist on type 'SelectQueryError<"column 'assigned_by_type' does not exist on 'setlists'.">'.
lib/queries/practiceAssignmentQueries.ts(442,35): error TS2589: Type instantiation is excessively deep and possibly infinite.
lib/queries/practiceAssignmentQueries.ts(443,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"practice_assignments"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/practiceQueries.ts(67,15): error TS2872: This kind of expression is always truthy.
lib/queries/practiceQueries.ts(105,29): error TS2339: Property 'song_id' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'song_id' does not exist on type 'string'.
lib/queries/practiceQueries.ts(132,44): error TS7006: Parameter 'session' implicitly has an 'any' type.
lib/queries/practiceQueries.ts(172,29): error TS2339: Property 'song_id' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'song_id' does not exist on type 'string'.
lib/queries/practiceQueries.ts(266,29): error TS2339: Property 'song_id' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'song_id' does not exist on type 'string'.
lib/queries/profileBasedPracticeQueries.ts(47,19): error TS2339: Property 'owner_id' does not exist on type 'SelectQueryError<"column 'owner_id' does not exist on 'profiles'.">'.
lib/queries/profileBasedPracticeQueries.ts(172,21): error TS2339: Property 'owner_id' does not exist on type 'SelectQueryError<"column 'owner_id' does not exist on 'profiles'.">'.
lib/queries/profileBasedPracticeQueries.ts(206,12): error TS2352: Conversion of type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }[]' to type 'PracticeSession[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }' is not comparable to type 'PracticeSession'.
    Types of property 'song' are incompatible.
      Type 'SelectQueryError<"could not find the relation between practice_sessions and song_id">' is not comparable to type 'Song | null | undefined'.
lib/queries/profileBasedPracticeQueries.ts(245,21): error TS2339: Property 'owner_id' does not exist on type 'SelectQueryError<"column 'owner_id' does not exist on 'profiles'.">'.
lib/queries/profileBasedPracticeQueries.ts(278,12): error TS2352: Conversion of type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }' to type 'PracticeSession' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Types of property 'song' are incompatible.
    Type 'SelectQueryError<"could not find the relation between practice_sessions and song_id">' is not comparable to type 'Song | null | undefined'.
lib/queries/profileBasedPracticeQueries.ts(318,21): error TS2339: Property 'owner_id' does not exist on type 'SelectQueryError<"column 'owner_id' does not exist on 'profiles'.">'.
lib/queries/profileBasedPracticeQueries.ts(391,9): error TS2698: Spread types may only be created from object types.
lib/queries/profileBasedPracticeQueries.ts(462,21): error TS2339: Property 'owner_id' does not exist on type 'SelectQueryError<"column 'owner_id' does not exist on 'profiles'.">'.
lib/queries/profileBasedPracticeQueries.ts(515,21): error TS2339: Property 'owner_id' does not exist on type 'SelectQueryError<"column 'owner_id' does not exist on 'profiles'.">'.
lib/queries/profileBasedPracticeQueries.ts(560,40): error TS2339: Property 'duration_minutes' does not exist on type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }'.
lib/queries/profileBasedPracticeQueries.ts(570,17): error TS2339: Property 'self_rating' does not exist on type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }'.
lib/queries/profileBasedPracticeQueries.ts(570,49): error TS2339: Property 'self_rating' does not exist on type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }'.
lib/queries/profileBasedPracticeQueries.ts(575,46): error TS2339: Property 'self_rating' does not exist on type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }'.
lib/queries/profileBasedPracticeQueries.ts(582,17): error TS2339: Property 'focus_rating' does not exist on type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }'.
lib/queries/profileBasedPracticeQueries.ts(582,50): error TS2339: Property 'focus_rating' does not exist on type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }'.
lib/queries/profileBasedPracticeQueries.ts(587,46): error TS2339: Property 'focus_rating' does not exist on type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }'.
lib/queries/profileBasedPracticeQueries.ts(594,23): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
lib/queries/profileBasedPracticeQueries.ts(654,29): error TS2769: No overload matches this call.
  Overload 1 of 4, '(value: string | number | Date): Date', gave the following error.
    Argument of type 'string | null' is not assignable to parameter of type 'string | number | Date'.
      Type 'null' is not assignable to type 'string | number | Date'.
  Overload 2 of 4, '(value: string | number): Date', gave the following error.
    Argument of type 'string | null' is not assignable to parameter of type 'string | number'.
      Type 'null' is not assignable to type 'string | number'.
lib/queries/profileBasedPracticeQueries.ts(658,37): error TS2769: No overload matches this call.
  Overload 1 of 4, '(value: string | number | Date): Date', gave the following error.
    Argument of type 'string | null' is not assignable to parameter of type 'string | number | Date'.
      Type 'null' is not assignable to type 'string | number | Date'.
  Overload 2 of 4, '(value: string | number): Date', gave the following error.
    Argument of type 'string | null' is not assignable to parameter of type 'string | number'.
      Type 'null' is not assignable to type 'string | number'.
lib/queries/profileBasedPracticeQueries.ts(659,48): error TS2339: Property 'duration_minutes' does not exist on type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }'.
lib/queries/profileBasedPracticeQueries.ts(667,19): error TS2339: Property 'song_id' does not exist on type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }'.
lib/queries/profileBasedPracticeQueries.ts(668,39): error TS2339: Property 'song_id' does not exist on type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }'.
lib/queries/profileBasedPracticeQueries.ts(672,33): error TS2339: Property 'title' does not exist on type 'SelectQueryError<"could not find the relation between practice_sessions and song_id">'.
lib/queries/profileBasedPracticeQueries.ts(673,25): error TS2339: Property 'song_id' does not exist on type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }'.
lib/queries/profileBasedQueries.ts(179,9): error TS2322: Type 'string | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
lib/queries/profileBasedQueries.ts(195,11): error TS2322: Type 'string[]' is not assignable to type 'InstrumentCapability[]'.
  Type 'string' is not assignable to type 'InstrumentCapability'.
lib/queries/profileBasedQueries.ts(196,11): error TS2322: Type 'string' is not assignable to type '"none" | "limited" | "full" | undefined'.
lib/queries/profileBasedQueries.ts(199,9): error TS2322: Type 'string | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
lib/queries/profileBasedQueries.ts(272,11): error TS2769: No overload matches this call.
  Overload 2 of 2, '(values: { avatar_url?: string | null | undefined; bio?: string | null | undefined; color_scheme?: string | null | undefined; created_at?: string | null | undefined; dob: string; id?: string | undefined; ... 11 more ...; username?: string | ... 1 more ... | undefined; }[], options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Object literal may only specify known properties, and 'id' does not exist in type '{ avatar_url?: string | null | undefined; bio?: string | null | undefined; color_scheme?: string | null | undefined; created_at?: string | null | undefined; dob: string; id?: string | undefined; ... 11 more ...; username?: string | ... 1 more ... | undefined; }[]'.
lib/queries/profileBasedQueries.ts(426,11): error TS2322: Type 'string | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
lib/queries/profileBasedQueries.ts(442,13): error TS2322: Type 'string[]' is not assignable to type 'InstrumentCapability[]'.
  Type 'string' is not assignable to type 'InstrumentCapability'.
lib/queries/profileBasedQueries.ts(443,13): error TS2322: Type 'string' is not assignable to type '"none" | "limited" | "full" | undefined'.
lib/queries/profileBasedQueries.ts(446,11): error TS2322: Type 'string | null' is not assignable to type 'string'.
  Type 'null' is not assignable to type 'string'.
lib/queries/profileBasedQueries.ts(599,7): error TS2322: Type '{ age: number | null; auth_method: string | null; avatar: string | null; avatar_url: string | null; bio: string | null; color_scheme: string | null; created_at: string | null; display_name: string | null; ... 14 more ...; username: string | null; }[]' is not assignable to type 'Profile[]'.
  Type '{ age: number | null; auth_method: string | null; avatar: string | null; avatar_url: string | null; bio: string | null; color_scheme: string | null; created_at: string | null; display_name: string | null; ... 14 more ...; username: string | null; }' is not assignable to type 'Profile'.
    Types of property 'username' are incompatible.
      Type 'string | null' is not assignable to type 'string'.
        Type 'null' is not assignable to type 'string'.
lib/queries/profileBasedQueries.ts(723,11): error TS2322: Type '{ instruments_count: number; practice_sessions_count: number; songs_count: number; age: number | null; auth_method: string | null; avatar: string | null; avatar_url: string | null; ... 18 more ...; username: string | null; }' is not assignable to type 'ProfileWithRelations'.
  Types of property 'username' are incompatible.
    Type 'string | null' is not assignable to type 'string'.
      Type 'null' is not assignable to type 'string'.
lib/queries/profileQueries.ts(320,17): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"parent_child"' is not assignable to parameter of type '"setlists" | "users" | "achievements" | "affiliations" | "assets" | "availability_settings" | "beta_signups" | "brand_category_mappings" | "child_access_codes" | "child_activities" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"parent_child"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/profileQueries.ts(335,58): error TS2339: Property 'child_id' does not exist on type 'SelectQueryError<"column 'child_id' does not exist on 'setlists'."> | SelectQueryError<"column 'child_id' does not exist on 'users'."> | ... 71 more ... | SelectQueryError<...>'.
  Property 'child_id' does not exist on type 'SelectQueryError<"column 'child_id' does not exist on 'setlists'.">'.
lib/queries/receiptQueries.ts(25,10): error TS2352: Conversion of type '{ created_at: string | null; data: Json; id: string; updated_at: string | null; }[]' to type 'Receipt[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ created_at: string | null; data: Json; id: string; updated_at: string | null; }' is missing the following properties from type 'Receipt': profile_id, merchant, date, total, category
lib/queries/receiptQueries.ts(45,10): error TS2352: Conversion of type '{ created_at: string | null; data: Json; id: string; updated_at: string | null; }' to type 'Receipt' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ created_at: string | null; data: Json; id: string; updated_at: string | null; }' is missing the following properties from type 'Receipt': profile_id, merchant, date, total, category
lib/queries/receiptQueries.ts(50,33): error TS2589: Type instantiation is excessively deep and possibly infinite.
lib/queries/receiptQueries.ts(51,11): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "gear_brands" | "profiles" | "children" | "child_events" | "practice_sessions" | "child_login_requests" | "parent_child_links" | "user_instruments" | "achievements" | ... 64 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"receipt_items"' is not assignable to parameter of type '"gear_brands" | "profiles" | "children" | "child_events" | "practice_sessions" | "child_login_requests" | "parent_child_links" | "user_instruments" | "achievements" | "affiliations" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"receipt_items"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/receiptQueries.ts(81,6): error TS2769: No overload matches this call.
  Overload 1 of 2, '(values: { created_at?: string | null | undefined; data: Json; id: string; updated_at?: string | null | undefined; }, options?: { count?: "exact" | "planned" | "estimated" | undefined; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Argument of type '(ReceiptFormData & { profile_id: string; })[]' is not assignable to parameter of type '{ created_at?: string | null | undefined; data: Json; id: string; updated_at?: string | null | undefined; }'.
      Type '(ReceiptFormData & { profile_id: string; })[]' is missing the following properties from type '{ created_at?: string | null | undefined; data: Json; id: string; updated_at?: string | null | undefined; }': data, id
  Overload 2 of 2, '(values: { created_at?: string | null | undefined; data: Json; id: string; updated_at?: string | null | undefined; }[], options?: { count?: "exact" | "planned" | "estimated" | undefined; defaultToNull?: boolean | undefined; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Type 'ReceiptFormData & { profile_id: string; }' is missing the following properties from type '{ created_at?: string | null | undefined; data: Json; id: string; updated_at?: string | null | undefined; }': data, id
lib/queries/receiptQueries.ts(86,10): error TS2352: Conversion of type '{ created_at: string | null; data: Json; id: string; updated_at: string | null; }' to type 'Receipt' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ created_at: string | null; data: Json; id: string; updated_at: string | null; }' is missing the following properties from type 'Receipt': profile_id, merchant, date, total, category
lib/queries/receiptQueries.ts(96,13): error TS2559: Type 'Partial<ReceiptFormData>' has no properties in common with type '{ created_at?: string | null | undefined; data?: Json | undefined; id?: string | undefined; updated_at?: string | null | undefined; }'.
lib/queries/receiptQueries.ts(102,10): error TS2352: Conversion of type '{ created_at: string | null; data: Json; id: string; updated_at: string | null; }' to type 'Receipt' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ created_at: string | null; data: Json; id: string; updated_at: string | null; }' is missing the following properties from type 'Receipt': profile_id, merchant, date, total, category
lib/queries/receiptQueries.ts(121,11): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "gear_brands" | "profiles" | "children" | "child_events" | "practice_sessions" | "child_login_requests" | "parent_child_links" | "user_instruments" | "achievements" | ... 64 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"receipt_items"' is not assignable to parameter of type '"gear_brands" | "profiles" | "children" | "child_events" | "practice_sessions" | "child_login_requests" | "parent_child_links" | "user_instruments" | "achievements" | "affiliations" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"receipt_items"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/receiptQueries.ts(136,11): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "gear_brands" | "profiles" | "children" | "child_events" | "practice_sessions" | "child_login_requests" | "parent_child_links" | "user_instruments" | "achievements" | ... 64 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"receipt_items"' is not assignable to parameter of type '"gear_brands" | "profiles" | "children" | "child_events" | "practice_sessions" | "child_login_requests" | "parent_child_links" | "user_instruments" | "achievements" | "affiliations" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"receipt_items"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/receiptQueries.ts(149,11): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "gear_brands" | "profiles" | "children" | "child_events" | "practice_sessions" | "child_login_requests" | "parent_child_links" | "user_instruments" | "achievements" | ... 64 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"receipt_items"' is not assignable to parameter of type '"gear_brands" | "profiles" | "children" | "child_events" | "practice_sessions" | "child_login_requests" | "parent_child_links" | "user_instruments" | "achievements" | "affiliations" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"receipt_items"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/receiptQueries.ts(165,6): error TS2769: No overload matches this call.
  Overload 1 of 2, '(values: { created_at?: string | null | undefined; data: Json; id: string; updated_at?: string | null | undefined; }, options?: { count?: "exact" | "planned" | "estimated" | undefined; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Argument of type '(ReceiptFormData & { profile_id: string; })[]' is not assignable to parameter of type '{ created_at?: string | null | undefined; data: Json; id: string; updated_at?: string | null | undefined; }'.
      Type '(ReceiptFormData & { profile_id: string; })[]' is missing the following properties from type '{ created_at?: string | null | undefined; data: Json; id: string; updated_at?: string | null | undefined; }': data, id
  Overload 2 of 2, '(values: { created_at?: string | null | undefined; data: Json; id: string; updated_at?: string | null | undefined; }[], options?: { count?: "exact" | "planned" | "estimated" | undefined; defaultToNull?: boolean | undefined; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Type 'ReceiptFormData & { profile_id: string; }' is missing the following properties from type '{ created_at?: string | null | undefined; data: Json; id: string; updated_at?: string | null | undefined; }': data, id
lib/queries/receiptQueries.ts(179,13): error TS2769: No overload matches this call.
  Overload 1 of 2, '(relation: "gear_brands" | "profiles" | "children" | "child_events" | "practice_sessions" | "child_login_requests" | "parent_child_links" | "user_instruments" | "achievements" | ... 64 more ... | "venues"): PostgrestQueryBuilder<...>', gave the following error.
    Argument of type '"receipt_items"' is not assignable to parameter of type '"gear_brands" | "profiles" | "children" | "child_events" | "practice_sessions" | "child_login_requests" | "parent_child_links" | "user_instruments" | "achievements" | "affiliations" | ... 63 more ... | "venues"'.
  Overload 2 of 2, '(relation: "beta_signups_view"): PostgrestQueryBuilder<{ Tables: { achievements: { Row: { achievement_type: string; created_at: string | null; date_achieved: string | null; description: string | null; ... 11 more ...; url: string | null; }; Insert: { ...; }; Update: { ...; }; Relationships: [...]; }; ... 72 more ...; venues: { ...; }; }; Views: { ...; }; Functions: { ...; }; Enums: { ...; }; CompositeTypes: {}; }, { ...; }, "beta_signups_view", []>', gave the following error.
    Argument of type '"receipt_items"' is not assignable to parameter of type '"beta_signups_view"'.
lib/queries/rpcQueries.ts(23,5): error TS2353: Object literal may only specify known properties, and 'profile_data' does not exist in type '{ user_id: string; user_full_name?: string | undefined; }'.
lib/queries/rpcQueries.ts(34,10): error TS2352: Conversion of type 'boolean' to type 'Profile' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
lib/queries/songVariantQueries.ts(127,7): error TS2698: Spread types may only be created from object types.
lib/queries/unifiedInstrumentQueries.ts(182,69): error TS2339: Property 'parent_id' does not exist on type 'SelectQueryError<"column 'parent_id' does not exist on 'children'.">'.
lib/queries/unifiedInstrumentQueries.ts(184,38): error TS2339: Property 'parent_id' does not exist on type 'SelectQueryError<"column 'parent_id' does not exist on 'children'.">'.
lib/queries/unifiedInstrumentQueries.ts(229,9): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.
  No index signature with a parameter of type 'string' was found on type '{}'.
lib/queries/unifiedInstrumentQueries.ts(237,21): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.
  No index signature with a parameter of type 'string' was found on type '{}'.
lib/queries/unifiedInstrumentQueries.ts(560,38): error TS2339: Property 'parent_id' does not exist on type 'SelectQueryError<"column 'parent_id' does not exist on 'children'.">'.
lib/queries/unifiedInstrumentQueries.ts(563,38): error TS2339: Property 'parent_id' does not exist on type 'SelectQueryError<"column 'parent_id' does not exist on 'children'.">'.
lib/queries/unifiedInstrumentQueries.ts(722,13): error TS2698: Spread types may only be created from object types.
lib/queries/unifiedInstrumentQueries.ts(763,69): error TS2339: Property 'parent_id' does not exist on type 'SelectQueryError<"column 'parent_id' does not exist on 'children'.">'.
lib/queries/unifiedInstrumentQueries.ts(765,38): error TS2339: Property 'parent_id' does not exist on type 'SelectQueryError<"column 'parent_id' does not exist on 'children'.">'.
lib/queries/unifiedInstrumentQueries.ts(869,69): error TS2339: Property 'parent_id' does not exist on type 'SelectQueryError<"column 'parent_id' does not exist on 'children'.">'.
lib/queries/unifiedInstrumentQueries.ts(871,38): error TS2339: Property 'parent_id' does not exist on type 'SelectQueryError<"column 'parent_id' does not exist on 'children'.">'.
lib/queries/unifiedPracticeQueries.ts(211,12): error TS2352: Conversion of type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }[]' to type 'PracticeSession[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }' is not comparable to type 'PracticeSession'.
    Types of property 'song' are incompatible.
      Type 'SelectQueryError<"could not find the relation between practice_sessions and song_id">' is not comparable to type 'Song | null | undefined'.
lib/queries/unifiedPracticeQueries.ts(268,12): error TS2352: Conversion of type '{ child_id: string | null; created_at: string | null; duration: number; end_time: string | null; id: string; instrument_id: string | null; lifetime_minutes: number | null; metadata: Json; ... 8 more ...; song: SelectQueryError<...>; }' to type 'PracticeSession' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Types of property 'song' are incompatible.
    Type 'SelectQueryError<"could not find the relation between practice_sessions and song_id">' is not comparable to type 'Song | null | undefined'.
lib/queries/unifiedPracticeQueries.ts(361,9): error TS2698: Spread types may only be created from object types.
lib/queries/unifiedPracticeQueries.ts(521,27): error TS2339: Property 'self_rating' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'self_rating' does not exist on type 'string'.
lib/queries/unifiedPracticeQueries.ts(522,27): error TS2339: Property 'self_rating' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'self_rating' does not exist on type 'string'.
lib/queries/unifiedPracticeQueries.ts(527,56): error TS2339: Property 'self_rating' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'self_rating' does not exist on type 'string'.
lib/queries/unifiedPracticeQueries.ts(534,27): error TS2339: Property 'focus_rating' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'focus_rating' does not exist on type 'string'.
lib/queries/unifiedPracticeQueries.ts(535,27): error TS2339: Property 'focus_rating' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'focus_rating' does not exist on type 'string'.
lib/queries/unifiedPracticeQueries.ts(540,56): error TS2339: Property 'focus_rating' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'focus_rating' does not exist on type 'string'.
lib/queries/unifiedPracticeQueries.ts(547,23): error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'string'.
  Type 'null' is not assignable to type 'string'.
lib/queries/unifiedPracticeQueries.ts(607,29): error TS2769: No overload matches this call.
  Overload 1 of 4, '(value: string | number | Date): Date', gave the following error.
    Argument of type 'string | null' is not assignable to parameter of type 'string | number | Date'.
      Type 'null' is not assignable to type 'string | number | Date'.
  Overload 2 of 4, '(value: string | number): Date', gave the following error.
    Argument of type 'string | null' is not assignable to parameter of type 'string | number'.
      Type 'null' is not assignable to type 'string | number'.
lib/queries/unifiedPracticeQueries.ts(611,37): error TS2769: No overload matches this call.
  Overload 1 of 4, '(value: string | number | Date): Date', gave the following error.
    Argument of type 'string | null' is not assignable to parameter of type 'string | number | Date'.
      Type 'null' is not assignable to type 'string | number | Date'.
  Overload 2 of 4, '(value: string | number): Date', gave the following error.
    Argument of type 'string | null' is not assignable to parameter of type 'string | number'.
      Type 'null' is not assignable to type 'string | number'.
lib/queries/unifiedPracticeQueries.ts(639,34): error TS2339: Property 'song_id' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'song_id' does not exist on type 'string'.
lib/queries/unifiedPracticeQueries.ts(640,48): error TS2339: Property 'song_id' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'song_id' does not exist on type 'string'.
lib/queries/unifiedPracticeQueries.ts(644,38): error TS2339: Property 'song_title' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'song_title' does not exist on type 'string'.
lib/queries/unifiedPracticeQueries.ts(645,34): error TS2339: Property 'song_id' does not exist on type 'string | number | boolean | { [key: string]: Json | undefined; } | Json[]'.
  Property 'song_id' does not exist on type 'string'.
lib/queries/userRoleQueries.ts(144,12): error TS7053: Element implicitly has an 'any' type because expression of type 'UserRoleType' can't be used to index type '{ admin: boolean | null; } | { teacher: boolean | null; } | { parent: boolean | null; } | { performer: boolean | null; } | { student: boolean | null; }'.
  Property 'teacher' does not exist on type '{ admin: boolean | null; } | { teacher: boolean | null; } | { parent: boolean | null; } | { performer: boolean | null; } | { student: boolean | null; }'.
lib/services/auth-service.ts(50,5): error TS2322: Type '{ avatar_url: string | null; created_at: string | null; email: string; id: string; is_admin: boolean | null; name: string | null; privacy_accepted_at: string | null; privacy_version: string | null; terms_accepted_at: string | null; terms_version: string | null; updated_at: string | null; } | null' is not assignable to type 'User | null'.
  Type '{ avatar_url: string | null; created_at: string | null; email: string; id: string; is_admin: boolean | null; name: string | null; privacy_accepted_at: string | null; privacy_version: string | null; terms_accepted_at: string | null; terms_version: string | null; updated_at: string | null; }' is missing the following properties from type 'User': app_metadata, user_metadata, aud
lib/services/billing-notifications.ts(70,13): error TS2352: Conversion of type '{ category: string; created_at: string | null; id: string; link: string | null; message: string; metadata: Json; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }' to type 'BillingNotification' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'data' is missing in type '{ category: string; created_at: string | null; id: string; link: string | null; message: string; metadata: Json; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }' but required in type 'BillingNotification'.
lib/services/billing-notifications.ts(105,13): error TS2352: Conversion of type '{ category: string; created_at: string | null; id: string; link: string | null; message: string; metadata: Json; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }[]' to type 'BillingNotification[]' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'data' is missing in type '{ category: string; created_at: string | null; id: string; link: string | null; message: string; metadata: Json; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }' but required in type 'BillingNotification'.
lib/services/billing-notifications.ts(141,13): error TS2352: Conversion of type '{ category: string; created_at: string | null; id: string; link: string | null; message: string; metadata: Json; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }' to type 'BillingNotification' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Property 'data' is missing in type '{ category: string; created_at: string | null; id: string; link: string | null; message: string; metadata: Json; read: boolean | null; title: string; type: string; updated_at: string | null; user_id: string; }' but required in type 'BillingNotification'.
