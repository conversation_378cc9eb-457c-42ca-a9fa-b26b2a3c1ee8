// Google Maps API type declarations
declare namespace google {
  namespace maps {
    class Map {
      constructor(mapDiv: Element | null, opts?: MapOptions);
      setCenter(latlng: LatLng | LatLngLiteral): void;
      setZoom(zoom: number): void;
      getCenter(): LatLng;
      getZoom(): number;
      panTo(latLng: LatLng | LatLngLiteral): void;
      fitBounds(bounds: LatLngBounds | LatLngBoundsLiteral): void;
      getBounds(): LatLngBounds | undefined;
      getDiv(): Element;
      setOptions(options: MapOptions): void;
      addListener(eventName: string, handler: Function): MapsEventListener;
    }

    interface MapOptions {
      center?: LatLng | LatLngLiteral;
      zoom?: number;
      mapTypeId?: MapTypeId;
      disableDefaultUI?: boolean;
      zoomControl?: boolean;
      mapTypeControl?: boolean;
      scaleControl?: boolean;
      streetViewControl?: boolean;
      rotateControl?: boolean;
      fullscreenControl?: boolean;
      gestureHandling?: string;
      styles?: MapTypeStyle[];
    }

    interface MapTypeStyle {
      elementType?: string;
      featureType?: string;
      stylers?: any[];
    }

    enum MapTypeId {
      HYBRID = 'hybrid',
      ROADMAP = 'roadmap',
      SATELLITE = 'satellite',
      TERRAIN = 'terrain'
    }

    class LatLng {
      constructor(lat: number, lng: number);
      lat(): number;
      lng(): number;
      equals(other: LatLng): boolean;
      toString(): string;
    }

    interface LatLngLiteral {
      lat: number;
      lng: number;
    }

    class LatLngBounds {
      constructor(sw?: LatLng, ne?: LatLng);
      contains(latLng: LatLng): boolean;
      extend(point: LatLng): LatLngBounds;
      getCenter(): LatLng;
      getNorthEast(): LatLng;
      getSouthWest(): LatLng;
      intersects(other: LatLngBounds): boolean;
      isEmpty(): boolean;
      toJSON(): LatLngBoundsLiteral;
      toString(): string;
      union(other: LatLngBounds): LatLngBounds;
    }

    interface LatLngBoundsLiteral {
      east: number;
      north: number;
      south: number;
      west: number;
    }

    class Marker {
      constructor(opts?: MarkerOptions);
      getPosition(): LatLng | undefined;
      setPosition(latlng: LatLng | LatLngLiteral | null): void;
      setMap(map: Map | null): void;
      getMap(): Map | null;
      setVisible(visible: boolean): void;
      getVisible(): boolean;
      setDraggable(draggable: boolean): void;
      getDraggable(): boolean;
      setTitle(title: string): void;
      getTitle(): string;
      addListener(eventName: string, handler: Function): MapsEventListener;
    }

    interface MarkerOptions {
      position?: LatLng | LatLngLiteral;
      map?: Map;
      title?: string;
      icon?: string | Icon | Symbol;
      draggable?: boolean;
      visible?: boolean;
      clickable?: boolean;
      cursor?: string;
      label?: string | MarkerLabel;
      opacity?: number;
      optimized?: boolean;
      zIndex?: number;
    }

    interface Icon {
      url: string;
      size?: Size;
      origin?: Point;
      anchor?: Point;
      scaledSize?: Size;
    }

    interface Symbol {
      path: string | SymbolPath;
      anchor?: Point;
      fillColor?: string;
      fillOpacity?: number;
      labelOrigin?: Point;
      rotation?: number;
      scale?: number;
      strokeColor?: string;
      strokeOpacity?: number;
      strokeWeight?: number;
    }

    enum SymbolPath {
      BACKWARD_CLOSED_ARROW = 0,
      BACKWARD_OPEN_ARROW = 1,
      CIRCLE = 2,
      FORWARD_CLOSED_ARROW = 3,
      FORWARD_OPEN_ARROW = 4
    }

    interface MarkerLabel {
      text: string;
      color?: string;
      fontFamily?: string;
      fontSize?: string;
      fontWeight?: string;
    }

    class Size {
      constructor(width: number, height: number, widthUnit?: string, heightUnit?: string);
      width: number;
      height: number;
    }

    class Point {
      constructor(x: number, y: number);
      x: number;
      y: number;
    }

    interface MapsEventListener {
      remove(): void;
    }

    class InfoWindow {
      constructor(opts?: InfoWindowOptions);
      close(): void;
      getContent(): string | Element | Text | null;
      getPosition(): LatLng | undefined;
      getZIndex(): number;
      open(opts?: InfoWindowOpenOptions): void;
      setContent(content: string | Element | Text | null): void;
      setOptions(options: InfoWindowOptions): void;
      setPosition(position: LatLng | LatLngLiteral | null): void;
      setZIndex(zIndex: number): void;
      addListener(eventName: string, handler: Function): MapsEventListener;
    }

    interface InfoWindowOptions {
      content?: string | Element | Text;
      disableAutoPan?: boolean;
      maxWidth?: number;
      pixelOffset?: Size;
      position?: LatLng | LatLngLiteral;
      zIndex?: number;
    }

    interface InfoWindowOpenOptions {
      map?: Map;
      anchor?: MVCObject;
      shouldFocus?: boolean;
    }

    class MVCObject {
      addListener(eventName: string, handler: Function): MapsEventListener;
      bindTo(key: string, target: MVCObject, targetKey?: string, noNotify?: boolean): void;
      get(key: string): any;
      notify(key: string): void;
      set(key: string, value: any): void;
      setValues(values: any): void;
      unbind(key: string): void;
      unbindAll(): void;
    }

    namespace places {
      class AutocompleteService {
        constructor();
        getPlacePredictions(
          request: AutocompletionRequest,
          callback: (predictions: AutocompletePrediction[] | null, status: PlacesServiceStatus) => void
        ): void;
        getQueryPredictions(
          request: QueryAutocompletionRequest,
          callback: (predictions: QueryAutocompletePrediction[] | null, status: PlacesServiceStatus) => void
        ): void;
      }

      class PlacesService {
        constructor(attrContainer: HTMLDivElement | Map);
        findPlaceFromPhoneNumber(
          request: FindPlaceFromPhoneNumberRequest,
          callback: (results: PlaceResult[] | null, status: PlacesServiceStatus) => void
        ): void;
        findPlaceFromQuery(
          request: FindPlaceFromQueryRequest,
          callback: (results: PlaceResult[] | null, status: PlacesServiceStatus) => void
        ): void;
        getDetails(
          request: PlaceDetailsRequest,
          callback: (result: PlaceResult | null, status: PlacesServiceStatus) => void
        ): void;
        nearbySearch(
          request: PlaceSearchRequest,
          callback: (results: PlaceResult[] | null, status: PlacesServiceStatus) => void
        ): void;
        textSearch(
          request: TextSearchRequest,
          callback: (results: PlaceResult[] | null, status: PlacesServiceStatus) => void
        ): void;
      }

      interface AutocompletionRequest {
        input: string;
        bounds?: LatLngBounds | LatLngBoundsLiteral;
        componentRestrictions?: ComponentRestrictions;
        location?: LatLng;
        offset?: number;
        origin?: LatLng | LatLngLiteral;
        radius?: number;
        region?: string;
        sessionToken?: AutocompleteSessionToken;
        types?: string[];
      }

      interface QueryAutocompletionRequest {
        input: string;
        bounds?: LatLngBounds | LatLngBoundsLiteral;
        location?: LatLng;
        offset?: number;
        radius?: number;
      }

      interface AutocompletePrediction {
        description: string;
        distance_meters?: number;
        matched_substrings: PredictionSubstring[];
        place_id: string;
        reference: string;
        structured_formatting: AutocompleteStructuredFormatting;
        terms: PredictionTerm[];
        types: string[];
      }

      interface QueryAutocompletePrediction {
        description: string;
        matched_substrings: PredictionSubstring[];
        place_id: string;
        terms: PredictionTerm[];
      }

      interface PredictionSubstring {
        length: number;
        offset: number;
      }

      interface AutocompleteStructuredFormatting {
        main_text: string;
        main_text_matched_substrings?: PredictionSubstring[];
        secondary_text?: string;
        secondary_text_matched_substrings?: PredictionSubstring[];
      }

      interface PredictionTerm {
        offset: number;
        value: string;
      }

      interface ComponentRestrictions {
        country: string | string[];
      }

      class AutocompleteSessionToken {
        constructor();
      }

      interface PlaceDetailsRequest {
        placeId: string;
        fields?: string[];
        language?: string;
        region?: string;
        sessionToken?: AutocompleteSessionToken;
      }

      interface FindPlaceFromPhoneNumberRequest {
        phoneNumber: string;
        fields: string[];
        locationBias?: LocationBias;
        language?: string;
      }

      interface FindPlaceFromQueryRequest {
        query: string;
        fields: string[];
        locationBias?: LocationBias;
        language?: string;
      }

      interface PlaceSearchRequest {
        bounds?: LatLngBounds | LatLngBoundsLiteral;
        keyword?: string;
        language?: string;
        location?: LatLng | LatLngLiteral;
        maxPriceLevel?: number;
        minPriceLevel?: number;
        name?: string;
        openNow?: boolean;
        radius?: number;
        rankBy?: RankBy;
        type?: string;
        types?: string[];
      }

      interface TextSearchRequest {
        query: string;
        bounds?: LatLngBounds | LatLngBoundsLiteral;
        language?: string;
        location?: LatLng | LatLngLiteral;
        radius?: number;
        type?: string;
        types?: string[];
      }

      type LocationBias = LatLng | LatLngLiteral | LatLngBounds | LatLngBoundsLiteral | Circle | string;

      interface Circle {
        center: LatLng | LatLngLiteral;
        radius: number;
      }

      enum RankBy {
        DISTANCE = 0,
        PROMINENCE = 1
      }

      interface PlaceResult {
        address_components?: GeocoderAddressComponent[];
        adr_address?: string;
        aspects?: PlaceAspectRating[];
        business_status?: BusinessStatus;
        formatted_address?: string;
        formatted_phone_number?: string;
        geometry?: PlaceGeometry;
        html_attributions?: string[];
        icon?: string;
        icon_background_color?: string;
        icon_mask_base_uri?: string;
        international_phone_number?: string;
        name?: string;
        opening_hours?: PlaceOpeningHours;
        permanently_closed?: boolean;
        photos?: PlacePhoto[];
        place_id?: string;
        plus_code?: PlacePlusCode;
        price_level?: number;
        rating?: number;
        reviews?: PlaceReview[];
        types?: string[];
        url?: string;
        user_ratings_total?: number;
        utc_offset_minutes?: number;
        vicinity?: string;
        website?: string;
      }

      interface GeocoderAddressComponent {
        long_name: string;
        short_name: string;
        types: string[];
      }

      interface PlaceAspectRating {
        rating: number;
        type: string;
      }

      enum BusinessStatus {
        CLOSED_PERMANENTLY = 'CLOSED_PERMANENTLY',
        CLOSED_TEMPORARILY = 'CLOSED_TEMPORARILY',
        OPERATIONAL = 'OPERATIONAL'
      }

      interface PlaceGeometry {
        location: LatLng;
        viewport: LatLngBounds;
      }

      interface PlaceOpeningHours {
        open_now?: boolean;
        periods?: PlaceOpeningHoursPeriod[];
        weekday_text?: string[];
      }

      interface PlaceOpeningHoursPeriod {
        close?: PlaceOpeningHoursTime;
        open: PlaceOpeningHoursTime;
      }

      interface PlaceOpeningHoursTime {
        day: number;
        time: string;
      }

      interface PlacePhoto {
        height: number;
        html_attributions: string[];
        width: number;
        getUrl(opts?: PhotoOptions): string;
      }

      interface PhotoOptions {
        maxHeight?: number;
        maxWidth?: number;
      }

      interface PlacePlusCode {
        compound_code?: string;
        global_code: string;
      }

      interface PlaceReview {
        aspects?: PlaceAspectRating[];
        author_name: string;
        author_url?: string;
        language: string;
        profile_photo_url: string;
        rating: number;
        relative_time_description: string;
        text: string;
        time: number;
      }

      enum PlacesServiceStatus {
        INVALID_REQUEST = 'INVALID_REQUEST',
        NOT_FOUND = 'NOT_FOUND',
        OK = 'OK',
        OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
        REQUEST_DENIED = 'REQUEST_DENIED',
        UNKNOWN_ERROR = 'UNKNOWN_ERROR',
        ZERO_RESULTS = 'ZERO_RESULTS'
      }
    }

    namespace event {
      function addListener(instance: object, eventName: string, handler: Function): MapsEventListener;
      function addListenerOnce(instance: object, eventName: string, handler: Function): MapsEventListener;
      function clearInstanceListeners(instance: object): void;
      function clearListeners(instance: object, eventName: string): void;
      function removeListener(listener: MapsEventListener): void;
      function trigger(instance: object, eventName: string, ...args: any[]): void;
    }
  }
}

// Extend the Window interface to include Google Maps
declare global {
  interface Window {
    google?: typeof google;
    [key: string]: any;
  }
}
